<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>UI-TARS 风格测试</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
            background: #f9fafb;
            color: #1f2937;
            height: 100vh;
            overflow: hidden;
        }

        .app-container {
            display: flex;
            flex-direction: column;
            height: 100vh;
            background: white;
        }

        .main-panels {
            display: flex;
            flex: 1;
            overflow: hidden;
        }

        .conversation-panel {
            width: 320px;
            background: white;
            border-right: 1px solid #e5e7eb;
            display: flex;
            flex-direction: column;
        }

        .agent-panel {
            flex: 1;
            background: white;
            border-right: 1px solid #e5e7eb;
            display: flex;
            flex-direction: column;
        }

        .conversation-header {
            padding: 16px 20px;
            border-bottom: 1px solid #e5e7eb;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .conversation-title {
            font-size: 16px;
            font-weight: 600;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .conversation-title i {
            color: #2563eb;
            font-size: 18px;
        }

        .action-button {
            background: transparent;
            border: 1px solid #d1d5db;
            color: #6b7280;
            padding: 6px 12px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 12px;
            display: flex;
            align-items: center;
            gap: 4px;
            transition: all 0.2s ease;
        }

        .action-button:hover {
            background: #f9fafb;
            border-color: #2563eb;
            color: #2563eb;
        }

        .action-button.primary {
            background: #2563eb;
            color: white;
            border-color: #2563eb;
        }

        .conversation-content {
            flex: 1;
            overflow-y: auto;
            padding: 20px;
            background: #f9fafb;
        }

        .agent-step {
            background: white;
            border: 1px solid #e5e7eb;
            border-radius: 8px;
            margin-bottom: 12px;
            overflow: hidden;
            transition: box-shadow 0.2s;
        }

        .agent-step:hover {
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
        }

        .agent-step-header {
            padding: 12px 16px;
            background: #f9fafb;
            border-bottom: 1px solid #e5e7eb;
            display: flex;
            align-items: center;
            gap: 12px;
            cursor: pointer;
        }

        .step-number {
            width: 24px;
            height: 24px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            font-weight: 600;
            color: white;
            background: #10b981;
        }

        .step-title {
            flex: 1;
            font-size: 14px;
            font-weight: 500;
        }

        .step-status {
            font-size: 12px;
            color: #10b981;
        }

        .agent-step-content {
            padding: 16px;
            background: white;
        }

        .content-panel {
            width: 400px;
            background: white;
            display: flex;
            flex-direction: column;
        }

        .content-header {
            padding: 16px 20px;
            border-bottom: 1px solid #e5e7eb;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .content-title {
            font-size: 16px;
            font-weight: 600;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .content-title i {
            color: #2563eb;
            font-size: 18px;
        }

        .content-area {
            flex: 1;
            overflow-y: auto;
            padding: 20px;
            background: #f9fafb;
        }

        .result-container {
            background: white;
            border: 1px solid #e5e7eb;
            border-radius: 8px;
            margin-bottom: 16px;
            overflow: hidden;
        }

        .result-header {
            padding: 12px 16px;
            background: #f9fafb;
            border-bottom: 1px solid #e5e7eb;
            font-size: 14px;
            font-weight: 500;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .result-icon {
            width: 20px;
            height: 20px;
            border-radius: 4px;
            background: #2563eb;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
        }

        .result-content {
            padding: 16px;
        }

        .input-panel {
            background: white;
            border-top: 1px solid #e5e7eb;
            padding: 16px 20px;
        }

        .input-container {
            display: flex;
            gap: 12px;
            align-items: flex-end;
        }

        .message-input-wrapper {
            flex: 1;
        }

        .message-input {
            width: 100%;
            min-height: 40px;
            padding: 10px 16px;
            border: 1px solid #d1d5db;
            border-radius: 8px;
            resize: none;
            font-family: inherit;
            font-size: 14px;
            transition: border-color 0.2s;
        }

        .message-input:focus {
            outline: none;
            border-color: #2563eb;
            box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
        }

        .send-button {
            background: #2563eb;
            color: white;
            border: none;
            padding: 10px 16px;
            border-radius: 8px;
            cursor: pointer;
            display: flex;
            align-items: center;
            gap: 6px;
            font-weight: 500;
            font-size: 14px;
            transition: background 0.2s;
        }

        .send-button:hover {
            background: #1d4ed8;
        }

        .data-table {
            width: 100%;
            border-collapse: collapse;
            font-size: 13px;
        }

        .data-table th {
            background: #f9fafb;
            padding: 8px 12px;
            text-align: left;
            font-weight: 500;
            border-bottom: 1px solid #e5e7eb;
        }

        .data-table td {
            padding: 8px 12px;
            border-bottom: 1px solid #f3f4f6;
        }

        .data-table tbody tr:hover {
            background: #f9fafb;
        }
    </style>
</head>
<body>
    <div class="app-container">
        <div class="main-panels">
            <!-- 左侧对话流程面板 -->
            <div class="conversation-panel">
                <div class="conversation-header">
                    <div class="conversation-title">
                        <i class="bi bi-chat-dots"></i>
                        <span>ChatBI Agent</span>
                    </div>
                    <div style="display: flex; gap: 8px;">
                        <button class="action-button primary">
                            <i class="bi bi-plus"></i>
                            新对话
                        </button>
                        <button class="action-button">
                            <i class="bi bi-trash"></i>
                            清空
                        </button>
                    </div>
                </div>

                <div class="conversation-content">
                    <div class="agent-step">
                        <div class="agent-step-header">
                            <div class="step-number">1</div>
                            <div class="step-title">SQL 查询分析</div>
                            <div class="step-status">已完成</div>
                            <i class="bi bi-chevron-right"></i>
                        </div>
                        <div class="agent-step-content">
                            <div style="font-size: 13px; color: #6b7280; margin-bottom: 12px;">
                                分析用户需求并生成相应的 SQL 查询语句
                            </div>
                        </div>
                    </div>

                    <div class="agent-step">
                        <div class="agent-step-header">
                            <div class="step-number">2</div>
                            <div class="step-title">数据查询执行</div>
                            <div class="step-status">已完成</div>
                            <i class="bi bi-chevron-right"></i>
                        </div>
                        <div class="agent-step-content">
                            <div style="font-size: 13px; color: #6b7280; margin-bottom: 12px;">
                                执行生成的 SQL 查询并获取结果数据
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 右侧内容展示面板 -->
            <div class="content-panel">
                <div class="content-header">
                    <div class="content-title">
                        <i class="bi bi-display"></i>
                        <span>执行结果</span>
                    </div>
                    <button class="action-button">
                        <i class="bi bi-bug"></i>
                        调试
                    </button>
                </div>

                <div class="content-area">
                    <div class="result-container">
                        <div class="result-header">
                            <div class="result-icon">
                                <i class="bi bi-table"></i>
                            </div>
                            查询结果
                        </div>
                        <div class="result-content">
                            <table class="data-table">
                                <thead>
                                    <tr>
                                        <th>地区</th>
                                        <th>销售额</th>
                                        <th>增长率</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td>华东</td>
                                        <td>¥1,250,000</td>
                                        <td>+12.5%</td>
                                    </tr>
                                    <tr>
                                        <td>华南</td>
                                        <td>¥980,000</td>
                                        <td>+8.3%</td>
                                    </tr>
                                    <tr>
                                        <td>华北</td>
                                        <td>¥750,000</td>
                                        <td>+5.7%</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 底部输入面板 -->
        <div class="input-panel">
            <div class="input-container">
                <div class="message-input-wrapper">
                    <textarea class="message-input" placeholder="输入您的数据分析需求..." rows="1"></textarea>
                </div>
                <button class="send-button">
                    <i class="bi bi-send"></i>
                    发送
                </button>
            </div>
        </div>
    </div>
</body>
</html>
