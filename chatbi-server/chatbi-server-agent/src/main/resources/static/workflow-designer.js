// 简约智能体编排器
let allAgents = [];
let agentSequence = [];
let sequenceCounter = 1;
let currentWorkflowId = null;
let editMode = false;

/**
 * 处理R类统一响应格式
 * @param {Response} response - 响应对象
 * @returns {Promise<any>} - 处理后的数据
 */
async function handleRResponse(response) {
    if (!response.ok) {
        throw new Error(`API请求失败: ${response.status}`);
    }

    const result = await response.json();

    // 检查R类响应格式
    if (result.code && result.code !== 1) {
        throw new Error(result.message || 'API请求失败');
    }

    // 返回data字段，如果没有data字段则返回整个结果（兼容旧格式）
    return result.data !== undefined ? result.data : result;
}

// DOM元素
let workflowTitle, workflowDescription, agentList, addAgentBtn;
let saveBtn, previewBtn, agentCount, stepCount;

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', async function() {
    console.log('智能体编排器初始化...');

    try {
        initializeElements();
        setupEventListeners();

        // 先加载智能体数据
        await loadInitialData();

        // 再检查编辑模式并加载编排数据
        await checkEditMode();

        // 最后渲染界面
        renderAgentSequence();
    } catch (error) {
        console.error('页面初始化失败:', error);
    }
});

function initializeElements() {
    // 表单元素
    workflowTitle = document.getElementById('workflowTitle');
    workflowDescription = document.getElementById('workflowDescription');
    agentList = document.getElementById('agentList');
    addAgentBtn = document.getElementById('addAgentBtn');

    // 统计元素
    agentCount = document.getElementById('agentCount');
    stepCount = document.getElementById('stepCount');

    // 按钮元素
    saveBtn = document.getElementById('saveBtn');
    previewBtn = document.getElementById('previewBtn');

    console.log('DOM元素初始化完成');
}

function setupEventListeners() {
    // 添加智能体按钮
    addAgentBtn.addEventListener('click', addNewAgent);

    // 保存和预览按钮
    saveBtn.addEventListener('click', saveWorkflow);
    previewBtn.addEventListener('click', previewWorkflow);

    console.log('事件监听器设置完成');
}

async function checkEditMode() {
    const urlParams = new URLSearchParams(window.location.search);
    const workflowId = urlParams.get('id');
    const mode = urlParams.get('mode');

    if (workflowId && (mode === 'edit' || mode === 'duplicate')) {
        currentWorkflowId = workflowId;
        editMode = mode === 'edit';

        // 更新页面标题
        const titleElement = document.querySelector('.app-title');
        if (titleElement) {
            titleElement.textContent = mode === 'edit' ? '编辑智能体编排' : '复制智能体编排';
        }

        // 更新保存按钮文本
        if (saveBtn) {
            saveBtn.innerHTML = `<i class="bi bi-check"></i> <span>${mode === 'edit' ? '更新编排' : '保存副本'}</span>`;
        }

        // 加载编排数据
        await loadWorkflowData(workflowId, mode === 'duplicate');
    }
}

async function loadInitialData() {
    try {
        const agentsData = await loadAgents();
        allAgents = agentsData || [];
        console.log('数据加载完成:', { agents: allAgents.length });
    } catch (error) {
        console.error('加载数据失败:', error);
        loadMockData();
    }
}

async function loadWorkflowData(workflowId, isDuplicate = false) {
    try {
        console.log('开始加载编排数据:', workflowId);

        const response = await fetch(`/api/plan-template/get?planId=${workflowId}`);
        const data = await handleRResponse(response);
        console.log('获取到的数据:', data);

        // 尝试解析description字段，它应该包含最新的计划JSON
        let planData;
        try {
            planData = JSON.parse(data.description);
            console.log('成功解析计划数据:', planData);
        } catch (parseError) {
            console.warn('解析description失败，尝试使用title作为基本信息:', parseError);
            console.log('原始description:', data.description);
            // 如果description不是JSON，使用基本信息
            planData = {
                title: data.title || '未命名编排',
                steps: []
            };
        }

        // 填充基本信息
        if (workflowTitle) {
            workflowTitle.value = isDuplicate ? `${planData.title} - 副本` : planData.title;
        }

        // 解析步骤数据
        if (planData.steps && planData.steps.length > 0) {
            agentSequence = planData.steps.map((step, index) => {
                const match = step.stepRequirement.match(/^\[([^\]]+)\]\s*(.*)$/);
                const agentName = match ? match[1] : '';
                const taskDescription = match ? match[2] : step.stepRequirement;

                // 查找对应的智能体ID
                const foundAgent = allAgents.find(a => a.name === agentName || a.id === agentName);
                const agentId = foundAgent ? foundAgent.id : agentName;

                console.log('解析智能体:', { agentName, foundAgent, agentId });

                return {
                    id: generateAgentId(),
                    agentId: agentId,
                    agentName: agentName,
                    description: foundAgent ? foundAgent.description : '',
                    taskDescription: taskDescription
                };
            });
        } else {
            agentSequence = [];
        }

        console.log('编排数据加载完成:', {
            title: planData.title,
            steps: agentSequence.length,
            agentSequence: agentSequence
        });

        // 重新渲染界面
        renderAgentSequence();

    } catch (error) {
        console.error('加载编排数据失败:', error);
        alert('加载编排数据失败: ' + error.message);
    }
}

async function loadAgents() {
    try {
        const response = await fetch('/api/agents');
        return await handleRResponse(response);
    } catch (error) {
        console.error('加载Agent列表失败:', error);
        throw error;
    }
}

function loadMockData() {
    allAgents = [
        {
            id: 'DEFAULT_AGENT',
            name: 'DEFAULT_AGENT',
            description: '一个通用的智能代理，可以使用各种工具来完成任务。它具有强大的推理能力，并能根据用户需求选择合适的工具。'
        },
        {
            id: 'TAVILY_AGENT',
            name: 'TAVILY_AGENT',
            description: '一个专门用于Tavily工具集成的智能代理。它能够使用Tavily的各种功能来处理特定的任务。'
        },
        {
            id: 'SQL_GENERATOR_AGENT',
            name: 'SQL_GENERATOR_AGENT',
            description: '专门用于生成SQL查询语句的智能代理，能够根据自然语言描述生成准确的SQL。'
        },
        {
            id: 'SQL_EXECUTION_INTERPRETER_AGENT',
            name: 'SQL_EXECUTION_INTERPRETER_AGENT',
            description: '专门用于执行SQL查询并解释结果的智能代理。'
        }
    ];
    console.log('使用模拟数据:', { agents: allAgents.length });
}

function addNewAgent() {
    const newAgent = {
        id: generateAgentId(),
        agentId: '',
        agentName: '',
        description: '',
        taskDescription: ''
    };

    agentSequence.push(newAgent);
    renderAgentSequence();
}

function generateAgentId() {
    return 'agent_' + (sequenceCounter++);
}

function renderAgentSequence() {
    updateStatistics();

    if (agentSequence.length === 0) {
        agentList.innerHTML = `
            <div class="agent-list-empty">
                <div class="empty-icon">
                    <i class="bi bi-robot"></i>
                </div>
                <h3 class="empty-title">开始创建您的智能体编排</h3>
                <p class="empty-description">添加智能体并配置它们的执行顺序，构建强大的自动化工作流</p>
            </div>
        `;
        return;
    }

    const agentsHtml = agentSequence.map((agent, index) => `
        <div class="agent-item" data-index="${index}">
            <div class="agent-content">
                <div class="drag-handle" title="拖拽排序">
                    <i class="bi bi-grip-vertical"></i>
                </div>
                <div class="agent-number">${index + 1}</div>
                <div class="agent-main">
                    <select class="agent-selector" onchange="handleAgentChange(${index}, this.value)">
                        <option value="">选择智能体类型...</option>
                        ${allAgents.map(a => `
                            <option value="${a.id}" ${agent.agentId === a.id ? 'selected' : ''}>
                                ${a.name}
                            </option>
                        `).join('')}
                    </select>
                    ${agent.agentId ? `
                        <div class="agent-info">
                            <div class="agent-name">${escapeHtml(agent.agentName)}</div>
                            <div class="agent-description">${escapeHtml(getAgentDescription(agent.agentId))}</div>
                        </div>
                    ` : ''}
                </div>
                <div class="agent-actions">
                    <button class="action-btn" onclick="duplicateAgent(${index})" title="复制">
                        <i class="bi bi-copy"></i>
                    </button>
                    <button class="action-btn danger" onclick="removeAgent(${index})" title="删除">
                        <i class="bi bi-trash"></i>
                    </button>
                </div>
            </div>
        </div>
    `).join('');

    agentList.innerHTML = agentsHtml;
    initializeSortable();
}

function updateStatistics() {
    const configuredAgents = agentSequence.filter(agent => agent.agentId).length;
    agentCount.textContent = agentSequence.length;
    stepCount.textContent = configuredAgents;
}

function initializeSortable() {
    if (window.sortableInstance) {
        window.sortableInstance.destroy();
    }

    window.sortableInstance = Sortable.create(agentList, {
        animation: 200,
        ghostClass: 'sortable-ghost',
        chosenClass: 'sortable-chosen',
        handle: '.drag-handle',
        onEnd: function(evt) {
            const oldIndex = evt.oldIndex;
            const newIndex = evt.newIndex;

            if (oldIndex !== newIndex) {
                // 重新排序数组
                const movedAgent = agentSequence.splice(oldIndex, 1)[0];
                agentSequence.splice(newIndex, 0, movedAgent);

                // 重新渲染
                renderAgentSequence();
            }
        }
    });
}

function handleAgentChange(index, agentId) {
    if (agentId) {
        const agent = allAgents.find(a => a.id === agentId);
        if (agent) {
            agentSequence[index].agentId = agentId;
            agentSequence[index].agentName = agent.name;
            agentSequence[index].taskDescription = `使用 ${agent.name} 处理任务`;
        }
    } else {
        agentSequence[index].agentId = '';
        agentSequence[index].agentName = '';
        agentSequence[index].taskDescription = '';
    }
    renderAgentSequence();
}

function getAgentDescription(agentId) {
    const agent = allAgents.find(a => a.id === agentId);
    return agent ? agent.description : '';
}

function moveAgent(index, direction) {
    const newIndex = index + direction;
    if (newIndex >= 0 && newIndex < agentSequence.length) {
        // 交换位置
        [agentSequence[index], agentSequence[newIndex]] = [agentSequence[newIndex], agentSequence[index]];
        renderAgentSequence();
    }
}

function removeAgent(index) {
    if (confirm('确定要删除这个智能体吗？')) {
        agentSequence.splice(index, 1);
        renderAgentSequence();
    }
}

function duplicateAgent(index) {
    const originalAgent = agentSequence[index];
    const duplicatedAgent = {
        id: generateAgentId(),
        agentId: originalAgent.agentId,
        agentName: originalAgent.agentName,
        description: originalAgent.description,
        taskDescription: originalAgent.taskDescription
    };

    agentSequence.splice(index + 1, 0, duplicatedAgent);
    renderAgentSequence();
    showSuccessMessage('智能体已复制');
}

async function saveWorkflow() {
    const title = workflowTitle.value.trim();

    if (!title) {
        alert('请输入编排标题');
        return;
    }

    if (agentSequence.length === 0) {
        alert('请至少添加一个智能体');
        return;
    }

    // 验证所有智能体都已配置
    for (let i = 0; i < agentSequence.length; i++) {
        const agent = agentSequence[i];
        if (!agent.agentId) {
            alert(`第 ${i + 1} 个智能体未选择类型，请检查`);
            return;
        }
    }

    try {
        saveBtn.disabled = true;
        saveBtn.innerHTML = '<i class="bi bi-hourglass-split"></i> 保存中...';

        const planData = {
            planId: editMode ? currentWorkflowId : generatePlanId(),
            title: title,
            steps: agentSequence.map(agent => ({
                stepRequirement: `[${agent.agentName}] ${agent.taskDescription}`
            }))
        };

        console.log('保存智能体编排数据:', planData);

        const response = await fetch('/api/plan-template/save', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                planId: planData.planId,
                planJson: JSON.stringify(planData)
            })
        });

        const result = await handleRResponse(response);
        console.log('保存成功:', result);

        showSuccessMessage(editMode ? '智能体编排更新成功！' : '智能体编排保存成功！');

        // 如果是新建模式，保存成功后跳转到列表页面
        if (!editMode) {
            setTimeout(() => {
                window.location.href = 'workflow-list.html';
            }, 1500);
        }

    } catch (error) {
        console.error('保存编排失败:', error);
        alert('保存失败: ' + error.message);
    } finally {
        saveBtn.disabled = false;
        saveBtn.innerHTML = '<i class="bi bi-check"></i> 保存编排';
    }
}

function generatePlanId() {
    return 'planTemplate-' + Date.now();
}

function previewWorkflow() {
    const title = workflowTitle.value.trim();

    if (!title) {
        alert('请输入编排标题');
        return;
    }

    if (agentSequence.length === 0) {
        alert('请至少添加一个智能体');
        return;
    }

    const planData = {
        planId: generatePlanId(),
        title: title,
        steps: agentSequence.map(agent => ({
            stepRequirement: `[${agent.agentName}] ${agent.taskDescription}`
        }))
    };

    // 在新窗口中显示预览
    const previewWindow = window.open('', '_blank', 'width=900,height=700');
    previewWindow.document.write(`
        <html>
        <head>
            <title>智能体编排预览 - ${escapeHtml(title)}</title>
            <style>
                body {
                    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
                    padding: 40px;
                    background: #f9fafb;
                    color: #374151;
                    line-height: 1.6;
                }
                .header {
                    background: white;
                    border-radius: 12px;
                    padding: 32px;
                    margin-bottom: 24px;
                    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
                    border: 1px solid #e5e7eb;
                }
                .header h1 {
                    color: #6366f1;
                    margin: 0 0 8px 0;
                    font-size: 2rem;
                    font-weight: 700;
                }
                .header h2 {
                    color: #1f2937;
                    margin: 0;
                    font-size: 1.5rem;
                    font-weight: 600;
                }
                .steps {
                    background: white;
                    border-radius: 12px;
                    padding: 32px;
                    margin-bottom: 24px;
                    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
                    border: 1px solid #e5e7eb;
                }
                .step {
                    display: flex;
                    align-items: flex-start;
                    gap: 16px;
                    margin: 20px 0;
                    padding: 20px;
                    border: 1px solid #e5e7eb;
                    border-radius: 8px;
                    background: #f9fafb;
                }
                .step-number {
                    width: 32px;
                    height: 32px;
                    background: #6366f1;
                    color: white;
                    border-radius: 50%;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    font-weight: 600;
                    font-size: 0.875rem;
                    flex-shrink: 0;
                }
                .step-content {
                    flex: 1;
                }
                .agent-name {
                    color: #059669;
                    font-weight: 600;
                    background: #d1fae5;
                    padding: 4px 8px;
                    border-radius: 4px;
                    display: inline-block;
                    margin-bottom: 8px;
                    font-size: 0.875rem;
                }
                .step-description {
                    color: #4b5563;
                    font-size: 0.95rem;
                    line-height: 1.5;
                }
                .json-section {
                    background: white;
                    border-radius: 12px;
                    padding: 32px;
                    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
                    border: 1px solid #e5e7eb;
                }
                .json-section h3 {
                    color: #1f2937;
                    margin: 0 0 16px 0;
                    font-size: 1.25rem;
                    font-weight: 600;
                }
                pre {
                    background: #1f2937;
                    color: #e5e7eb;
                    padding: 20px;
                    border-radius: 8px;
                    overflow-x: auto;
                    font-size: 0.875rem;
                    line-height: 1.5;
                }
            </style>
        </head>
        <body>
            <div class="header">
                <h1>智能体编排预览</h1>
                <h2>${escapeHtml(title)}</h2>
            </div>
            <div class="steps">
                <h3 style="margin: 0 0 20px 0; color: #1f2937; font-size: 1.25rem; font-weight: 600;">执行序列</h3>
                ${agentSequence.map((agent, index) => `
                    <div class="step">
                        <div class="step-number">${index + 1}</div>
                        <div class="step-content">
                            <div class="agent-name">${escapeHtml(agent.agentName)}</div>
                            <div class="step-description">${escapeHtml(agent.taskDescription)}</div>
                        </div>
                    </div>
                `).join('')}
            </div>
            <div class="json-section">
                <h3>生成的JSON数据</h3>
                <pre>${JSON.stringify(planData, null, 2)}</pre>
            </div>
        </body>
        </html>
    `);
}

function showSuccessMessage(message) {
    const toast = document.createElement('div');
    toast.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        z-index: 9999;
        background: var(--success);
        color: white;
        padding: 16px 20px;
        border-radius: var(--radius);
        box-shadow: var(--shadow-lg);
        display: flex;
        align-items: center;
        gap: 8px;
        font-weight: 500;
    `;
    toast.innerHTML = `<i class="bi bi-check-circle"></i> ${escapeHtml(message)}`;
    document.body.appendChild(toast);

    setTimeout(() => {
        toast.remove();
    }, 3000);
}

function escapeHtml(text) {
    if (!text) return '';
    const div = document.createElement('div');
    div.textContent = text;
    return div.innerHTML;
}
