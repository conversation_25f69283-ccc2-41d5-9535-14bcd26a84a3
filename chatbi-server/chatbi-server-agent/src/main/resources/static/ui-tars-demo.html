<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ChatBI - UI-TARS 风格演示</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
            background: #f9fafb;
            color: #1f2937;
            height: 100vh;
            overflow: hidden;
        }

        .demo-container {
            display: flex;
            flex-direction: column;
            height: 100vh;
            background: white;
        }

        .demo-header {
            padding: 16px 20px;
            border-bottom: 1px solid #e5e7eb;
            background: white;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .demo-title {
            font-size: 18px;
            font-weight: 600;
            color: #1f2937;
        }

        .demo-subtitle {
            font-size: 14px;
            color: #6b7280;
            margin-top: 4px;
        }

        .demo-actions {
            display: flex;
            gap: 12px;
        }

        .demo-button {
            background: #2563eb;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            display: flex;
            align-items: center;
            gap: 6px;
            transition: background 0.2s;
        }

        .demo-button:hover {
            background: #1d4ed8;
        }

        .demo-button.secondary {
            background: #6b7280;
        }

        .demo-button.secondary:hover {
            background: #4b5563;
        }

        .demo-content {
            flex: 1;
            display: flex;
            overflow: hidden;
        }

        .demo-left {
            width: 480px;
            background: white;
            border-right: 1px solid #e5e7eb;
            display: flex;
            flex-direction: column;
        }

        .demo-right {
            flex: 1;
            background: white;
            display: flex;
            flex-direction: column;
        }

        .panel-header {
            padding: 12px 16px;
            border-bottom: 1px solid #e5e7eb;
            background: #f9fafb;
            font-size: 14px;
            font-weight: 500;
            color: #374151;
        }

        .panel-content {
            flex: 1;
            overflow-y: auto;
            padding: 20px;
        }

        .step-item {
            background: white;
            border: 1px solid #e5e7eb;
            border-radius: 8px;
            margin-bottom: 12px;
            overflow: hidden;
            transition: box-shadow 0.2s;
        }

        .step-item:hover {
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
        }

        .step-header {
            padding: 12px 16px;
            background: #f9fafb;
            border-bottom: 1px solid #e5e7eb;
            display: flex;
            align-items: center;
            gap: 12px;
            cursor: pointer;
        }

        .step-number {
            width: 24px;
            height: 24px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            font-weight: 600;
            color: white;
            background: #10b981;
        }

        .step-title {
            flex: 1;
            font-size: 14px;
            font-weight: 500;
        }

        .step-status {
            font-size: 12px;
            color: #10b981;
        }

        .step-content {
            padding: 16px;
            background: white;
        }

        .step-description {
            font-size: 13px;
            color: #6b7280;
            margin-bottom: 12px;
        }

        .tool-execution {
            background: #f9fafb;
            border: 1px solid #e5e7eb;
            border-radius: 6px;
            margin-bottom: 12px;
            overflow: hidden;
        }

        .tool-header {
            padding: 8px 12px;
            background: #f3f4f6;
            border-bottom: 1px solid #e5e7eb;
            display: flex;
            align-items: center;
            gap: 8px;
            font-size: 12px;
            font-weight: 500;
        }

        .tool-icon {
            width: 16px;
            height: 16px;
            border-radius: 3px;
            background: #2563eb;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 10px;
        }

        .tool-content {
            padding: 12px;
            font-family: 'SF Mono', Monaco, monospace;
            font-size: 12px;
            line-height: 1.4;
            color: #374151;
        }

        .tool-result {
            background: #f0f9ff;
            border-left: 3px solid #2563eb;
            padding: 8px 12px;
            margin-top: 8px;
            border-radius: 0 4px 4px 0;
            font-size: 12px;
        }

        .result-container {
            background: white;
            border: 1px solid #e5e7eb;
            border-radius: 8px;
            margin-bottom: 16px;
            overflow: hidden;
        }

        .result-header {
            padding: 12px 16px;
            background: #f9fafb;
            border-bottom: 1px solid #e5e7eb;
            font-size: 14px;
            font-weight: 500;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .result-icon {
            width: 20px;
            height: 20px;
            border-radius: 4px;
            background: #2563eb;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
        }

        .result-content {
            padding: 16px;
        }

        .data-table {
            width: 100%;
            border-collapse: collapse;
            font-size: 13px;
        }

        .data-table th {
            background: #f9fafb;
            padding: 8px 12px;
            text-align: left;
            font-weight: 500;
            border-bottom: 1px solid #e5e7eb;
        }

        .data-table td {
            padding: 8px 12px;
            border-bottom: 1px solid #f3f4f6;
        }

        .data-table tbody tr:hover {
            background: #f9fafb;
        }
    </style>
</head>
<body>
    <div class="demo-container">
        <div class="demo-header">
            <div>
                <div class="demo-title">ChatBI - UI-TARS 风格界面演示</div>
                <div class="demo-subtitle">类似 UI-TARS-desktop 的 Agent 执行步骤展示</div>
            </div>
            <div class="demo-actions">
                <button class="demo-button" onclick="addDemoStep()">
                    <i class="bi bi-plus"></i>
                    添加步骤
                </button>
                <button class="demo-button secondary" onclick="clearDemo()">
                    <i class="bi bi-trash"></i>
                    清空
                </button>
            </div>
        </div>

        <div class="demo-content">
            <div class="demo-left">
                <div class="panel-header">
                    <i class="bi bi-list-task"></i>
                    Agent 执行步骤
                </div>
                <div class="panel-content" id="stepsContainer">
                    <!-- 步骤将在这里动态生成 -->
                </div>
            </div>

            <div class="demo-right">
                <div class="panel-header">
                    <i class="bi bi-display"></i>
                    执行结果
                </div>
                <div class="panel-content" id="resultsContainer">
                    <!-- 结果将在这里显示 -->
                </div>
            </div>
        </div>
    </div>

    <script>
        let stepCounter = 0;

        function addDemoStep() {
            stepCounter++;
            const stepsContainer = document.getElementById('stepsContainer');
            const resultsContainer = document.getElementById('resultsContainer');

            // 创建步骤
            const stepDiv = document.createElement('div');
            stepDiv.className = 'step-item';
            stepDiv.innerHTML = `
                <div class="step-header">
                    <div class="step-number">${stepCounter}</div>
                    <div class="step-title">SQL 查询分析</div>
                    <div class="step-status">已完成</div>
                    <i class="bi bi-chevron-right"></i>
                </div>
                <div class="step-content">
                    <div class="step-description">分析用户需求并生成相应的 SQL 查询语句</div>
                    <div class="tool-execution">
                        <div class="tool-header">
                            <div class="tool-icon">
                                <i class="bi bi-gear"></i>
                            </div>
                            <div>sql_query</div>
                            <div style="margin-left: auto; color: #10b981;">完成</div>
                        </div>
                        <div class="tool-content">
                            参数: {"sql": "SELECT region, SUM(sales) as total_sales FROM sales_data WHERE date >= '2024-01-01' GROUP BY region ORDER BY total_sales DESC"}
                            <div class="tool-result">
                                执行成功，返回 3 行数据
                            </div>
                        </div>
                    </div>
                </div>
            `;

            stepsContainer.appendChild(stepDiv);

            // 创建对应的结果
            const resultDiv = document.createElement('div');
            resultDiv.className = 'result-container';
            resultDiv.innerHTML = `
                <div class="result-header">
                    <div class="result-icon">
                        <i class="bi bi-table"></i>
                    </div>
                    查询结果 - 步骤 ${stepCounter}
                </div>
                <div class="result-content">
                    <table class="data-table">
                        <thead>
                            <tr>
                                <th>地区</th>
                                <th>总销售额</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>华东</td>
                                <td>¥1,250,000</td>
                            </tr>
                            <tr>
                                <td>华南</td>
                                <td>¥980,000</td>
                            </tr>
                            <tr>
                                <td>华北</td>
                                <td>¥750,000</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            `;

            resultsContainer.appendChild(resultDiv);

            // 滚动到底部
            stepDiv.scrollIntoView({ behavior: 'smooth' });
        }

        function clearDemo() {
            document.getElementById('stepsContainer').innerHTML = '';
            document.getElementById('resultsContainer').innerHTML = '';
            stepCounter = 0;
        }

        // 初始化演示
        addDemoStep();
    </script>
</body>
</html>
