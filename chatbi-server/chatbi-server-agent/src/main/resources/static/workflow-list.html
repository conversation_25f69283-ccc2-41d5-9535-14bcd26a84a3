<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>智能体编排管理 - Spring AI Alibaba</title>
    
    <!-- Bootstrap Icons -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.0/font/bootstrap-icons.css" rel="stylesheet">
    <!-- Inter Font -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <style>
        :root {
            /* 现代化配色系统 */
            --primary: #5b21b6;
            --primary-hover: #4c1d95;
            --primary-light: #f3e8ff;
            --primary-dark: #3b0764;
            
            --accent: #0ea5e9;
            --accent-light: #e0f2fe;
            
            --success: #059669;
            --success-light: #d1fae5;
            
            --warning: #d97706;
            --warning-light: #fef3c7;
            
            --danger: #dc2626;
            --danger-light: #fee2e2;
            
            /* 精细化灰度系统 */
            --white: #ffffff;
            --gray-25: #fcfcfd;
            --gray-50: #f8fafc;
            --gray-100: #f1f5f9;
            --gray-200: #e2e8f0;
            --gray-300: #cbd5e1;
            --gray-400: #94a3b8;
            --gray-500: #64748b;
            --gray-600: #475569;
            --gray-700: #334155;
            --gray-800: #1e293b;
            --gray-900: #0f172a;
            
            /* 设计令牌 */
            --radius-sm: 6px;
            --radius: 8px;
            --radius-md: 10px;
            --radius-lg: 12px;
            --radius-xl: 16px;
            
            /* 阴影系统 */
            --shadow-xs: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
            --shadow-sm: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
            --shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
            --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
            --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
            
            /* 间距系统 */
            --space-1: 4px;
            --space-2: 8px;
            --space-3: 12px;
            --space-4: 16px;
            --space-5: 20px;
            --space-6: 24px;
            --space-8: 32px;
            --space-10: 40px;
            --space-12: 48px;
            --space-16: 64px;
        }

        * {
            box-sizing: border-box;
        }

        body {
            background: var(--gray-25);
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 0;
            color: var(--gray-900);
            line-height: 1.5;
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
        }

        /* 主布局 */
        .app {
            min-height: 100vh;
            background: var(--gray-25);
        }

        .app-header {
            background: var(--white);
            border-bottom: 1px solid var(--gray-200);
            padding: var(--space-6) 0;
            position: sticky;
            top: 0;
            z-index: 50;
            backdrop-filter: blur(8px);
        }

        .header-content {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 var(--space-6);
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .header-left {
            display: flex;
            align-items: center;
            gap: var(--space-4);
        }

        .app-title {
            font-size: 1.5rem;
            font-weight: 700;
            color: var(--gray-900);
            margin: 0;
        }

        .app-subtitle {
            font-size: 0.875rem;
            color: var(--gray-500);
            margin: 0;
        }

        .header-actions {
            display: flex;
            align-items: center;
            gap: var(--space-3);
        }

        /* 主内容区域 */
        .main-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: var(--space-8) var(--space-6);
        }

        /* 工具栏 */
        .toolbar {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: var(--space-6);
            background: var(--white);
            padding: var(--space-4) var(--space-6);
            border-radius: var(--radius-lg);
            border: 1px solid var(--gray-200);
            box-shadow: var(--shadow-sm);
        }

        .toolbar-left {
            display: flex;
            align-items: center;
            gap: var(--space-4);
        }

        .search-box {
            position: relative;
            width: 300px;
        }

        .search-input {
            width: 100%;
            padding: var(--space-3) var(--space-4) var(--space-3) var(--space-10);
            border: 1px solid var(--gray-300);
            border-radius: var(--radius);
            font-size: 0.875rem;
            transition: all 0.15s ease;
            background: var(--white);
        }

        .search-input:focus {
            outline: none;
            border-color: var(--primary);
            box-shadow: 0 0 0 3px var(--primary-light);
        }

        .search-icon {
            position: absolute;
            left: var(--space-3);
            top: 50%;
            transform: translateY(-50%);
            color: var(--gray-400);
            font-size: 0.875rem;
        }

        .filter-select {
            padding: var(--space-3) var(--space-4);
            border: 1px solid var(--gray-300);
            border-radius: var(--radius);
            font-size: 0.875rem;
            background: var(--white);
            cursor: pointer;
            transition: all 0.15s ease;
        }

        .filter-select:focus {
            outline: none;
            border-color: var(--primary);
            box-shadow: 0 0 0 3px var(--primary-light);
        }

        /* 按钮系统 */
        .btn {
            padding: var(--space-3) var(--space-5);
            border: 1px solid transparent;
            border-radius: var(--radius);
            font-weight: 500;
            font-size: 0.875rem;
            cursor: pointer;
            transition: all 0.15s ease;
            display: inline-flex;
            align-items: center;
            gap: var(--space-2);
            text-decoration: none;
            line-height: 1;
        }

        .btn-primary {
            background: var(--primary);
            color: var(--white);
            border-color: var(--primary);
        }

        .btn-primary:hover {
            background: var(--primary-hover);
            border-color: var(--primary-hover);
            transform: translateY(-1px);
            box-shadow: var(--shadow);
        }

        .btn-secondary {
            background: var(--white);
            color: var(--gray-700);
            border-color: var(--gray-300);
        }

        .btn-secondary:hover {
            background: var(--gray-50);
            border-color: var(--gray-400);
            color: var(--gray-900);
        }

        .btn-ghost {
            background: transparent;
            color: var(--gray-600);
            border-color: transparent;
        }

        .btn-ghost:hover {
            background: var(--gray-100);
            color: var(--gray-900);
        }

        .btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none !important;
        }

        .btn-sm {
            padding: var(--space-2) var(--space-3);
            font-size: 0.8125rem;
        }

        /* 编排列表 */
        .workflow-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
            gap: var(--space-6);
        }

        .workflow-card {
            background: var(--white);
            border: 1px solid var(--gray-200);
            border-radius: var(--radius-lg);
            overflow: hidden;
            transition: all 0.15s ease;
            cursor: pointer;
        }

        .workflow-card:hover {
            border-color: var(--primary);
            box-shadow: var(--shadow-lg);
            transform: translateY(-2px);
        }

        .card-header {
            padding: var(--space-5);
            border-bottom: 1px solid var(--gray-200);
            background: var(--gray-50);
        }

        .card-title {
            font-size: 1.125rem;
            font-weight: 600;
            color: var(--gray-900);
            margin: 0 0 var(--space-2) 0;
            line-height: 1.3;
        }

        .card-meta {
            display: flex;
            align-items: center;
            gap: var(--space-4);
            font-size: 0.8125rem;
            color: var(--gray-500);
        }

        .card-content {
            padding: var(--space-5);
        }

        .card-description {
            color: var(--gray-600);
            font-size: 0.875rem;
            line-height: 1.5;
            margin: 0 0 var(--space-4) 0;
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
            overflow: hidden;
        }

        .card-stats {
            display: flex;
            gap: var(--space-4);
            margin-bottom: var(--space-4);
        }

        .stat-item {
            display: flex;
            align-items: center;
            gap: var(--space-1);
            font-size: 0.8125rem;
            color: var(--gray-600);
        }

        .card-actions {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .action-buttons {
            display: flex;
            gap: var(--space-2);
        }

        .status-badge {
            display: inline-flex;
            align-items: center;
            gap: var(--space-1);
            font-size: 0.75rem;
            font-weight: 500;
            padding: var(--space-1) var(--space-2);
            border-radius: var(--radius-sm);
        }

        .status-active {
            background: var(--success-light);
            color: var(--success);
        }

        .status-draft {
            background: var(--warning-light);
            color: var(--warning);
        }

        /* 空状态 */
        .empty-state {
            text-align: center;
            padding: var(--space-16) var(--space-6);
            color: var(--gray-500);
        }

        .empty-icon {
            width: 64px;
            height: 64px;
            background: var(--gray-100);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto var(--space-4);
            font-size: 1.5rem;
            color: var(--gray-400);
        }

        .empty-title {
            font-size: 1.125rem;
            font-weight: 600;
            color: var(--gray-700);
            margin: 0 0 var(--space-2) 0;
        }

        .empty-description {
            font-size: 0.875rem;
            color: var(--gray-500);
            margin: 0 0 var(--space-6) 0;
            line-height: 1.5;
        }

        /* 加载状态 */
        .loading-state {
            text-align: center;
            padding: var(--space-16) var(--space-6);
            color: var(--gray-500);
        }

        .loading-spinner {
            width: 32px;
            height: 32px;
            border: 3px solid var(--gray-200);
            border-top: 3px solid var(--primary);
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin: 0 auto var(--space-4);
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .main-container {
                padding: var(--space-6) var(--space-4);
            }
            
            .header-content {
                padding: 0 var(--space-4);
                flex-direction: column;
                align-items: flex-start;
                gap: var(--space-3);
            }
            
            .header-actions {
                width: 100%;
                justify-content: flex-end;
            }
            
            .toolbar {
                flex-direction: column;
                align-items: stretch;
                gap: var(--space-4);
            }
            
            .toolbar-left {
                flex-direction: column;
                align-items: stretch;
            }
            
            .search-box {
                width: 100%;
            }
            
            .workflow-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="app">
        <!-- 应用头部 -->
        <header class="app-header">
            <div class="header-content">
                <div class="header-left">
                    <div>
                        <h1 class="app-title">智能体编排管理</h1>
                        <p class="app-subtitle">管理您的智能体工作流程</p>
                    </div>
                </div>
                <div class="header-actions">
                    <a href="workflow-designer.html" class="btn btn-primary">
                        <i class="bi bi-plus"></i>
                        <span>新建编排</span>
                    </a>
                </div>
            </div>
        </header>

        <!-- 主内容区域 -->
        <main class="main-container">
            <!-- 工具栏 -->
            <div class="toolbar">
                <div class="toolbar-left">
                    <div class="search-box">
                        <i class="bi bi-search search-icon"></i>
                        <input type="text" class="search-input" id="searchInput" placeholder="搜索编排名称...">
                    </div>
                    <select class="filter-select" id="statusFilter">
                        <option value="">所有状态</option>
                        <option value="active">已激活</option>
                        <option value="draft">草稿</option>
                    </select>
                </div>
                <div class="toolbar-right">
                    <button class="btn btn-ghost btn-sm" id="refreshBtn">
                        <i class="bi bi-arrow-clockwise"></i>
                        <span>刷新</span>
                    </button>
                </div>
            </div>

            <!-- 编排列表 -->
            <div class="workflow-grid" id="workflowGrid">
                <!-- 编排卡片将在这里动态生成 -->
            </div>
        </main>
    </div>

    <!-- 引入JavaScript -->
    <script src="workflow-list.js"></script>
</body>
</html>
