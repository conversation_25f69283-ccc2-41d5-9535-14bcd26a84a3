<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ECharts 渲染测试</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/echarts@5.4.3/dist/echarts.min.js"></script>
    <style>
        body {
            background: #f8fafc;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .test-section {
            background: white;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .chart-container {
            margin: 16px 0;
            border: 1px solid #e5e7eb;
            border-radius: 8px;
            background: white;
            overflow: hidden;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
        }

        .chart-header {
            background: linear-gradient(135deg, #f8fafc, #e2e8f0);
            padding: 12px 16px;
            border-bottom: 1px solid #e5e7eb;
            display: flex;
            align-items: center;
            gap: 8px;
            font-weight: 500;
            color: #374151;
        }

        .chart-header i {
            color: #3b82f6;
            font-size: 1.1rem;
        }

        .chart-title {
            flex: 1;
            font-size: 0.95rem;
        }

        .chart-type-badge {
            background: #3b82f6;
            color: white;
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 0.75rem;
            text-transform: uppercase;
        }

        .chart-content {
            padding: 0;
            position: relative;
        }

        .chart-canvas {
            width: 100%;
            height: 400px;
            min-height: 300px;
        }

        .data-table-container {
            margin: 16px 0;
            border: 1px solid #e5e7eb;
            border-radius: 8px;
            background: white;
            overflow: hidden;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
        }

        .data-table-header {
            background: linear-gradient(135deg, #f1f5f9, #e2e8f0);
            padding: 12px 16px;
            border-bottom: 1px solid #e5e7eb;
            display: flex;
            align-items: center;
            gap: 8px;
            font-weight: 500;
            color: #374151;
        }

        .data-table-header i {
            color: #10b981;
            font-size: 1.1rem;
        }

        .data-table-content {
            overflow-x: auto;
            max-height: 300px;
            overflow-y: auto;
        }

        .data-table {
            width: 100%;
            border-collapse: collapse;
            font-size: 0.875rem;
        }

        .data-table th {
            background: #f8fafc;
            padding: 8px 12px;
            text-align: left;
            font-weight: 500;
            color: #374151;
            border-bottom: 1px solid #e5e7eb;
            position: sticky;
            top: 0;
            z-index: 1;
        }

        .data-table td {
            padding: 8px 12px;
            border-bottom: 1px solid #f1f5f9;
            color: #6b7280;
        }

        .data-table tbody tr:hover {
            background: #f8fafc;
        }

        .data-table tbody tr:last-child td {
            border-bottom: none;
        }

        .btn-test {
            margin: 5px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1 class="mb-4">ECharts 渲染测试</h1>
        
        <div class="test-section">
            <h3>测试控制</h3>
            <button class="btn btn-primary btn-test" onclick="testLineChart()">测试折线图</button>
            <button class="btn btn-success btn-test" onclick="testBarChart()">测试柱状图</button>
            <button class="btn btn-warning btn-test" onclick="testPieChart()">测试饼图</button>
            <button class="btn btn-info btn-test" onclick="testSqlQueryResult()">测试SQL查询结果</button>
            <button class="btn btn-danger btn-test" onclick="clearResults()">清空结果</button>
        </div>
        
        <div class="test-section">
            <h3>渲染结果</h3>
            <div id="result-container">
                <!-- 这里将显示渲染结果 -->
            </div>
        </div>
    </div>

    <script>
        const resultContainer = document.getElementById('result-container');

        function clearResults() {
            resultContainer.innerHTML = '';
        }

        function testLineChart() {
            const chartConfig = {
                title: {
                    text: '2025年每月总邮件数趋势',
                    left: 'center'
                },
                tooltip: {
                    trigger: 'axis'
                },
                legend: {
                    top: 'bottom'
                },
                xAxis: {
                    type: 'category',
                    data: ['202501', '202502', '202503', '202504'],
                    name: '月份'
                },
                yAxis: {
                    type: 'value',
                    name: '总邮件数'
                },
                series: [{
                    name: '总邮件数',
                    type: 'line',
                    smooth: true,
                    data: [20.0, 40.0, 370953.0, 50.0]
                }]
            };

            renderChart(chartConfig, resultContainer);
        }

        function testBarChart() {
            const chartConfig = {
                title: {
                    text: '产品销量对比',
                    left: 'center'
                },
                tooltip: {
                    trigger: 'axis'
                },
                xAxis: {
                    type: 'category',
                    data: ['产品A', '产品B', '产品C', '产品D']
                },
                yAxis: {
                    type: 'value',
                    name: '销量'
                },
                series: [{
                    name: '销量',
                    type: 'bar',
                    data: [120, 200, 150, 80]
                }]
            };

            renderChart(chartConfig, resultContainer);
        }

        function testPieChart() {
            const chartConfig = {
                title: {
                    text: '市场份额分布',
                    left: 'center'
                },
                tooltip: {
                    trigger: 'item'
                },
                legend: {
                    orient: 'vertical',
                    left: 'left'
                },
                series: [{
                    name: '市场份额',
                    type: 'pie',
                    radius: '50%',
                    data: [
                        { value: 1048, name: '搜索引擎' },
                        { value: 735, name: '直接访问' },
                        { value: 580, name: '邮件营销' },
                        { value: 484, name: '联盟广告' },
                        { value: 300, name: '视频广告' }
                    ]
                }]
            };

            renderChart(chartConfig, resultContainer);
        }

        function testSqlQueryResult() {
            // 模拟从history.json中的SQL查询结果
            const sqlData = {
                "duration": 36,
                "maxRows": 100,
                "chart_config": {
                    "yAxis": {
                        "name": "总邮件数",
                        "type": "value"
                    },
                    "xAxis": {
                        "type": "category",
                        "data": ["202501", "202502", "202503", "202504"],
                        "name": "月份"
                    },
                    "legend": {
                        "top": "bottom"
                    },
                    "series": [{
                        "smooth": true,
                        "type": "line",
                        "name": "总邮件数",
                        "data": [20.0, 40.0, 370953.0, 50.0]
                    }],
                    "tooltip": {
                        "trigger": "axis"
                    },
                    "title": {
                        "left": "center",
                        "text": "2025年每月总邮件数趋势"
                    }
                },
                "data": [
                    { "pt_month": "202501", "total_mails": "20", "行号": "1" },
                    { "pt_month": "202502", "total_mails": "40", "行号": "2" },
                    { "pt_month": "202503", "total_mails": "370953", "行号": "3" },
                    { "pt_month": "202504", "total_mails": "50", "行号": "4" }
                ],
                "success": true,
                "columns": [
                    { "dataType": "CHATBI_ROW_NUMBER", "name": "行号" },
                    { "dataType": "NUMERIC", "name": "pt_month" },
                    { "dataType": "NUMERIC", "name": "total_mails" }
                ],
                "rowCount": 4,
                "message": "SQL查询执行成功",
                "sql": "SELECT pt_month, SUM(state_mails) AS total_mails FROM poc_data WHERE pt_month >= 202501 GROUP BY pt_month ORDER BY pt_month"
            };

            handleSqlQueryResult(sqlData, resultContainer);
        }

        // 从主页面复制的渲染函数
        function renderChart(chartConfig, container) {
            try {
                console.log('📈 开始渲染图表:', chartConfig);

                const chartContainer = document.createElement('div');
                chartContainer.className = 'chart-container';

                const chartHeader = document.createElement('div');
                chartHeader.className = 'chart-header';
                
                const chartTitle = chartConfig.title?.text || '数据图表';
                const chartType = getChartTypeFromConfig(chartConfig);
                
                chartHeader.innerHTML = `
                    <i class="bi bi-bar-chart"></i>
                    <span class="chart-title">${chartTitle}</span>
                    <span class="chart-type-badge">${chartType}</span>
                `;

                const chartContent = document.createElement('div');
                chartContent.className = 'chart-content';

                const chartCanvas = document.createElement('div');
                chartCanvas.className = 'chart-canvas';
                chartCanvas.id = `chart-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;

                chartContent.appendChild(chartCanvas);
                chartContainer.appendChild(chartHeader);
                chartContainer.appendChild(chartContent);
                container.appendChild(chartContainer);

                const chart = echarts.init(chartCanvas);
                
                const option = {
                    ...chartConfig,
                    animation: true,
                    animationDuration: 1000,
                    animationEasing: 'cubicOut'
                };

                chart.setOption(option);

                const resizeObserver = new ResizeObserver(() => {
                    chart.resize();
                });
                resizeObserver.observe(chartCanvas);

                chartCanvas._chartInstance = chart;

                console.log('✅ 图表渲染完成');
            } catch (error) {
                console.error('❌ 图表渲染失败:', error);
                const errorDiv = document.createElement('div');
                errorDiv.className = 'chart-error';
                errorDiv.innerHTML = `
                    <i class="bi bi-exclamation-triangle"></i>
                    图表渲染失败: ${error.message}
                `;
                container.appendChild(errorDiv);
            }
        }

        function getChartTypeFromConfig(chartConfig) {
            if (chartConfig.series && chartConfig.series.length > 0) {
                const firstSeries = chartConfig.series[0];
                switch (firstSeries.type) {
                    case 'line': return '折线图';
                    case 'bar': return '柱状图';
                    case 'pie': return '饼图';
                    case 'scatter': return '散点图';
                    case 'radar': return '雷达图';
                    default: return '图表';
                }
            }
            return '图表';
        }

        function renderDataTable(data, columns, container) {
            try {
                console.log('📋 开始渲染数据表格:', { data, columns });

                const tableContainer = document.createElement('div');
                tableContainer.className = 'data-table-container';

                const tableHeader = document.createElement('div');
                tableHeader.className = 'data-table-header';
                tableHeader.innerHTML = `
                    <i class="bi bi-table"></i>
                    <span>查询结果数据 (${data.length} 行)</span>
                `;

                const tableContent = document.createElement('div');
                tableContent.className = 'data-table-content';

                const table = document.createElement('table');
                table.className = 'data-table';

                const thead = document.createElement('thead');
                const headerRow = document.createElement('tr');

                const columnNames = columns && columns.length > 0 
                    ? columns.map(col => col.name) 
                    : Object.keys(data[0] || {});

                columnNames.forEach(columnName => {
                    const th = document.createElement('th');
                    th.textContent = columnName;
                    headerRow.appendChild(th);
                });

                thead.appendChild(headerRow);
                table.appendChild(thead);

                const tbody = document.createElement('tbody');
                data.forEach(row => {
                    const tr = document.createElement('tr');
                    columnNames.forEach(columnName => {
                        const td = document.createElement('td');
                        const value = row[columnName];
                        td.textContent = value !== null && value !== undefined ? value : '';
                        tr.appendChild(td);
                    });
                    tbody.appendChild(tr);
                });

                table.appendChild(tbody);
                tableContent.appendChild(table);
                tableContainer.appendChild(tableHeader);
                tableContainer.appendChild(tableContent);
                container.appendChild(tableContainer);

                console.log('✅ 数据表格渲染完成');
            } catch (error) {
                console.error('❌ 数据表格渲染失败:', error);
                const errorDiv = document.createElement('div');
                errorDiv.className = 'chart-error';
                errorDiv.innerHTML = `
                    <i class="bi bi-exclamation-triangle"></i>
                    数据表格渲染失败: ${error.message}
                `;
                container.appendChild(errorDiv);
            }
        }

        function handleSqlQueryResult(sqlData, container) {
            try {
                console.log('📊 SQL查询结果数据:', sqlData);

                const infoDiv = document.createElement('div');
                infoDiv.style.marginBottom = '16px';
                infoDiv.style.padding = '12px';
                infoDiv.style.background = 'rgba(16, 185, 129, 0.1)';
                infoDiv.style.borderRadius = '6px';
                infoDiv.style.fontSize = '0.875rem';
                infoDiv.innerHTML = `
                    <div><strong>SQL:</strong> ${sqlData.sql || '未知'}</div>
                    <div><strong>执行时间:</strong> ${sqlData.duration || 0}ms</div>
                    <div><strong>返回行数:</strong> ${sqlData.rowCount || 0}</div>
                `;
                container.appendChild(infoDiv);

                if (sqlData.chart_config) {
                    renderChart(sqlData.chart_config, container);
                }

                if (sqlData.data && sqlData.data.length > 0) {
                    renderDataTable(sqlData.data, sqlData.columns, container);
                }
            } catch (error) {
                console.error('❌ 处理SQL查询结果失败:', error);
                const errorDiv = document.createElement('div');
                errorDiv.className = 'chart-error';
                errorDiv.innerHTML = `
                    <i class="bi bi-exclamation-triangle"></i>
                    解析查询结果失败: ${error.message}
                `;
                container.appendChild(errorDiv);
            }
        }

        console.log('✅ ECharts测试页面加载完成');
    </script>
</body>
</html>
