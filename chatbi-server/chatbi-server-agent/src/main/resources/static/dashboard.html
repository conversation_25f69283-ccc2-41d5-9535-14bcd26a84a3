<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ChatBI Agent 管理面板</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css" rel="stylesheet">
    <style>
        :root {
            --primary-color: #6366f1;
            --secondary-color: #8b5cf6;
            --success-color: #10b981;
            --warning-color: #f59e0b;
            --danger-color: #ef4444;
            --text-primary: #1f2937;
            --text-secondary: #6b7280;
            --bg-primary: #f9fafb;
            --border-color: #e5e7eb;
            --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
            --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: var(--bg-primary);
            color: var(--text-primary);
            line-height: 1.6;
        }

        .header {
            background: white;
            border-bottom: 1px solid var(--border-color);
            padding: 1rem 0;
            box-shadow: var(--shadow-sm);
        }

        .header h1 {
            color: var(--primary-color);
            font-weight: 600;
            margin: 0;
        }

        .main-container {
            max-width: 1200px;
            margin: 2rem auto;
            padding: 0 1rem;
        }

        .card-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 1.5rem;
            margin-top: 2rem;
        }

        .feature-card {
            background: white;
            border-radius: 12px;
            padding: 1.5rem;
            box-shadow: var(--shadow-sm);
            border: 1px solid var(--border-color);
            transition: all 0.3s ease;
            text-decoration: none;
            color: inherit;
        }

        .feature-card:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow-lg);
            text-decoration: none;
            color: inherit;
        }

        .feature-card .icon {
            width: 48px;
            height: 48px;
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.5rem;
            color: white;
            margin-bottom: 1rem;
        }

        .feature-card .icon.primary { background: var(--primary-color); }
        .feature-card .icon.success { background: var(--success-color); }
        .feature-card .icon.warning { background: var(--warning-color); }
        .feature-card .icon.danger { background: var(--danger-color); }
        .feature-card .icon.secondary { background: var(--secondary-color); }

        .feature-card h3 {
            font-size: 1.25rem;
            font-weight: 600;
            margin-bottom: 0.5rem;
            color: var(--text-primary);
        }

        .feature-card p {
            color: var(--text-secondary);
            font-size: 0.9rem;
            margin: 0;
        }

        .status-badge {
            display: inline-block;
            padding: 0.25rem 0.75rem;
            border-radius: 20px;
            font-size: 0.75rem;
            font-weight: 500;
            margin-top: 0.5rem;
        }

        .status-badge.active {
            background: rgba(16, 185, 129, 0.1);
            color: var(--success-color);
        }

        .status-badge.beta {
            background: rgba(245, 158, 11, 0.1);
            color: var(--warning-color);
        }

        .intro-section {
            background: white;
            border-radius: 12px;
            padding: 2rem;
            box-shadow: var(--shadow-sm);
            border: 1px solid var(--border-color);
            text-align: center;
        }

        .intro-section h2 {
            color: var(--primary-color);
            margin-bottom: 1rem;
        }

        .intro-section p {
            color: var(--text-secondary);
            font-size: 1.1rem;
            max-width: 600px;
            margin: 0 auto;
        }

        @media (max-width: 768px) {
            .card-grid {
                grid-template-columns: 1fr;
            }
            
            .main-container {
                padding: 0 0.5rem;
            }
        }
    </style>
</head>
<body>
    <div class="header">
        <div class="container">
            <h1><i class="bi bi-robot me-2"></i>ChatBI Agent 管理面板</h1>
        </div>
    </div>

    <div class="main-container">
        <div class="intro-section">
            <h2>欢迎使用 ChatBI Agent 系统</h2>
            <p>这是一个基于多智能体架构的对话式商业智能系统，提供智能对话、数据分析、工作流编排等功能。选择下方的功能模块开始使用。</p>
        </div>

        <div class="card-grid">
            <!-- 对话聊天 -->
            <a href="hierarchical-chat.html" class="feature-card">
                <div class="icon primary">
                    <i class="bi bi-chat-dots"></i>
                </div>
                <h3>智能对话</h3>
                <p>与AI助手进行层次化对话，支持计划生成、智能体执行和思考过程展示</p>
                <span class="status-badge active">核心功能</span>
            </a>

            <!-- Agent 配置 -->
            <a href="agent-config.html" class="feature-card">
                <div class="icon success">
                    <i class="bi bi-gear"></i>
                </div>
                <h3>Agent 配置</h3>
                <p>配置和管理智能体，包括模型设置、工具配置和行为参数</p>
                <span class="status-badge active">管理工具</span>
            </a>

            <!-- MCP 配置 -->
            <a href="mcp-config.html" class="feature-card">
                <div class="icon warning">
                    <i class="bi bi-plugin"></i>
                </div>
                <h3>MCP 服务器</h3>
                <p>管理 Model Context Protocol (MCP) 服务器配置，扩展系统能力</p>
                <span class="status-badge active">扩展功能</span>
            </a>

            <!-- 工作流编排 -->
            <a href="workflow-list.html" class="feature-card">
                <div class="icon secondary">
                    <i class="bi bi-diagram-3"></i>
                </div>
                <h3>工作流编排</h3>
                <p>查看和管理计划模板，设计复杂的多步骤工作流程</p>
                <span class="status-badge active">编排工具</span>
            </a>

            <!-- 计划模板 -->
            <a href="plan-template/index.html" class="feature-card">
                <div class="icon primary">
                    <i class="bi bi-file-earmark-code"></i>
                </div>
                <h3>计划模板</h3>
                <p>创建和编辑计划模板，定义智能体执行流程</p>
                <span class="status-badge active">模板管理</span>
            </a>

            <!-- 流式事件查看器 -->
            <a href="streaming-event-viewer.html" class="feature-card">
                <div class="icon danger">
                    <i class="bi bi-activity"></i>
                </div>
                <h3>事件监控</h3>
                <p>实时查看系统执行事件流，监控任务执行状态</p>
                <span class="status-badge beta">调试工具</span>
            </a>

            <!-- 管理后台 -->
            <a href="admin/index.html" class="feature-card">
                <div class="icon secondary">
                    <i class="bi bi-shield-check"></i>
                </div>
                <h3>管理后台</h3>
                <p>系统管理界面，包括用户管理、权限配置等高级功能</p>
                <span class="status-badge active">管理面板</span>
            </a>

            <!-- 测试工具 -->
            <a href="ui-test.html" class="feature-card">
                <div class="icon warning">
                    <i class="bi bi-tools"></i>
                </div>
                <h3>UI 测试</h3>
                <p>界面组件测试页面，用于开发和调试界面元素</p>
                <span class="status-badge beta">开发工具</span>
            </a>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // 简单的页面加载动画
        document.addEventListener('DOMContentLoaded', function() {
            const cards = document.querySelectorAll('.feature-card');
            cards.forEach((card, index) => {
                card.style.opacity = '0';
                card.style.transform = 'translateY(20px)';
                
                setTimeout(() => {
                    card.style.transition = 'all 0.5s ease';
                    card.style.opacity = '1';
                    card.style.transform = 'translateY(0)';
                }, index * 100);
            });
        });

        // 添加点击反馈
        document.querySelectorAll('.feature-card').forEach(card => {
            card.addEventListener('click', function(e) {
                // 添加点击动画效果
                this.style.transform = 'scale(0.98)';
                setTimeout(() => {
                    this.style.transform = '';
                }, 150);
            });
        });
    </script>
</body>
</html>
