<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Agent配置管理 - JTaskPilot</title>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css" rel="stylesheet">
    <style>
        :root {
            --primary-color: #6366f1;
            --primary-hover: #4f46e5;
            --primary-light: #e0e7ff;
            --secondary-color: #64748b;
            --success-color: #10b981;
            --success-light: #d1fae5;
            --danger-color: #ef4444;
            --danger-light: #fee2e2;
            --warning-color: #f59e0b;
            --warning-light: #fef3c7;
            --info-color: #06b6d4;
            --info-light: #cffafe;
            --gray-50: #f9fafb;
            --gray-100: #f3f4f6;
            --gray-200: #e5e7eb;
            --gray-300: #d1d5db;
            --gray-400: #9ca3af;
            --gray-500: #6b7280;
            --gray-600: #4b5563;
            --gray-700: #374151;
            --gray-800: #1f2937;
            --gray-900: #111827;
            --border-radius: 12px;
            --border-radius-lg: 16px;
            --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
            --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
            --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
            --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
            background: var(--gray-50);
            color: var(--gray-900);
            margin: 0;
            padding: 0;
            min-height: 100vh;
            line-height: 1.6;
        }

        .app-container {
            min-height: 100vh;
            display: flex;
            flex-direction: column;
        }

        /* 顶部导航栏 */
        .app-header {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-bottom: 1px solid var(--gray-200);
            padding: 0 32px;
            height: 72px;
            display: flex;
            align-items: center;
            justify-content: space-between;
            box-shadow: var(--shadow-sm);
            position: sticky;
            top: 0;
            z-index: 100;
        }

        .app-title {
            display: flex;
            align-items: center;
            gap: 12px;
            font-size: 1.75rem;
            font-weight: 700;
            color: var(--gray-900);
            letter-spacing: -0.025em;
        }

        .app-title i {
            font-size: 2rem;
            color: var(--primary-color);
            background: var(--primary-light);
            padding: 8px;
            border-radius: var(--border-radius);
        }

        .header-actions {
            display: flex;
            gap: 12px;
        }

        .action-btn {
            background: white;
            color: var(--gray-700);
            border: 1px solid var(--gray-300);
            padding: 10px 16px;
            border-radius: var(--border-radius);
            font-size: 0.875rem;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
            display: flex;
            align-items: center;
            gap: 8px;
            text-decoration: none;
            box-shadow: var(--shadow-sm);
        }

        .action-btn:hover {
            background: var(--gray-50);
            border-color: var(--gray-400);
            transform: translateY(-1px);
            box-shadow: var(--shadow-md);
            color: var(--gray-700);
        }

        .action-btn.primary {
            background: var(--primary-color);
            color: white;
            border-color: var(--primary-color);
        }

        .action-btn.primary:hover {
            background: var(--primary-hover);
            border-color: var(--primary-hover);
            color: white;
        }

        /* 主内容区域 */
        .main-content {
            flex: 1;
            background: var(--gray-50);
            min-height: calc(100vh - 72px);
            padding: 32px;
        }

        /* 顶部操作栏 */
        .content-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 32px;
        }

        .content-title {
            font-size: 2rem;
            font-weight: 800;
            color: var(--gray-900);
            letter-spacing: -0.025em;
        }

        .content-actions {
            display: flex;
            gap: 12px;
            align-items: center;
        }

        .search-bar {
            position: relative;
            width: 320px;
        }

        .search-bar input {
            width: 100%;
            padding: 12px 16px 12px 44px;
            border: 1px solid var(--gray-300);
            border-radius: var(--border-radius);
            font-size: 0.875rem;
            background: white;
            box-shadow: var(--shadow-sm);
            transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
        }

        .search-bar input:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px var(--primary-light);
        }

        .search-bar i {
            position: absolute;
            left: 16px;
            top: 50%;
            transform: translateY(-50%);
            color: var(--gray-400);
        }

        /* Agent卡片网格布局 */
        .agent-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(380px, 1fr));
            gap: 24px;
            margin-bottom: 32px;
        }

        .agent-card {
            background: white;
            border: 1px solid var(--gray-200);
            border-radius: var(--border-radius-lg);
            padding: 24px;
            box-shadow: var(--shadow-sm);
            transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
            cursor: pointer;
            position: relative;
            overflow: hidden;
        }

        .agent-card:hover {
            border-color: var(--primary-color);
            box-shadow: var(--shadow-xl);
            transform: translateY(-4px);
        }

        .agent-card.selected {
            border-color: var(--primary-color);
            background: var(--primary-light);
            box-shadow: var(--shadow-xl);
        }

        .agent-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: var(--primary-color);
            opacity: 0;
            transition: opacity 0.2s ease;
        }

        .agent-card.selected::before,
        .agent-card:hover::before {
            opacity: 1;
        }

        .agent-card-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 16px;
        }

        .agent-card-title {
            font-size: 1.25rem;
            font-weight: 700;
            color: var(--gray-900);
            letter-spacing: -0.025em;
            margin: 0;
        }

        .agent-card-id {
            font-size: 0.75rem;
            color: var(--gray-500);
            background: var(--gray-100);
            padding: 4px 8px;
            border-radius: 6px;
            font-weight: 500;
            font-family: 'JetBrains Mono', 'Fira Code', 'Courier New', monospace;
        }

        .agent-card-description {
            font-size: 0.875rem;
            color: var(--gray-600);
            line-height: 1.5;
            margin-bottom: 16px;
            display: -webkit-box;
            -webkit-line-clamp: 3;
            -webkit-box-orient: vertical;
            overflow: hidden;
        }

        .agent-card-tools {
            display: flex;
            flex-wrap: wrap;
            gap: 6px;
            margin-bottom: 16px;
        }

        .agent-tool-tag {
            background: var(--primary-light);
            color: var(--primary-color);
            padding: 4px 8px;
            border-radius: 16px;
            font-size: 0.75rem;
            font-weight: 500;
        }

        .agent-card-actions {
            display: flex;
            gap: 8px;
            justify-content: flex-end;
        }

        .card-action-btn {
            padding: 6px 12px;
            border: 1px solid var(--gray-300);
            border-radius: 6px;
            background: white;
            color: var(--gray-600);
            font-size: 0.75rem;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .card-action-btn:hover {
            border-color: var(--primary-color);
            color: var(--primary-color);
        }

        .card-action-btn.danger:hover {
            border-color: var(--danger-color);
            color: var(--danger-color);
        }

        /* 新建Agent卡片 */
        .new-agent-card {
            background: linear-gradient(135deg, var(--primary-color), var(--primary-hover));
            border: 2px dashed var(--primary-color);
            border-radius: var(--border-radius-lg);
            padding: 40px 24px;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
            color: white;
            text-align: center;
            min-height: 200px;
        }

        .new-agent-card:hover {
            transform: translateY(-4px);
            box-shadow: var(--shadow-xl);
        }

        .new-agent-card i {
            font-size: 3rem;
            margin-bottom: 16px;
            opacity: 0.9;
        }

        .new-agent-card h3 {
            font-size: 1.25rem;
            font-weight: 700;
            margin: 0 0 8px 0;
        }

        .new-agent-card p {
            font-size: 0.875rem;
            opacity: 0.9;
            margin: 0;
        }

        /* 空状态 */
        .empty-grid {
            text-align: center;
            padding: 80px 40px;
            color: var(--gray-500);
        }

        .empty-grid i {
            font-size: 5rem;
            margin-bottom: 24px;
            display: block;
            opacity: 0.6;
            color: var(--gray-400);
        }

        .empty-grid h3 {
            font-size: 1.5rem;
            margin-bottom: 12px;
            color: var(--gray-900);
            font-weight: 700;
        }

        .empty-grid p {
            font-size: 1rem;
            line-height: 1.6;
            color: var(--gray-600);
            margin-bottom: 24px;
        }

        /* 侧边抽屉 */
        .drawer-overlay {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0, 0, 0, 0.5);
            z-index: 1000;
            opacity: 0;
            visibility: hidden;
            transition: all 0.3s ease;
        }

        .drawer-overlay.show {
            opacity: 1;
            visibility: visible;
        }

        .config-drawer {
            position: fixed;
            top: 0;
            right: 0;
            width: 700px;
            height: 100vh;
            background: white;
            box-shadow: var(--shadow-xl);
            z-index: 1001;
            transform: translateX(100%);
            transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            display: flex;
            flex-direction: column;
        }

        .config-drawer.show {
            transform: translateX(0);
        }

        .drawer-header {
            padding: 24px 32px;
            border-bottom: 1px solid var(--gray-200);
            background: var(--gray-50);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .drawer-title {
            font-size: 1.5rem;
            font-weight: 700;
            color: var(--gray-900);
            margin: 0;
        }

        .drawer-close {
            background: none;
            border: none;
            font-size: 1.5rem;
            color: var(--gray-500);
            cursor: pointer;
            padding: 8px;
            border-radius: var(--border-radius);
            transition: all 0.2s ease;
        }

        .drawer-close:hover {
            background: var(--gray-200);
            color: var(--gray-700);
        }

        .drawer-content {
            flex: 1;
            padding: 32px;
            overflow-y: auto;
        }

        .drawer-footer {
            padding: 24px 32px;
            border-top: 1px solid var(--gray-200);
            background: var(--gray-50);
            display: flex;
            gap: 12px;
            justify-content: flex-end;
        }

        .loading-placeholder {
            text-align: center;
            padding: 40px 20px;
            color: var(--gray-500);
            font-size: 0.875rem;
        }

        .loading-placeholder i {
            font-size: 2rem;
            margin-bottom: 10px;
            display: block;
        }

        /* 右侧配置详情区域 */
        .config-detail {
            flex: 1;
            background: white;
            display: flex;
            flex-direction: column;
            overflow: hidden;
            min-height: 0; /* 确保flex子元素可以收缩 */
        }

        .detail-form {
            display: none;
            flex-direction: column;
            height: 100%;
        }

        .detail-header {
            padding: 20px 30px;
            border-bottom: 1px solid var(--border-color);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .detail-title {
            font-size: 1.25rem;
            font-weight: 600;
            color: var(--text-primary);
            margin: 0;
        }

        .detail-actions {
            display: flex;
            gap: 10px;
        }

        .detail-content {
            flex: 1;
            overflow-y: auto;
            padding: 30px;
        }

        .form-section {
            margin-bottom: 30px;
        }

        .section-title {
            font-size: 1.1rem;
            font-weight: 600;
            color: var(--text-primary);
            margin-bottom: 15px;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-label {
            display: block;
            margin-bottom: 8px;
            font-weight: 500;
            color: var(--text-primary);
            font-size: 0.875rem;
        }

        .form-input {
            width: 100%;
            padding: 12px 16px;
            border: 1px solid var(--border-color);
            border-radius: 8px;
            font-size: 0.875rem;
            transition: border-color 0.2s ease;
            font-family: inherit;
        }

        .form-input:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
        }

        .form-textarea {
            min-height: 120px;
            resize: vertical;
            font-family: 'SF Mono', Monaco, 'Cascadia Code', 'Roboto Mono', Consolas, 'Courier New', monospace;
            line-height: 1.5;
        }

        .form-textarea.large {
            min-height: 200px;
        }

        /* 工具选择区域 */
        .tools-section {
            border: 1px solid var(--border-color);
            border-radius: 8px;
            padding: 20px;
            background: var(--bg-primary);
        }

        .tools-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
        }

        .tools-title {
            font-size: 1rem;
            font-weight: 600;
            color: var(--text-primary);
        }

        .add-tool-btn {
            background: var(--success-color);
            color: white;
            border: none;
            padding: 8px 12px;
            border-radius: 6px;
            font-size: 0.875rem;
            cursor: pointer;
            display: flex;
            align-items: center;
            gap: 6px;
            transition: all 0.2s ease;
        }

        .add-tool-btn:hover {
            background: #059669;
            transform: translateY(-1px);
        }

        .selected-tools {
            display: flex;
            flex-wrap: wrap;
            gap: 8px;
            min-height: 40px;
            padding: 10px;
            border: 1px dashed var(--border-color);
            border-radius: 6px;
            background: white;
        }

        .selected-tool {
            background: var(--primary-color);
            color: white;
            padding: 6px 12px;
            border-radius: 20px;
            font-size: 0.875rem;
            display: flex;
            align-items: center;
            gap: 6px;
            animation: toolSlideIn 0.3s ease;
        }

        @keyframes toolSlideIn {
            from {
                opacity: 0;
                transform: scale(0.8);
            }
            to {
                opacity: 1;
                transform: scale(1);
            }
        }

        .remove-tool-btn {
            background: transparent;
            border: none;
            color: white;
            cursor: pointer;
            padding: 2px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: background-color 0.2s ease;
        }

        .remove-tool-btn:hover {
            background: rgba(255, 255, 255, 0.2);
        }

        .empty-tools {
            color: var(--text-secondary);
            font-style: italic;
            text-align: center;
            padding: 20px;
        }

        /* 空状态 */
        .empty-state {
            text-align: center;
            padding: 60px 40px;
            color: var(--text-secondary);
        }

        .empty-state i {
            font-size: 4rem;
            margin-bottom: 20px;
            display: block;
            opacity: 0.5;
        }

        .empty-state h3 {
            font-size: 1.25rem;
            margin-bottom: 10px;
            color: var(--text-primary);
        }

        .empty-state p {
            font-size: 0.875rem;
            line-height: 1.5;
        }

        /* 按钮样式 */
        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 6px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s ease;
            font-size: 0.875rem;
            display: inline-flex;
            align-items: center;
            gap: 6px;
        }

        .btn-primary {
            background: var(--primary-color);
            color: white;
        }

        .btn-primary:hover {
            background: var(--secondary-color);
            transform: translateY(-1px);
        }

        .btn-danger {
            background: var(--danger-color);
            color: white;
        }

        .btn-danger:hover {
            background: #dc2626;
            transform: translateY(-1px);
        }

        .btn-secondary {
            background: var(--text-secondary);
            color: white;
        }

        .btn-secondary:hover {
            background: var(--text-primary);
        }

        .btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none !important;
        }

        /* 工具选择对话框样式 */
        .modal-xl {
            max-width: 1200px;
        }

        .tool-controls {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            border: 1px solid #e9ecef;
        }

        .search-container {
            position: relative;
        }

        .search-icon {
            position: absolute;
            right: 12px;
            top: 50%;
            transform: translateY(-50%);
            color: #6c757d;
        }

        .tool-stats {
            font-size: 14px;
            padding: 8px 0;
            border-bottom: 1px solid #e9ecef;
        }

        .tool-groups {
            max-height: 500px;
            overflow-y: auto;
        }

        .tool-group {
            margin-bottom: 20px;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            overflow: hidden;
        }

        .tool-group-header {
            background: #f8f9fa;
            padding: 12px 16px;
            border-bottom: 1px solid #e9ecef;
            display: flex;
            align-items: center;
            justify-content: space-between;
            cursor: pointer;
            transition: background-color 0.2s ease;
        }

        .tool-group-header:hover {
            background: #e9ecef;
        }

        .tool-group-title {
            display: flex;
            align-items: center;
            gap: 8px;
            font-weight: 600;
            color: #495057;
        }

        .tool-group-icon {
            color: #ffc107;
            font-size: 16px;
        }

        .tool-group-count {
            background: #6c757d;
            color: white;
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: 500;
        }

        .tool-group-toggle {
            color: #6c757d;
            transition: transform 0.2s ease;
        }

        .tool-group.collapsed .tool-group-toggle {
            transform: rotate(-90deg);
        }

        .tool-group-content {
            padding: 16px;
            background: white;
        }

        .tool-group.collapsed .tool-group-content {
            display: none;
        }

        .tool-item {
            border: 1px solid #e9ecef;
            border-radius: 6px;
            padding: 12px;
            margin-bottom: 12px;
            cursor: pointer;
            transition: all 0.2s ease;
            background: white;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .tool-item:last-child {
            margin-bottom: 0;
        }

        .tool-item:hover {
            border-color: #007bff;
            box-shadow: 0 2px 4px rgba(0,123,255,0.1);
        }

        .tool-item.selected {
            border-color: #007bff;
            background-color: #f8f9ff;
        }

        .tool-info {
            flex: 1;
        }

        .tool-name {
            font-weight: 600;
            color: #333;
            margin: 0 0 4px 0;
            font-size: 14px;
        }

        .tool-description {
            color: #6c757d;
            font-size: 13px;
            line-height: 1.4;
            margin: 0;
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
            overflow: hidden;
        }

        .tool-toggle {
            margin-left: 12px;
        }

        .form-check-input:checked {
            background-color: #007bff;
            border-color: #007bff;
        }

        .group-select-all {
            display: flex;
            align-items: center;
            gap: 8px;
            font-size: 12px;
            color: #6c757d;
        }

        .text-muted {
            color: var(--text-secondary) !important;
        }

        /* 消息提示样式 */
        .alert {
            border: none;
            border-radius: 8px;
            box-shadow: var(--shadow-lg);
            animation: slideInRight 0.3s ease;
        }

        @keyframes slideInRight {
            from {
                opacity: 0;
                transform: translateX(100%);
            }
            to {
                opacity: 1;
                transform: translateX(0);
            }
        }

        .alert-success {
            background: var(--success-color);
            color: white;
        }

        .alert-danger {
            background: var(--danger-color);
            color: white;
        }

        /* 表单验证样式 */
        .form-input.is-invalid {
            border-color: var(--danger-color);
            box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1);
        }

        .invalid-feedback {
            display: block;
            color: var(--danger-color);
            font-size: 0.875rem;
            margin-top: 5px;
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .app-header {
                padding: 0 15px;
            }

            .app-title {
                font-size: 1.25rem;
            }

            .header-actions {
                gap: 5px;
            }

            .action-btn {
                padding: 6px 10px;
                font-size: 0.8rem;
            }

            .agent-sidebar {
                width: 100%;
                position: absolute;
                z-index: 100;
                transform: translateX(-100%);
            }

            .agent-sidebar.show {
                transform: translateX(0);
            }

            .config-detail {
                width: 100%;
            }

            .detail-content {
                padding: 20px;
            }

            .form-textarea.large {
                min-height: 150px;
            }
        }

        @media (max-width: 480px) {
            .detail-header {
                flex-direction: column;
                gap: 15px;
                align-items: stretch;
            }

            .detail-actions {
                justify-content: stretch;
            }

            .detail-actions .btn {
                flex: 1;
            }
        }
    </style>
</head>
<body>
    <div class="app-container">
        <!-- 顶部导航栏 -->
        <div class="app-header">
            <div class="app-title">
                <i class="bi bi-robot"></i>
                <span>Agent配置管理</span>
            </div>
            <div class="header-actions">
                <button class="action-btn" id="debugBtn" title="调试信息">
                    <i class="bi bi-bug"></i>
                    <span>调试</span>
                </button>
                <button class="action-btn" id="backBtn">
                    <i class="bi bi-arrow-left"></i>
                    <span>返回</span>
                </button>
                <button class="action-btn" id="importBtn">
                    <i class="bi bi-upload"></i>
                    <span>导入</span>
                </button>
                <button class="action-btn" id="exportBtn">
                    <i class="bi bi-download"></i>
                    <span>导出</span>
                </button>
            </div>
        </div>

        <!-- 主内容区域 -->
        <div class="main-content">
            <!-- 顶部操作栏 -->
            <div class="content-header">
                <h1 class="content-title">智能代理</h1>
                <div class="content-actions">
                    <div class="search-bar">
                        <i class="bi bi-search"></i>
                        <input type="text" id="searchInput" placeholder="搜索Agent...">
                    </div>
                    <button class="btn btn-primary" id="newAgentBtn">
                        <i class="bi bi-plus-lg"></i>
                        <span>新建Agent</span>
                    </button>
                </div>
            </div>

            <!-- Agent卡片网格 -->
            <div class="agent-grid" id="agentGrid">
                <div class="loading-placeholder">
                    <i class="bi bi-hourglass-split"></i>
                    <span>加载Agent列表...</span>
                </div>
            </div>
        </div>

        <!-- 配置抽屉 -->
        <div class="drawer-overlay" id="drawerOverlay"></div>
        <div class="config-drawer" id="configDrawer">
            <div class="drawer-header">
                <h2 class="drawer-title" id="drawerTitle">Agent配置</h2>
                <button class="drawer-close" id="drawerClose">
                    <i class="bi bi-x"></i>
                </button>
            </div>
            <div class="drawer-content" id="drawerContent">
                <!-- 配置表单将在这里动态生成 -->

            </div>
            <div class="drawer-footer" id="drawerFooter">
                <!-- 操作按钮将在这里动态生成 -->
            </div>
    </div>

    <!-- 工具选择对话框 -->
    <div class="modal fade" id="toolSelectionModal" tabindex="-1">
        <div class="modal-dialog modal-xl">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">
                        <i class="bi bi-tools"></i>
                        <span>选择工具</span>
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <!-- 搜索和控制区域 -->
                    <div class="tool-controls mb-4">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="search-container">
                                    <input type="text" class="form-control" id="toolSearchInput" placeholder="搜索工具...">
                                    <i class="bi bi-search search-icon"></i>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <select class="form-select" id="sortBySelect">
                                    <option value="serviceGroup">按服务组排序</option>
                                    <option value="name">按名称排序</option>
                                    <option value="enabled">按启用状态排序</option>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <div class="form-check form-switch">
                                    <input class="form-check-input" type="checkbox" id="selectAllTools">
                                    <label class="form-check-label" for="selectAllTools">
                                        启用全部
                                    </label>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 工具统计信息 -->
                    <div class="tool-stats mb-3">
                        <span class="text-muted" id="toolStatsText">共 0 个服务组，0 个工具</span>
                    </div>

                    <!-- 工具分组列表 -->
                    <div class="tool-groups" id="toolGroups">
                        <!-- 工具分组将在这里动态生成 -->
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary" id="confirmToolSelection">确认选择</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 引入JavaScript -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="admin/js/admin-api.js"></script>
    <script>
        // 全局变量
        let currentAgent = null;
        let allAgents = [];
        let allTools = [];
        let selectedTools = [];
        let isEditing = false;

        // DOM元素
        let agentGrid, searchInput, newAgentBtn, configDrawer, drawerOverlay, drawerClose;
        let toolSelectionModal, toolSearchInput;

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            console.log('Agent配置页面开始初始化...');

            // 延迟初始化，确保DOM完全渲染
            setTimeout(() => {
                try {
                    // 获取DOM元素
                    initializeElements();

                    // 设置事件监听器
                    setupEventListeners();

                    // 加载数据
                    loadInitialData();

                    console.log('Agent配置页面初始化完成');
                } catch (error) {
                    console.error('页面初始化失败:', error);
                    alert('页面初始化失败: ' + error.message);
                }
            }, 100);
        });

        function initializeElements() {
            // 主要元素
            agentGrid = document.getElementById('agentGrid');
            searchInput = document.getElementById('searchInput');
            newAgentBtn = document.getElementById('newAgentBtn');

            // 抽屉相关元素
            configDrawer = document.getElementById('configDrawer');
            drawerOverlay = document.getElementById('drawerOverlay');
            drawerClose = document.getElementById('drawerClose');

            // 对话框元素
            const modalElement = document.getElementById('toolSelectionModal');
            if (modalElement) {
                toolSelectionModal = new bootstrap.Modal(modalElement);
            }
            toolSearchInput = document.getElementById('toolSearchInput');

            // 验证关键元素是否存在
            const requiredElements = {
                agentGrid, searchInput, newAgentBtn
            };

            for (const [name, element] of Object.entries(requiredElements)) {
                if (!element) {
                    console.error(`关键元素未找到: ${name}`);
                }
            }

            console.log('DOM元素初始化完成', {
                agentGrid: !!agentGrid,
                searchInput: !!searchInput,
                newAgentBtn: !!newAgentBtn,
                configDrawer: !!configDrawer,
                toolModal: !!toolSelectionModal
            });
        }

        function setupEventListeners() {
            // 搜索功能
            if (searchInput) {
                searchInput.addEventListener('input', function() {
                    const keyword = this.value.trim().toLowerCase();
                    filterAgents(keyword);
                });
            }

            // 新建Agent按钮
            if (newAgentBtn) {
                newAgentBtn.addEventListener('click', function() {
                    openAgentDrawer();
                });
            }

            // 抽屉关闭按钮
            if (drawerClose) {
                drawerClose.addEventListener('click', closeAgentDrawer);
            }

            // 抽屉遮罩点击关闭
            if (drawerOverlay) {
                drawerOverlay.addEventListener('click', closeAgentDrawer);
            }



            // 调试按钮
            const debugBtn = document.getElementById('debugBtn');
            if (debugBtn) {
                debugBtn.addEventListener('click', function() {
                    showDebugInfo();
                });
            }

            // 返回按钮
            const backBtn = document.getElementById('backBtn');
            if (backBtn) {
                backBtn.addEventListener('click', function() {
                    window.location.href = '/';
                });
            }

            // 导入导出按钮
            const importBtn = document.getElementById('importBtn');
            if (importBtn) {
                importBtn.addEventListener('click', function() {
                    importAgents();
                });
            }

            const exportBtn = document.getElementById('exportBtn');
            if (exportBtn) {
                exportBtn.addEventListener('click', function() {
                    exportAgents();
                });
            }

            // 工具选择确认
            const confirmToolSelectionBtn = document.getElementById('confirmToolSelection');
            if (confirmToolSelectionBtn) {
                confirmToolSelectionBtn.addEventListener('click', function() {
                    confirmToolSelection();
                });
            }

            // 工具搜索
            if (toolSearchInput) {
                toolSearchInput.addEventListener('input', function() {
                    const keyword = this.value.trim().toLowerCase();
                    filterTools(keyword);
                });
            }

            // 排序方式选择
            const sortBySelect = document.getElementById('sortBySelect');
            if (sortBySelect) {
                sortBySelect.addEventListener('change', function() {
                    renderToolGroups();
                });
            }

            // 全选工具
            const selectAllTools = document.getElementById('selectAllTools');
            if (selectAllTools) {
                selectAllTools.addEventListener('change', function() {
                    const allCheckboxes = document.querySelectorAll('#toolGroups .tool-item input[type="checkbox"]');
                    allCheckboxes.forEach(checkbox => {
                        checkbox.checked = this.checked;
                        checkbox.dispatchEvent(new Event('change'));
                    });
                });
            }

            console.log('事件监听器设置完成');
        }

        async function loadInitialData() {
            try {
                // 显示加载状态
                showLoading();

                // 检查AdminAPI是否可用
                if (typeof AdminAPI === 'undefined') {
                    console.warn('AdminAPI未加载，使用模拟数据');
                    loadMockData();
                    return;
                }

                // 并行加载Agent列表和工具列表
                const [agentsData, toolsData] = await Promise.all([
                    loadAgents(),
                    loadTools()
                ]);

                allAgents = agentsData || [];
                allTools = toolsData || [];

                console.log('数据加载完成:', {
                    agents: allAgents.length,
                    tools: allTools.length
                });

                // 渲染Agent列表
                renderAgentList();

            } catch (error) {
                console.error('加载数据失败:', error);
                showError('加载数据失败: ' + error.message);
                // 加载失败时使用模拟数据
                loadMockData();
            }
        }

        /**
         * 处理R类统一响应格式
         * @param {Response} response - 响应对象
         * @returns {Promise<any>} - 处理后的数据
         */
        async function handleRResponse(response) {
            if (!response.ok) {
                throw new Error(`API请求失败: ${response.status}`);
            }

            const result = await response.json();

            // 检查R类响应格式
            if (result.code && result.code !== 1) {
                throw new Error(result.message || 'API请求失败');
            }

            // 返回data字段，如果没有data字段则返回整个结果（兼容旧格式）
            return result.data !== undefined ? result.data : result;
        }

        async function loadAgents() {
            try {
                if (AdminAPI && AdminAPI.getAllAgents) {
                    return await AdminAPI.getAllAgents();
                } else {
                    // 直接调用API
                    const response = await fetch('/api/agents');
                    return await handleRResponse(response);
                }
            } catch (error) {
                console.error('加载Agent列表失败:', error);
                throw error;
            }
        }

        async function loadTools() {
            try {
                if (AdminAPI && AdminAPI.getAvailableTools) {
                    return await AdminAPI.getAvailableTools();
                } else {
                    // 直接调用API
                    const response = await fetch('/api/agents/tools');
                    return await handleRResponse(response);
                }
            } catch (error) {
                console.error('加载工具列表失败:', error);
                throw error;
            }
        }

        function loadMockData() {
            // 模拟数据用于测试
            allAgents = [
                {
                    id: 'DEFAULT_AGENT',
                    name: 'DEFAULT_AGENT',
                    description: '一个通用的智能代理，可以使用各种工具来完成任务。它具有强大的推理能力，并能根据用户需求选择合适的工具。',
                    systemPrompt: '',
                    nextStepPrompt: '请根据用户的需求，选择合适的工具来完成任务。',
                    availableTools: ['tool_one_query_executor', 'terminal'],
                    className: ''
                },
                {
                    id: 'TAVILY_AGENT',
                    name: 'TAVILY_AGENT',
                    description: '一个专门用于Tavily工具集成的智能代理。它能够使用Tavily的各种功能来处理特定的任务。',
                    systemPrompt: '',
                    nextStepPrompt: '使用Tavily工具来处理用户的请求。',
                    availableTools: ['tavily_one_query_extract', 'tavily_one_query_cloud', 'tavily_one_query_search', 'tavily_one_query_map', 'terminal'],
                    className: ''
                }
            ];

            allTools = [
                { key: 'tool_one_query_executor', name: '查询执行器', description: '执行各种查询操作' },
                { key: 'terminal', name: '终端', description: '执行系统命令' },
                { key: 'tavily_one_query_extract', name: 'Tavily提取', description: 'Tavily数据提取工具' },
                { key: 'tavily_one_query_cloud', name: 'Tavily云服务', description: 'Tavily云服务工具' },
                { key: 'tavily_one_query_search', name: 'Tavily搜索', description: 'Tavily搜索工具' },
                { key: 'tavily_one_query_map', name: 'Tavily地图', description: 'Tavily地图工具' }
            ];

            console.log('使用模拟数据:', { agents: allAgents.length, tools: allTools.length });
            renderAgentList();
        }

        async function createAgent(agentConfig) {
            try {
                if (AdminAPI && AdminAPI.createAgent) {
                    return await AdminAPI.createAgent(agentConfig);
                } else {
                    const response = await fetch('/api/agents', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify(agentConfig)
                    });

                    return await handleRResponse(response);
                }
            } catch (error) {
                console.error('创建Agent失败:', error);
                throw error;
            }
        }

        async function updateAgent(agentId, agentConfig) {
            try {
                if (AdminAPI && AdminAPI.updateAgent) {
                    return await AdminAPI.updateAgent(agentId, agentConfig);
                } else {
                    const response = await fetch(`/api/agents/${agentId}`, {
                        method: 'PUT',
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify(agentConfig)
                    });

                    return await handleRResponse(response);
                }
            } catch (error) {
                console.error('更新Agent失败:', error);
                throw error;
            }
        }

        async function deleteAgentAPI(agentId) {
            try {
                if (AdminAPI && AdminAPI.deleteAgent) {
                    return await AdminAPI.deleteAgent(agentId);
                } else {
                    const response = await fetch(`/api/agents/${agentId}`, {
                        method: 'DELETE'
                    });

                    if (response.status === 204) {
                        return true;
                    }

                    return await handleRResponse(response);
                }
            } catch (error) {
                console.error('删除Agent失败:', error);
                throw error;
            }
        }

        function showLoading() {
            if (agentGrid) {
                agentGrid.innerHTML = `
                    <div class="loading-placeholder">
                        <i class="bi bi-hourglass-split"></i>
                        <span>加载Agent列表...</span>
                    </div>
                `;
            }
        }

        function showError(message) {
            if (agentGrid) {
                agentGrid.innerHTML = `
                    <div class="loading-placeholder">
                        <i class="bi bi-exclamation-triangle text-danger"></i>
                        <span>${message}</span>
                    </div>
                `;
            }
        }

        function renderAgentList(agents = allAgents) {
            if (!agentGrid) {
                console.error('agentGrid元素未找到');
                return;
            }

            if (!agents || agents.length === 0) {
                agentGrid.innerHTML = `
                    <div class="new-agent-card" onclick="openAgentDrawer()">
                        <i class="bi bi-plus-lg"></i>
                        <h3>创建新的Agent</h3>
                        <p>点击开始配置您的第一个智能代理</p>
                    </div>
                    <div class="empty-grid">
                        <i class="bi bi-robot"></i>
                        <h3>暂无Agent配置</h3>
                        <p>您还没有创建任何智能代理。<br>点击上方的"新建Agent"按钮开始创建。</p>
                    </div>
                `;
                return;
            }

            // 创建新建卡片 + Agent卡片
            const newAgentCard = `
                <div class="new-agent-card" onclick="openAgentDrawer()">
                    <i class="bi bi-plus-lg"></i>
                    <h3>创建新的Agent</h3>
                    <p>配置新的智能代理</p>
                </div>
            `;

            const agentCards = agents.map(agent => `
                <div class="agent-card" data-id="${agent.id}" onclick="selectAgent('${agent.id}')">
                    <div class="agent-card-header">
                        <h3 class="agent-card-title">${escapeHtml(agent.name)}</h3>
                        <span class="agent-card-id">${agent.id}</span>
                    </div>
                    <p class="agent-card-description">${escapeHtml(agent.description || '暂无描述')}</p>
                    <div class="agent-card-tools">
                        ${(agent.availableTools || []).slice(0, 3).map(tool =>
                            `<span class="agent-tool-tag">${escapeHtml(tool)}</span>`
                        ).join('')}
                        ${(agent.availableTools || []).length > 3 ?
                            `<span class="agent-tool-tag">+${(agent.availableTools || []).length - 3}</span>` : ''}
                    </div>
                    <div class="agent-card-actions">
                        <button class="card-action-btn" onclick="event.stopPropagation(); editAgent('${agent.id}')">
                            <i class="bi bi-pencil"></i> 编辑
                        </button>
                        <button class="card-action-btn danger" onclick="event.stopPropagation(); deleteAgent('${agent.id}')">
                            <i class="bi bi-trash"></i> 删除
                        </button>
                    </div>
                </div>
            `).join('');

            agentGrid.innerHTML = newAgentCard + agentCards;
        }

        function openAgentDrawer(agent = null) {
            currentAgent = agent;

            if (configDrawer && drawerOverlay) {
                // 显示抽屉
                drawerOverlay.classList.add('show');
                configDrawer.classList.add('show');

                // 渲染抽屉内容
                renderDrawerContent(agent);
            }
        }

        function closeAgentDrawer() {
            if (configDrawer && drawerOverlay) {
                drawerOverlay.classList.remove('show');
                configDrawer.classList.remove('show');
            }
        }

        function selectAgent(agentId) {
            // 移除之前的选中状态
            document.querySelectorAll('.agent-card').forEach(card => {
                card.classList.remove('selected');
            });

            // 添加当前选中状态
            const selectedCard = document.querySelector(`[data-id="${agentId}"]`);
            if (selectedCard) {
                selectedCard.classList.add('selected');
            }

            // 查找Agent数据
            const agent = allAgents.find(a => a.id === agentId);
            if (agent) {
                openAgentDrawer(agent);
            }
        }

        function editAgent(agentId) {
            const agent = allAgents.find(a => a.id === agentId);
            if (agent) {
                openAgentDrawer(agent);
            }
        }

        function renderDrawerContent(agent) {
            const drawerTitle = document.getElementById('drawerTitle');
            const drawerContent = document.getElementById('drawerContent');
            const drawerFooter = document.getElementById('drawerFooter');

            if (!drawerTitle || !drawerContent || !drawerFooter) {
                console.error('抽屉元素未找到');
                return;
            }

            // 设置标题
            drawerTitle.textContent = agent ? `编辑Agent: ${agent.name}` : '新建Agent';

            // 渲染表单内容
            drawerContent.innerHTML = `
                <form id="agentForm">
                    <div class="mb-4">
                        <label for="agentName" class="form-label">Agent名称 *</label>
                        <input type="text" class="form-control" id="agentName" value="${agent ? escapeHtml(agent.name) : ''}" required>
                    </div>

                    <div class="mb-4">
                        <label for="agentDescription" class="form-label">描述</label>
                        <textarea class="form-control" id="agentDescription" rows="3">${agent ? escapeHtml(agent.description || '') : ''}</textarea>
                    </div>

                    <div class="mb-4">
                        <label for="systemPrompt" class="form-label">系统提示词</label>
                        <textarea class="form-control" id="systemPrompt" rows="4">${agent ? escapeHtml(agent.systemPrompt || '') : ''}</textarea>
                    </div>

                    <div class="mb-4">
                        <label for="nextStepPrompt" class="form-label">下一步提示词</label>
                        <textarea class="form-control" id="nextStepPrompt" rows="3">${agent ? escapeHtml(agent.nextStepPrompt || '') : ''}</textarea>
                    </div>

                    <div class="mb-4">
                        <label for="className" class="form-label">类名</label>
                        <input type="text" class="form-control" id="className" value="${agent ? escapeHtml(agent.className || '') : ''}">
                    </div>

                    <div class="mb-4">
                        <label class="form-label">可用工具</label>
                        <div class="selected-tools-container" id="selectedTools">
                            ${renderSelectedToolsHTML(agent ? agent.availableTools : [])}
                        </div>
                        <button type="button" class="btn btn-outline-primary btn-sm mt-2" onclick="showToolSelectionModal()">
                            <i class="bi bi-plus"></i> 添加工具
                        </button>
                    </div>
                </form>
            `;

            // 渲染底部按钮
            drawerFooter.innerHTML = `
                <button type="button" class="btn btn-secondary" onclick="closeAgentDrawer()">取消</button>
                ${agent ? `<button type="button" class="btn btn-danger me-auto" onclick="deleteAgent('${agent.id}')">删除</button>` : ''}
                <button type="button" class="btn btn-primary" onclick="saveAgent()">保存</button>
            `;

            // 设置选中的工具
            selectedTools = agent ? [...(agent.availableTools || [])] : [];
        }

        function renderSelectedToolsHTML(tools) {
            if (!tools || tools.length === 0) {
                return '<div class="text-muted">暂未选择任何工具</div>';
            }

            return tools.map(toolKey => {
                const tool = allTools.find(t => t.key === toolKey);
                const toolName = tool ? tool.name : toolKey;
                return `
                    <span class="badge bg-primary me-2 mb-2">
                        ${escapeHtml(toolName)}
                        <button type="button" class="btn-close btn-close-white ms-1" onclick="removeTool('${toolKey}')"></button>
                    </span>
                `;
            }).join('');
        }

        function escapeHtml(text) {
            if (!text) return '';
            const div = document.createElement('div');
            div.textContent = text;
            return div.innerHTML;
        }

        async function saveAgent() {
            try {
                const form = document.getElementById('agentForm');
                if (!form) return;

                const formData = new FormData(form);
                const agentData = {
                    name: document.getElementById('agentName').value.trim(),
                    description: document.getElementById('agentDescription').value.trim(),
                    systemPrompt: document.getElementById('systemPrompt').value.trim(),
                    nextStepPrompt: document.getElementById('nextStepPrompt').value.trim(),
                    className: document.getElementById('className').value.trim(),
                    availableTools: selectedTools
                };

                if (!agentData.name) {
                    alert('请输入Agent名称');
                    return;
                }

                let result;
                if (currentAgent) {
                    // 更新现有Agent
                    result = await updateAgent(currentAgent.id, agentData);
                    // 更新本地数据
                    const index = allAgents.findIndex(a => a.id === currentAgent.id);
                    if (index !== -1) {
                        allAgents[index] = { ...currentAgent, ...agentData };
                    }
                } else {
                    // 创建新Agent
                    result = await createAgent(agentData);
                    // 添加到本地数据
                    allAgents.push(result);
                }

                // 重新渲染列表
                renderAgentList();

                // 关闭抽屉
                closeAgentDrawer();

                alert(currentAgent ? 'Agent更新成功' : 'Agent创建成功');

            } catch (error) {
                console.error('保存Agent失败:', error);
                alert('保存失败: ' + error.message);
            }
        }

        async function deleteAgent(agentId) {
            if (!confirm('确定要删除这个Agent吗？此操作不可撤销。')) {
                return;
            }

            try {
                await deleteAgentAPI(agentId);

                // 从本地数据中移除
                allAgents = allAgents.filter(a => a.id !== agentId);

                // 重新渲染列表
                renderAgentList();

                // 关闭抽屉
                closeAgentDrawer();

                alert('Agent删除成功');

            } catch (error) {
                console.error('删除Agent失败:', error);
                alert('删除失败: ' + error.message);
            }
        }

        function removeTool(toolKey) {
            selectedTools = selectedTools.filter(t => t !== toolKey);

            // 重新渲染选中的工具
            const container = document.getElementById('selectedTools');
            if (container) {
                container.innerHTML = renderSelectedToolsHTML(selectedTools);
            }
        }

        function showToolSelectionModal() {
            if (toolSelectionModal) {
                toolSelectionModal.show();
                renderToolGroups();
            }
        }

        function confirmToolSelection() {
            // 获取选中的工具
            const checkboxes = document.querySelectorAll('#toolGroups input[type="checkbox"]:checked');
            selectedTools = Array.from(checkboxes).map(cb => cb.value);

            // 更新显示
            const container = document.getElementById('selectedTools');
            if (container) {
                container.innerHTML = renderSelectedToolsHTML(selectedTools);
            }

            // 关闭模态框
            if (toolSelectionModal) {
                toolSelectionModal.hide();
            }
        }

        function renderToolGroups() {
            // 这里可以实现工具分组渲染逻辑
            // 暂时使用简单的列表
            const toolGroups = document.getElementById('toolGroups');
            if (!toolGroups || !allTools) return;

            const html = allTools.map(tool => `
                <div class="form-check">
                    <input class="form-check-input" type="checkbox" value="${tool.key}"
                           id="tool_${tool.key}" ${selectedTools.includes(tool.key) ? 'checked' : ''}>
                    <label class="form-check-label" for="tool_${tool.key}">
                        <strong>${escapeHtml(tool.name || tool.key)}</strong>
                        ${tool.description ? `<br><small class="text-muted">${escapeHtml(tool.description)}</small>` : ''}
                    </label>
                </div>
            `).join('');

            toolGroups.innerHTML = html;
        }

        function filterAgents(keyword) {
            if (!keyword) {
                renderAgentList();
                return;
            }

            const filtered = allAgents.filter(agent =>
                agent.name.toLowerCase().includes(keyword) ||
                (agent.description && agent.description.toLowerCase().includes(keyword)) ||
                (agent.availableTools && agent.availableTools.some(tool =>
                    tool.toLowerCase().includes(keyword)
                ))
            );

            renderAgentList(filtered);
        }

        // 导入导出功能的占位符
        function importAgents() {
            alert('导入功能开发中...');
        }

        function exportAgents() {
            alert('导出功能开发中...');
        }

        function filterAgents(keyword) {
            if (!keyword) {
                renderAgentList();
                return;
            }

            const filtered = allAgents.filter(agent =>
                agent.name.toLowerCase().includes(keyword) ||
                (agent.description && agent.description.toLowerCase().includes(keyword)) ||
                (agent.availableTools && agent.availableTools.some(tool =>
                    tool.toLowerCase().includes(keyword)
                ))
            );

            renderAgentList(filtered);
        }





        function renderToolGroups() {
            const toolGroups = document.getElementById('toolGroups');
            const toolStatsText = document.getElementById('toolStatsText');
            const sortBy = document.getElementById('sortBySelect').value;
            const searchTerm = toolSearchInput ? toolSearchInput.value.toLowerCase() : '';

            if (!toolGroups) {
                console.error('toolGroups元素未找到');
                return;
            }

            // 过滤工具
            const filteredTools = allTools.filter(tool =>
                tool.name.toLowerCase().includes(searchTerm) ||
                tool.description.toLowerCase().includes(searchTerm) ||
                (tool.serviceGroup && tool.serviceGroup.toLowerCase().includes(searchTerm))
            );

            // 按服务组分组
            const groupedTools = {};
            filteredTools.forEach(tool => {
                const group = tool.serviceGroup || 'default-service-group';
                if (!groupedTools[group]) {
                    groupedTools[group] = [];
                }
                groupedTools[group].push(tool);
            });

            // 排序分组
            const sortedGroups = Object.keys(groupedTools).sort();

            // 更新统计信息
            const groupCount = sortedGroups.length;
            const toolCount = filteredTools.length;
            const selectedCount = selectedTools.length;
            if (toolStatsText) {
                toolStatsText.textContent = `共 ${groupCount} 个服务组，${toolCount} 个工具（已选择 ${selectedCount} 个）`;
            }

            // 清空容器
            toolGroups.innerHTML = '';

            if (filteredTools.length === 0) {
                toolGroups.innerHTML = '<div class="text-center text-muted p-4">没有找到匹配的工具</div>';
                return;
            }

            // 渲染每个分组
            sortedGroups.forEach(groupName => {
                const tools = groupedTools[groupName];
                const groupSelectedCount = tools.filter(tool => selectedTools.includes(tool.key)).length;

                const groupElement = document.createElement('div');
                groupElement.className = 'tool-group';
                groupElement.innerHTML = `
                    <div class="tool-group-header" data-group="${groupName}">
                        <div class="tool-group-title">
                            <i class="bi bi-folder tool-group-icon"></i>
                            <span>${getGroupDisplayName(groupName)}</span>
                            <span class="tool-group-count">${groupSelectedCount}/${tools.length}</span>
                        </div>
                        <div class="d-flex align-items-center gap-3">
                            <div class="group-select-all">
                                <input class="form-check-input" type="checkbox" id="group_${groupName}"
                                    ${groupSelectedCount === tools.length ? 'checked' : ''}>
                                <label class="form-check-label" for="group_${groupName}">启用全部</label>
                            </div>
                            <i class="bi bi-chevron-down tool-group-toggle"></i>
                        </div>
                    </div>
                    <div class="tool-group-content">
                        ${tools.map(tool => renderToolItem(tool)).join('')}
                    </div>
                `;

                // 添加分组头部点击事件
                const header = groupElement.querySelector('.tool-group-header');
                header.addEventListener('click', function(e) {
                    if (e.target.type !== 'checkbox' && !e.target.closest('.form-check')) {
                        groupElement.classList.toggle('collapsed');
                    }
                });

                // 添加分组全选事件
                const groupCheckbox = groupElement.querySelector(`#group_${groupName}`);
                groupCheckbox.addEventListener('change', function() {
                    const toolCheckboxes = groupElement.querySelectorAll('.tool-item input[type="checkbox"]');
                    toolCheckboxes.forEach(checkbox => {
                        checkbox.checked = this.checked;
                        checkbox.dispatchEvent(new Event('change'));
                    });
                });

                // 添加工具选择事件
                const toolCheckboxes = groupElement.querySelectorAll('.tool-item input[type="checkbox"]');
                toolCheckboxes.forEach(checkbox => {
                    checkbox.addEventListener('change', function() {
                        const toolKey = this.value;
                        if (this.checked) {
                            if (!selectedTools.includes(toolKey)) {
                                selectedTools.push(toolKey);
                            }
                        } else {
                            const index = selectedTools.indexOf(toolKey);
                            if (index > -1) {
                                selectedTools.splice(index, 1);
                            }
                        }

                        // 更新分组选择状态
                        updateGroupCheckboxState(groupElement, groupName);
                        // 更新统计
                        updateToolStats();
                    });
                });

                toolGroups.appendChild(groupElement);
            });
        }

        function renderToolItem(tool) {
            const isSelected = selectedTools.includes(tool.key);
            return `
                <div class="tool-item ${isSelected ? 'selected' : ''}">
                    <div class="tool-info">
                        <div class="tool-name">${escapeHtml(tool.name)}</div>
                        <div class="tool-description">${escapeHtml(tool.description)}</div>
                    </div>
                    <div class="tool-toggle">
                        <input class="form-check-input" type="checkbox" value="${tool.key}"
                            id="tool_${tool.key}" ${isSelected ? 'checked' : ''}>
                    </div>
                </div>
            `;
        }

        function getGroupDisplayName(groupName) {
            // 直接返回原始的serviceGroup名称，不做任何映射
            return groupName || 'default-service-group';
        }

        function updateGroupCheckboxState(groupElement, groupName) {
            const toolCheckboxes = groupElement.querySelectorAll('.tool-item input[type="checkbox"]');
            const checkedCount = Array.from(toolCheckboxes).filter(cb => cb.checked).length;
            const groupCheckbox = groupElement.querySelector(`#group_${groupName}`);
            const countSpan = groupElement.querySelector('.tool-group-count');

            if (groupCheckbox) {
                groupCheckbox.checked = checkedCount === toolCheckboxes.length;
                groupCheckbox.indeterminate = checkedCount > 0 && checkedCount < toolCheckboxes.length;
            }

            if (countSpan) {
                countSpan.textContent = `${checkedCount}/${toolCheckboxes.length}`;
            }
        }

        function updateToolStats() {
            const toolStatsText = document.getElementById('toolStatsText');
            if (toolStatsText) {
                const groupCount = document.querySelectorAll('.tool-group').length;
                const selectedCount = selectedTools.length;
                const totalCount = allTools.length;
                toolStatsText.textContent = `共 ${groupCount} 个服务组，${totalCount} 个工具（已选择 ${selectedCount} 个）`;
            }
        }

        function filterTools(keyword) {
            renderToolGroups();
        }





        function importAgents() {
            const input = document.createElement('input');
            input.type = 'file';
            input.accept = '.json';
            input.onchange = function(event) {
                const file = event.target.files[0];
                if (!file) return;

                const reader = new FileReader();
                reader.onload = function(e) {
                    try {
                        const importedAgents = JSON.parse(e.target.result);
                        if (!Array.isArray(importedAgents)) {
                            throw new Error('导入文件格式错误');
                        }

                        console.log('导入Agent配置:', importedAgents.length);
                        showSuccessMessage(`准备导入 ${importedAgents.length} 个Agent配置`);

                        // 这里可以添加批量导入的逻辑

                    } catch (error) {
                        console.error('导入失败:', error);
                        showErrorMessage('导入失败: ' + error.message);
                    }
                };
                reader.readAsText(file);
            };
            input.click();
        }

        function exportAgents() {
            try {
                const dataStr = JSON.stringify(allAgents, null, 2);
                const dataBlob = new Blob([dataStr], {type: 'application/json'});

                const link = document.createElement('a');
                link.href = URL.createObjectURL(dataBlob);
                link.download = `agents-export-${new Date().toISOString().split('T')[0]}.json`;
                link.click();

                showSuccessMessage('Agent配置导出成功');
                console.log('导出Agent配置:', allAgents.length);

            } catch (error) {
                console.error('导出失败:', error);
                showErrorMessage('导出失败: ' + error.message);
            }
        }

        function showSuccessMessage(message) {
            // 简单的成功提示，可以替换为更好的UI组件
            const toast = document.createElement('div');
            toast.className = 'alert alert-success position-fixed';
            toast.style.cssText = 'top: 80px; right: 20px; z-index: 9999; min-width: 300px;';
            toast.innerHTML = `
                <i class="bi bi-check-circle"></i>
                ${escapeHtml(message)}
            `;
            document.body.appendChild(toast);

            setTimeout(() => {
                toast.remove();
            }, 3000);
        }

        function showErrorMessage(message) {
            // 简单的错误提示，可以替换为更好的UI组件
            const toast = document.createElement('div');
            toast.className = 'alert alert-danger position-fixed';
            toast.style.cssText = 'top: 80px; right: 20px; z-index: 9999; min-width: 300px;';
            toast.innerHTML = `
                <i class="bi bi-exclamation-triangle"></i>
                ${escapeHtml(message)}
            `;
            document.body.appendChild(toast);

            setTimeout(() => {
                toast.remove();
            }, 5000);
        }

        function showDebugInfo() {
            const debugInfo = {
                '页面状态': {
                    'currentAgent': currentAgent ? currentAgent.name : 'null',
                    'isEditing': isEditing,
                    'selectedTools': selectedTools.length,
                    'allAgents': allAgents.length,
                    'allTools': allTools.length
                },
                'DOM元素状态': {
                    'agentList': !!agentList,
                    'emptyState': !!emptyState,
                    'detailForm': !!detailForm,
                    'agentName': !!agentName,
                    'agentDescription': !!agentDescription,
                    'nextStepPrompt': !!nextStepPrompt,
                    'selectedToolsContainer': !!selectedToolsContainer,
                    'toolSelectionModal': !!toolSelectionModal
                },
                '显示状态': {
                    'emptyState.display': emptyState ? emptyState.style.display : 'null',
                    'detailForm.display': detailForm ? detailForm.style.display : 'null'
                },
                'API状态': {
                    'AdminAPI可用': typeof AdminAPI !== 'undefined',
                    'fetch可用': typeof fetch !== 'undefined',
                    'bootstrap可用': typeof bootstrap !== 'undefined'
                }
            };

            console.log('=== 调试信息 ===');
            console.table(debugInfo);

            alert('调试信息已输出到控制台，请按F12查看');
        }


    </script>
</body>
</html>
