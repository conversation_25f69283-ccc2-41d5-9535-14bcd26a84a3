/* 基础样式 */
* {
    box-sizing: border-box;
    margin: 0;
    padding: 0;
}

body {
    font-family: 'PingFang SC', 'Microsoft YaHei', sans-serif;
    line-height: 1.6;
    color: #333;
    background-color: #f5f5f5;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
}

/* 头部样式 */
header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding-bottom: 10px;
    border-bottom: 1px solid #ddd;
}

header h1 {
    font-size: 24px;
    color: #2c3e50;
}

.connection-status {
    display: flex;
    align-items: center;
}

.status-indicator {
    display: inline-block;
    width: 12px;
    height: 12px;
    border-radius: 50%;
    margin-right: 8px;
}

.status-disconnected {
    background-color: #e74c3c;
    box-shadow: 0 0 5px #e74c3c;
}

.status-connected {
    background-color: #2ecc71;
    box-shadow: 0 0 5px #2ecc71;
}

.status-connecting {
    background-color: #f39c12;
    box-shadow: 0 0 5px #f39c12;
    animation: blink 1s infinite;
}

@keyframes blink {
    0% { opacity: 0.5; }
    50% { opacity: 1; }
    100% { opacity: 0.5; }
}

/* 控制面板样式 */
.control-panel {
    background-color: #fff;
    border-radius: 5px;
    padding: 15px;
    margin-bottom: 20px;
    box-shadow: 0 2px 5px rgba(0,0,0,0.1);
}

.input-group {
    display: flex;
    align-items: center;
    margin-bottom: 15px;
}

.input-group label {
    margin-right: 10px;
    font-weight: bold;
}

.input-group input {
    flex: 1;
    padding: 8px 12px;
    border: 1px solid #ddd;
    border-radius: 4px;
    margin-right: 10px;
}

.filter-group {
    margin-bottom: 15px;
}

.filter-group label {
    font-weight: bold;
    display: block;
    margin-bottom: 5px;
}

.checkbox-group {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
}

.checkbox-group label {
    font-weight: normal;
    display: flex;
    align-items: center;
}

.checkbox-group input {
    margin-right: 5px;
}

.action-group {
    display: flex;
    gap: 10px;
}

button {
    padding: 8px 15px;
    background-color: #3498db;
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    transition: background-color 0.3s;
}

button:hover {
    background-color: #2980b9;
}

button:disabled {
    background-color: #95a5a6;
    cursor: not-allowed;
}

#disconnect-btn {
    background-color: #e74c3c;
}

#disconnect-btn:hover {
    background-color: #c0392b;
}

#clear-btn {
    background-color: #f39c12;
}

#clear-btn:hover {
    background-color: #d35400;
}

/* 事件容器样式 */
.event-container {
    background-color: #fff;
    border-radius: 5px;
    margin-bottom: 20px;
    box-shadow: 0 2px 5px rgba(0,0,0,0.1);
    overflow: hidden;
}

.event-header {
    display: grid;
    grid-template-columns: 180px 150px 1fr;
    background-color: #34495e;
    color: white;
    padding: 10px 15px;
    font-weight: bold;
}

.event-log {
    height: 300px;
    overflow-y: auto;
    padding: 0;
}

.event-item {
    display: grid;
    grid-template-columns: 180px 150px 1fr;
    padding: 8px 15px;
    border-bottom: 1px solid #eee;
    cursor: pointer;
    transition: background-color 0.2s;
}

.event-item:hover {
    background-color: #f9f9f9;
}

.event-item.selected {
    background-color: #ecf0f1;
}

.event-time {
    color: #7f8c8d;
    font-size: 0.9em;
}

.event-type {
    font-weight: bold;
}

.event-type.plan-start { color: #3498db; }
.event-type.step-start { color: #9b59b6; }
.event-type.agent-start { color: #2ecc71; }
.event-type.think-act-start { color: #f39c12; }
.event-type.think-act-complete { color: #e67e22; }
.event-type.agent-complete { color: #27ae60; }
.event-type.step-complete { color: #8e44ad; }
.event-type.plan-complete { color: #2980b9; }

.event-content {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

/* 详情面板样式 */
.detail-panel {
    background-color: #fff;
    border-radius: 5px;
    padding: 15px;
    box-shadow: 0 2px 5px rgba(0,0,0,0.1);
}

.detail-panel h3 {
    margin-bottom: 10px;
    color: #2c3e50;
    font-size: 18px;
}

#event-detail {
    background-color: #f9f9f9;
    padding: 15px;
    border-radius: 4px;
    overflow-x: auto;
    white-space: pre-wrap;
    font-family: 'Courier New', monospace;
    font-size: 14px;
    max-height: 300px;
    overflow-y: auto;
}
