/* sidebar.css - 左侧边栏样式 */

/* 通用侧边栏样式 */
.sidebar {
    background-color: #f7f7f7; 
    border: 1px solid #e0e0e0; 
    display: flex;
    flex-direction: column;
    height: 100vh; /* 全高 */
    flex-shrink: 0; /* 防止收缩 */
    overflow: hidden; /* 收起时隐藏内容 */
    transition: all 0.3s ease-in-out; /* 平滑过渡 */
    position: relative; /* 需要用于内部绝对定位 */
}

/* 左侧边栏特定样式 */
.left-sidebar {
    width: 260px;
    border-right: 1px solid #e0e0e0;
    border-left: none;
    border-top: none;
    border-bottom: none;
    padding: 15px 8px;
    transform: translateX(0);
    transition: all 0.3s ease-in-out;
}

/* 左侧边栏收起状态 */
.left-sidebar.collapsed {
    width: 0;
    padding: 0;
    margin: 0;
    border: none;
    transform: translateX(-100%);
}

/* 顶部图标 */
.sidebar-top-icons {
    display: flex;
    justify-content: space-around;
    padding: 10px 0;
    margin-bottom: 15px;
}

.sidebar-top-icons .icon {
    font-size: 18px;
    color: #555;
}

/* 新建任务按钮 */
.new-task-btn {
    display: flex;
    align-items: center;
    justify-content: space-between; /* 快捷键右对齐 */
    background-color: #fff;
    border: 1px solid #dcdcdc;
    border-radius: 6px;
    padding: 8px 12px;
    font-size: 14px;
    font-weight: 500;
    color: #333;
    cursor: pointer;
    text-align: left;
    width: 100%;
    margin-bottom: 15px;
    transition: background-color 0.2s ease;
    white-space: nowrap; /* 收起时防止换行 */
}

.new-task-btn:hover {
    background-color: #f5f5f5;
}

.new-task-btn .icon-add {
    font-size: 16px;
    margin-right: 8px;
    font-weight: bold;
}

.new-task-btn .shortcut {
    color: #888;
    font-size: 12px;
}

/* 任务列表 */
.task-list {
    list-style: none;
    flex-grow: 1;
    overflow-y: auto;
    margin: 0 -8px;
    padding: 0 8px;
}

.task-item {
    display: flex;
    align-items: center;
    padding: 8px 8px;
    margin-bottom: 2px;
    border-radius: 6px;
    cursor: pointer;
    transition: background-color 0.2s ease;
    white-space: nowrap; /* 收起时防止换行 */
    position: relative; /* 添加相对定位，使绝对定位的子元素能够相对于它定位 */
}

.task-item:hover {
    background-color: #ededed;
}

.task-item.selected {
    background-color: #e6e6e6;
}

.task-icon {
    margin-right: 10px;
    font-size: 16px;
    flex-shrink: 0;
    width: 24px;
    text-align: center;
}

.task-details {
    flex-grow: 1;
    overflow: hidden;
}

.task-title {
    font-weight: 500;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    font-size: 13px;
}

.task-preview {
    font-size: 11px;
    color: #666;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.task-time {
    font-size: 10px;
    color: #888;
    margin-left: 10px;
    white-space: nowrap;
    flex-shrink: 0;
}

/* 侧边栏底部 - 用户资料 */
.sidebar-bottom {
    margin-top: auto; /* 推至底部 */
    padding-top: 10px;
    border-top: 1px solid #e0e0e0;
}

.user-profile-stub {
    display: flex;
    align-items: center;
    padding: 5px 8px;
    font-size: 13px;
    white-space: nowrap;
    overflow: hidden;
}

.user-avatar {
    width: 24px;
    height: 24px;
    border-radius: 50%;
    margin-right: 8px;
    flex-shrink: 0;
}

.user-profile-stub span {
    flex-grow: 1;
    overflow: hidden;
    text-overflow: ellipsis;
}

.user-profile-stub button {
    background: none;
    border: none;
    cursor: pointer;
    font-size: 16px;
    color: #555;
    padding: 0 5px;
    margin-left: 5px;
}
