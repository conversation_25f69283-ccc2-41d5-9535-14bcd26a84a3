/* input.css - 输入区域样式 */

/* 输入区域 */
.input-area {
    padding: 12px 24px;
    border-top: 1px solid #e0e0e0;
    background-color: #fcfcfc;
    display: flex;
    align-items: center;
    gap: 10px;
}

/* 输入框 */
.input-area input[type="text"] {
    flex-grow: 1;
    padding: 10px 15px;
    border: 1px solid #dcdcdc;
    border-radius: 20px; /* 胶囊形状 */
    font-size: 14px;
    outline: none;
}

.input-area input[type="text"]:focus {
    border-color: #a0a0a0;
}

/* 按钮通用样式 */
.input-area button {
    background: none;
    border: none;
    cursor: pointer;
    font-size: 14px; /* 调整图标大小 */
    color: #666;
    padding: 5px;
    flex-shrink: 0;
}

.input-area button:hover {
    color: #333;
}

/* 附件按钮 */
.attach-btn {
    /* 如需要添加特定样式 */
}

/* 发送按钮 */
.send-btn {
    /* 如需要添加特定样式 */
}

/* 计划模式按钮 */
.plan-mode-btn, .stream-mode-btn {
    background-color: #f0f8ff;
    color: #1a73e8;
    border: 1px solid #1a73e8;
    border-radius: 4px;
    padding: 6px 12px;
    margin-right: 10px;
    cursor: pointer;
    transition: background-color 0.3s;
}

.plan-mode-btn:hover, .stream-mode-btn:hover {
    background-color: #e6f2ff;
}

/* 流式执行按钮 */
.stream-mode-btn {
    background-color: #f0fff0;
    color: #28a745;
    border: 1px solid #28a745;
}

.stream-mode-btn:hover {
    background-color: #e6ffe6;
}
