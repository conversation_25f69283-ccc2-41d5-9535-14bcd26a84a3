<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Agent配置管理</title>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css" rel="stylesheet">
    <style>
        :root {
            --primary-color: #6366f1;
            --primary-hover: #4f46e5;
            --primary-light: #e0e7ff;
            --secondary-color: #64748b;
            --success-color: #10b981;
            --success-light: #d1fae5;
            --danger-color: #ef4444;
            --danger-light: #fee2e2;
            --warning-color: #f59e0b;
            --warning-light: #fef3c7;
            --info-color: #06b6d4;
            --info-light: #cffafe;
            --gray-50: #f9fafb;
            --gray-100: #f3f4f6;
            --gray-200: #e5e7eb;
            --gray-300: #d1d5db;
            --gray-400: #9ca3af;
            --gray-500: #6b7280;
            --gray-600: #4b5563;
            --gray-700: #374151;
            --gray-800: #1f2937;
            --gray-900: #111827;
            --border-radius: 12px;
            --border-radius-lg: 16px;
            --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
            --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
            --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
            --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
        }

        * {
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            margin: 0;
            padding: 0;
            min-height: 100vh;
            color: var(--gray-900);
            line-height: 1.6;
        }

        .app-container {
            min-height: 100vh;
            display: flex;
            flex-direction: column;
            background: var(--gray-50);
        }

        /* 顶部导航栏 */
        .app-header {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-bottom: 1px solid var(--gray-200);
            padding: 0 32px;
            height: 72px;
            display: flex;
            align-items: center;
            justify-content: space-between;
            box-shadow: var(--shadow-sm);
            position: sticky;
            top: 0;
            z-index: 100;
        }

        .app-title {
            display: flex;
            align-items: center;
            gap: 12px;
            font-size: 1.75rem;
            font-weight: 700;
            color: var(--gray-900);
            letter-spacing: -0.025em;
        }

        .app-title i {
            font-size: 2rem;
            color: var(--primary-color);
            background: var(--primary-light);
            padding: 8px;
            border-radius: var(--border-radius);
        }

        .header-actions {
            display: flex;
            gap: 12px;
        }

        .action-btn {
            background: white;
            color: var(--gray-700);
            border: 1px solid var(--gray-300);
            padding: 10px 16px;
            border-radius: var(--border-radius);
            font-size: 0.875rem;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
            display: flex;
            align-items: center;
            gap: 8px;
            text-decoration: none;
            box-shadow: var(--shadow-sm);
        }

        .action-btn:hover {
            background: var(--gray-50);
            border-color: var(--gray-400);
            transform: translateY(-1px);
            box-shadow: var(--shadow-md);
            color: var(--gray-700);
        }

        .action-btn.primary {
            background: var(--primary-color);
            color: white;
            border-color: var(--primary-color);
        }

        .action-btn.primary:hover {
            background: var(--primary-hover);
            border-color: var(--primary-hover);
            color: white;
        }

        /* 主内容区域 */
        .main-content {
            flex: 1;
            background: var(--gray-50);
            min-height: calc(100vh - 72px);
            padding: 32px;
        }

        /* 顶部操作栏 */
        .content-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 32px;
        }

        .content-title {
            font-size: 2rem;
            font-weight: 800;
            color: var(--gray-900);
            letter-spacing: -0.025em;
        }

        .content-actions {
            display: flex;
            gap: 12px;
            align-items: center;
        }

        .search-bar {
            position: relative;
            width: 320px;
        }

        .search-bar input {
            width: 100%;
            padding: 12px 16px 12px 44px;
            border: 1px solid var(--gray-300);
            border-radius: var(--border-radius);
            font-size: 0.875rem;
            background: white;
            box-shadow: var(--shadow-sm);
            transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
        }

        .search-bar input:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px var(--primary-light);
        }

        .search-bar i {
            position: absolute;
            left: 16px;
            top: 50%;
            transform: translateY(-50%);
            color: var(--gray-400);
        }

        /* Agent卡片网格布局 */
        .agent-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(380px, 1fr));
            gap: 24px;
            margin-bottom: 32px;
        }

        .agent-card {
            background: white;
            border: 1px solid var(--gray-200);
            border-radius: var(--border-radius-lg);
            padding: 24px;
            box-shadow: var(--shadow-sm);
            transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
            cursor: pointer;
            position: relative;
            overflow: hidden;
        }

        .agent-card:hover {
            border-color: var(--primary-color);
            box-shadow: var(--shadow-xl);
            transform: translateY(-4px);
        }

        .agent-card.selected {
            border-color: var(--primary-color);
            background: var(--primary-light);
            box-shadow: var(--shadow-xl);
        }

        .agent-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: var(--primary-color);
            opacity: 0;
            transition: opacity 0.2s ease;
        }

        .agent-card.selected::before,
        .agent-card:hover::before {
            opacity: 1;
        }

        .agent-card-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 16px;
        }

        .agent-card-title {
            font-size: 1.25rem;
            font-weight: 700;
            color: var(--gray-900);
            letter-spacing: -0.025em;
            margin: 0;
        }

        .agent-card-id {
            font-size: 0.75rem;
            color: var(--gray-500);
            background: var(--gray-100);
            padding: 4px 8px;
            border-radius: 6px;
            font-weight: 500;
            font-family: 'JetBrains Mono', 'Fira Code', 'Courier New', monospace;
        }

        .agent-card-description {
            font-size: 0.875rem;
            color: var(--gray-600);
            line-height: 1.5;
            margin-bottom: 16px;
            display: -webkit-box;
            -webkit-line-clamp: 3;
            -webkit-box-orient: vertical;
            overflow: hidden;
        }

        .agent-card-tools {
            display: flex;
            flex-wrap: wrap;
            gap: 6px;
            margin-bottom: 16px;
        }

        .agent-tool-tag {
            background: var(--primary-light);
            color: var(--primary-color);
            padding: 4px 8px;
            border-radius: 16px;
            font-size: 0.75rem;
            font-weight: 500;
        }

        .agent-card-actions {
            display: flex;
            gap: 8px;
            justify-content: flex-end;
        }

        .card-action-btn {
            padding: 6px 12px;
            border: 1px solid var(--gray-300);
            border-radius: 6px;
            background: white;
            color: var(--gray-600);
            font-size: 0.75rem;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .card-action-btn:hover {
            border-color: var(--primary-color);
            color: var(--primary-color);
        }

        .card-action-btn.danger:hover {
            border-color: var(--danger-color);
            color: var(--danger-color);
        }

        /* 新建Agent卡片 */
        .new-agent-card {
            background: linear-gradient(135deg, var(--primary-color), var(--primary-hover));
            border: 2px dashed var(--primary-color);
            border-radius: var(--border-radius-lg);
            padding: 40px 24px;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
            color: white;
            text-align: center;
            min-height: 200px;
        }

        .new-agent-card:hover {
            transform: translateY(-4px);
            box-shadow: var(--shadow-xl);
        }

        .new-agent-card i {
            font-size: 3rem;
            margin-bottom: 16px;
            opacity: 0.9;
        }

        .new-agent-card h3 {
            font-size: 1.25rem;
            font-weight: 700;
            margin: 0 0 8px 0;
        }

        .new-agent-card p {
            font-size: 0.875rem;
            opacity: 0.9;
            margin: 0;
        }

        /* 侧边抽屉 */
        .drawer-overlay {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0, 0, 0, 0.5);
            z-index: 1000;
            opacity: 0;
            visibility: hidden;
            transition: all 0.3s ease;
        }

        .drawer-overlay.show {
            opacity: 1;
            visibility: visible;
        }

        .config-drawer {
            position: fixed;
            top: 0;
            right: 0;
            width: 700px;
            height: 100vh;
            background: white;
            box-shadow: var(--shadow-xl);
            z-index: 1001;
            transform: translateX(100%);
            transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            display: flex;
            flex-direction: column;
        }

        .config-drawer.show {
            transform: translateX(0);
        }

        .drawer-header {
            padding: 24px 32px;
            border-bottom: 1px solid var(--gray-200);
            background: var(--gray-50);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .drawer-title {
            font-size: 1.5rem;
            font-weight: 700;
            color: var(--gray-900);
            margin: 0;
        }

        .drawer-close {
            background: none;
            border: none;
            font-size: 1.5rem;
            color: var(--gray-500);
            cursor: pointer;
            padding: 8px;
            border-radius: var(--border-radius);
            transition: all 0.2s ease;
        }

        .drawer-close:hover {
            background: var(--gray-200);
            color: var(--gray-700);
        }

        .drawer-content {
            flex: 1;
            padding: 32px;
            overflow-y: auto;
        }

        .drawer-footer {
            padding: 24px 32px;
            border-top: 1px solid var(--gray-200);
            background: var(--gray-50);
            display: flex;
            gap: 12px;
            justify-content: flex-end;
        }

        /* 表单样式 */
        .form-section {
            margin-bottom: 40px;
        }

        .section-title {
            display: flex;
            align-items: center;
            gap: 12px;
            font-size: 1.25rem;
            font-weight: 700;
            color: var(--gray-900);
            margin-bottom: 24px;
            padding-bottom: 12px;
            border-bottom: 2px solid var(--gray-200);
            letter-spacing: -0.025em;
        }

        .section-title i {
            color: var(--primary-color);
            font-size: 1.375rem;
            background: var(--primary-light);
            padding: 8px;
            border-radius: var(--border-radius);
        }

        .form-group {
            margin-bottom: 24px;
        }

        .form-label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: var(--gray-900);
            font-size: 0.875rem;
        }

        .form-input, .form-select, .form-textarea {
            width: 100%;
            padding: 12px 16px;
            border: 1px solid var(--gray-300);
            border-radius: var(--border-radius);
            font-size: 0.875rem;
            transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
            background: white;
            box-shadow: var(--shadow-sm);
        }

        .form-input:focus, .form-select:focus, .form-textarea:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px var(--primary-light);
        }

        .form-textarea {
            resize: vertical;
            min-height: 120px;
            font-family: 'JetBrains Mono', 'Fira Code', 'Courier New', monospace;
            line-height: 1.5;
        }

        .form-textarea.large {
            min-height: 200px;
        }

        .text-muted {
            color: var(--gray-500) !important;
            font-size: 0.8rem;
            margin-top: 6px;
        }

        /* 按钮样式 */
        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: var(--border-radius);
            font-weight: 600;
            cursor: pointer;
            transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
            font-size: 0.875rem;
            display: inline-flex;
            align-items: center;
            gap: 8px;
            box-shadow: var(--shadow-sm);
        }

        .btn-primary {
            background: var(--primary-color);
            color: white;
            border: 1px solid var(--primary-color);
        }

        .btn-primary:hover {
            background: var(--primary-hover);
            border-color: var(--primary-hover);
            transform: translateY(-2px);
            box-shadow: var(--shadow-lg);
        }

        .btn-danger {
            background: var(--danger-color);
            color: white;
            border: 1px solid var(--danger-color);
        }

        .btn-danger:hover {
            background: #dc2626;
            border-color: #dc2626;
            transform: translateY(-2px);
            box-shadow: var(--shadow-lg);
        }

        .btn-secondary {
            background: white;
            color: var(--gray-700);
            border: 1px solid var(--gray-300);
        }

        .btn-secondary:hover {
            background: var(--gray-50);
            border-color: var(--gray-400);
            transform: translateY(-1px);
        }

        .btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none !important;
        }

        .btn-sm {
            padding: 8px 16px;
            font-size: 0.8rem;
        }

        /* 工具选择相关样式 */
        .tools-section {
            border: 1px solid var(--gray-200);
            border-radius: var(--border-radius);
            padding: 20px;
            background: var(--gray-50);
        }

        .tools-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }

        .tools-title {
            font-weight: 600;
            color: var(--gray-900);
        }

        .add-tool-btn {
            background: var(--primary-color);
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: var(--border-radius);
            font-size: 0.875rem;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s ease;
            display: flex;
            align-items: center;
            gap: 6px;
        }

        .add-tool-btn:hover {
            background: var(--primary-hover);
            transform: translateY(-1px);
        }

        .selected-tools {
            display: flex;
            flex-wrap: wrap;
            gap: 8px;
            min-height: 40px;
            align-items: flex-start;
        }

        .selected-tool-item {
            background: var(--primary-light);
            color: var(--primary-color);
            padding: 6px 12px;
            border-radius: 20px;
            font-size: 0.875rem;
            font-weight: 500;
            display: flex;
            align-items: center;
            gap: 6px;
        }

        .remove-tool-btn {
            background: none;
            border: none;
            color: var(--primary-color);
            cursor: pointer;
            padding: 0;
            font-size: 1rem;
            line-height: 1;
        }

        .remove-tool-btn:hover {
            color: var(--danger-color);
        }

        .empty-tools {
            color: var(--gray-500);
            font-style: italic;
            padding: 12px 0;
        }

        /* 工具选择模态框样式 */
        .tool-modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0, 0, 0, 0.5);
            z-index: 2000;
            display: flex;
            align-items: center;
            justify-content: center;
            opacity: 0;
            visibility: hidden;
            transition: all 0.3s ease;
        }

        .tool-modal.show {
            opacity: 1;
            visibility: visible;
        }

        .tool-modal-content {
            background: white;
            border-radius: var(--border-radius-lg);
            box-shadow: var(--shadow-xl);
            width: 90%;
            max-width: 1000px;
            max-height: 80vh;
            display: flex;
            flex-direction: column;
            transform: scale(0.9);
            transition: transform 0.3s ease;
        }

        .tool-modal.show .tool-modal-content {
            transform: scale(1);
        }

        .tool-modal-header {
            padding: 24px 32px;
            border-bottom: 1px solid var(--gray-200);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .tool-modal-title {
            font-size: 1.5rem;
            font-weight: 700;
            color: var(--gray-900);
            margin: 0;
        }

        .tool-modal-close {
            background: none;
            border: none;
            font-size: 1.5rem;
            color: var(--gray-500);
            cursor: pointer;
            padding: 8px;
            border-radius: var(--border-radius);
            transition: all 0.2s ease;
        }

        .tool-modal-close:hover {
            background: var(--gray-200);
            color: var(--gray-700);
        }

        .tool-modal-body {
            flex: 1;
            padding: 32px;
            overflow-y: auto;
        }

        .tool-controls {
            display: flex;
            gap: 16px;
            margin-bottom: 24px;
            align-items: center;
        }

        .tool-search {
            flex: 1;
            position: relative;
        }

        .tool-search input {
            width: 100%;
            padding: 10px 16px 10px 40px;
            border: 1px solid var(--gray-300);
            border-radius: var(--border-radius);
            font-size: 0.875rem;
        }

        .tool-search i {
            position: absolute;
            left: 14px;
            top: 50%;
            transform: translateY(-50%);
            color: var(--gray-400);
        }

        .tool-groups {
            display: grid;
            gap: 24px;
        }

        .tool-group {
            border: 1px solid var(--gray-200);
            border-radius: var(--border-radius);
            overflow: hidden;
            margin-bottom: 16px;
        }

        .tool-group-header {
            background: var(--gray-50);
            padding: 16px 20px;
            border-bottom: 1px solid var(--gray-200);
            font-weight: 600;
            color: var(--gray-900);
            cursor: pointer;
            display: flex;
            justify-content: space-between;
            align-items: center;
            transition: all 0.2s ease;
            user-select: none;
        }

        .tool-group-header:hover {
            background: var(--gray-100);
        }

        .tool-group-title {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .tool-group-count {
            background: var(--primary-light);
            color: var(--primary-color);
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 0.75rem;
            font-weight: 500;
        }

        .tool-group-toggle {
            color: var(--gray-500);
            font-size: 1.2rem;
            transition: transform 0.2s ease;
        }

        .tool-group.collapsed .tool-group-toggle {
            transform: rotate(-90deg);
        }

        .tool-group.collapsed .tool-group-header {
            border-bottom: none;
        }

        .tool-group-items {
            padding: 16px 20px;
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 12px;
            transition: all 0.3s ease;
            overflow: hidden;
        }

        .tool-group.collapsed .tool-group-items {
            max-height: 0;
            padding: 0 20px;
            opacity: 0;
        }

        .tool-item {
            display: flex;
            align-items: center;
            gap: 12px;
            padding: 12px;
            border: 1px solid var(--gray-200);
            border-radius: var(--border-radius);
            background: white;
            transition: all 0.2s ease;
        }

        .tool-item:hover {
            border-color: var(--primary-color);
            background: var(--primary-light);
        }

        .tool-item input[type="checkbox"] {
            margin: 0;
        }

        .tool-item-info {
            flex: 1;
        }

        .tool-item-name {
            font-weight: 600;
            color: var(--gray-900);
            margin-bottom: 4px;
        }

        .tool-item-description {
            font-size: 0.875rem;
            color: var(--gray-600);
        }

        .tool-modal-footer {
            padding: 24px 32px;
            border-top: 1px solid var(--gray-200);
            background: var(--gray-50);
            display: flex;
            gap: 12px;
            justify-content: flex-end;
        }
    </style>
</head>
<body>
    <div class="app-container">
        <!-- 顶部导航栏 -->
        <div class="app-header">
            <div class="app-title">
                <i class="bi bi-robot"></i>
                <span>Agent配置管理</span>
            </div>
            <div class="header-actions">
                <button class="action-btn" id="debugBtn" title="调试信息">
                    <i class="bi bi-bug"></i>
                    <span>调试</span>
                </button>
                <a href="/" class="action-btn">
                    <i class="bi bi-arrow-left"></i>
                    <span>返回</span>
                </a>
                <a href="/mcp-config.html" class="action-btn primary">
                    <i class="bi bi-server"></i>
                    <span>MCP管理</span>
                </a>
            </div>
        </div>

        <!-- 主内容区域 -->
        <div class="main-content">
            <!-- 顶部操作栏 -->
            <div class="content-header">
                <h1 class="content-title">智能代理</h1>
                <div class="content-actions">
                    <div class="search-bar">
                        <i class="bi bi-search"></i>
                        <input type="text" id="searchInput" placeholder="搜索Agent...">
                    </div>
                    <button class="btn btn-primary" id="newAgentBtn">
                        <i class="bi bi-plus-lg"></i>
                        <span>新建Agent</span>
                    </button>
                </div>
            </div>

            <!-- Agent卡片网格 -->
            <div class="agent-grid" id="agentGrid">
                <div class="loading-placeholder">
                    <i class="bi bi-hourglass-split"></i>
                    <span>加载Agent列表...</span>
                </div>
            </div>
        </div>

        <!-- 配置抽屉 -->
        <div class="drawer-overlay" id="drawerOverlay"></div>
        <div class="config-drawer" id="configDrawer">
            <div class="drawer-header">
                <h2 class="drawer-title" id="drawerTitle">Agent配置</h2>
                <button class="drawer-close" id="drawerClose">
                    <i class="bi bi-x"></i>
                </button>
            </div>
            <div class="drawer-content" id="drawerContent">
                <!-- 配置表单将在这里动态生成 -->
            </div>
            <div class="drawer-footer" id="drawerFooter">
                <!-- 操作按钮将在这里动态生成 -->
            </div>
        </div>

        <!-- 工具选择模态框 -->
        <div class="tool-modal" id="toolModal">
            <div class="tool-modal-content">
                <div class="tool-modal-header">
                    <h3 class="tool-modal-title">
                        <i class="bi bi-tools"></i>
                        <span>选择工具</span>
                    </h3>
                    <button class="tool-modal-close" id="toolModalClose">
                        <i class="bi bi-x"></i>
                    </button>
                </div>
                <div class="tool-modal-body">
                    <!-- 搜索和控制区域 -->
                    <div class="tool-controls">
                        <div class="tool-search">
                            <i class="bi bi-search"></i>
                            <input type="text" id="toolSearchInput" placeholder="搜索工具...">
                        </div>
                        <div class="form-check form-switch">
                            <input class="form-check-input" type="checkbox" id="selectAllTools">
                            <label class="form-check-label" for="selectAllTools">
                                全选
                            </label>
                        </div>
                    </div>

                    <!-- 工具分组列表 -->
                    <div class="tool-groups" id="toolGroups">
                        <!-- 工具分组将在这里动态生成 -->
                    </div>
                </div>
                <div class="tool-modal-footer">
                    <button class="btn btn-secondary" id="cancelToolSelection">取消</button>
                    <button class="btn btn-primary" id="confirmToolSelection">确认选择</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 引入JavaScript -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // 全局变量
        let currentAgent = null;
        let allAgents = [];
        let allTools = [];
        let selectedTools = [];
        let isEditing = false;

        // DOM元素
        let agentGrid, searchInput, newAgentBtn, configDrawer, drawerOverlay, drawerClose;
        let toolModal, toolModalClose, toolGroups, toolSearchInput;

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            console.log('Agent配置页面开始初始化...');

            try {
                initializeElements();
                setupEventListeners();
                loadInitialData();
            } catch (error) {
                console.error('页面初始化失败:', error);
            }
        });

        function initializeElements() {
            // 主要元素
            agentGrid = document.getElementById('agentGrid');
            searchInput = document.getElementById('searchInput');
            newAgentBtn = document.getElementById('newAgentBtn');

            // 抽屉元素
            configDrawer = document.getElementById('configDrawer');
            drawerOverlay = document.getElementById('drawerOverlay');
            drawerClose = document.getElementById('drawerClose');

            // 工具选择模态框元素
            toolModal = document.getElementById('toolModal');
            toolModalClose = document.getElementById('toolModalClose');
            toolGroups = document.getElementById('toolGroups');
            toolSearchInput = document.getElementById('toolSearchInput');

            console.log('DOM元素初始化完成');
        }

        function setupEventListeners() {
            // 搜索功能
            searchInput.addEventListener('input', function() {
                const keyword = this.value.trim().toLowerCase();
                filterAgents(keyword);
            });

            // 新建Agent按钮
            newAgentBtn.addEventListener('click', function() {
                openDrawer();
                createNewAgent();
            });

            // 抽屉关闭
            drawerClose.addEventListener('click', closeDrawer);
            drawerOverlay.addEventListener('click', closeDrawer);

            // ESC键关闭抽屉
            document.addEventListener('keydown', function(e) {
                if (e.key === 'Escape' && configDrawer.classList.contains('show')) {
                    closeDrawer();
                }
            });

            // 工具选择模态框事件
            toolModalClose.addEventListener('click', closeToolModal);
            document.getElementById('cancelToolSelection').addEventListener('click', closeToolModal);
            document.getElementById('confirmToolSelection').addEventListener('click', confirmToolSelection);

            // 工具搜索
            toolSearchInput.addEventListener('input', function() {
                const keyword = this.value.trim().toLowerCase();
                filterTools(keyword);
            });

            // 全选工具
            document.getElementById('selectAllTools').addEventListener('change', function() {
                const allCheckboxes = document.querySelectorAll('#toolGroups .tool-item input[type="checkbox"]');
                allCheckboxes.forEach(checkbox => {
                    checkbox.checked = this.checked;
                });
            });

            // 调试按钮
            document.getElementById('debugBtn').addEventListener('click', function() {
                showDebugInfo();
            });

            console.log('事件监听器设置完成');
        }

        async function loadInitialData() {
            try {
                showLoading();

                // 并行加载Agent列表和工具列表
                const [agentsData, toolsData] = await Promise.all([
                    loadAgents(),
                    loadTools()
                ]);

                allAgents = agentsData || [];
                allTools = toolsData || [];

                console.log('数据加载完成:', {
                    agents: allAgents.length,
                    tools: allTools.length
                });

                renderAgentGrid();
            } catch (error) {
                console.error('加载数据失败:', error);
                loadMockData();
            }
        }

        async function loadAgents() {
            try {
                const response = await fetch('/api/agents');
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                return await response.json();
            } catch (error) {
                console.error('加载Agent列表失败:', error);
                throw error;
            }
        }

        async function loadTools() {
            try {
                const response = await fetch('/api/agents/tools');
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                return await response.json();
            } catch (error) {
                console.error('加载工具列表失败:', error);
                throw error;
            }
        }

        function loadMockData() {
            allAgents = [
                {
                    id: 'DEFAULT_AGENT',
                    name: 'DEFAULT_AGENT',
                    description: '一个通用的智能代理，可以使用各种工具来完成任务。它具有强大的推理能力，并能根据用户需求选择合适的工具。',
                    availableTools: ['tool_one_query_executor', 'terminal']
                },
                {
                    id: 'TAVILY_AGENT',
                    name: 'TAVILY_AGENT',
                    description: '一个专门用于Tavily工具集成的智能代理。它能够使用Tavily的各种功能来处理特定的任务。',
                    availableTools: ['tavily_one_query_extract', 'tavily_one_query_cloud', 'tavily_one_query_search']
                }
            ];

            allTools = [
                { key: 'tool_one_query_executor', name: '查询执行器', description: '执行各种查询操作', serviceGroup: 'QUERY' },
                { key: 'terminal', name: '终端', description: '执行系统命令', serviceGroup: 'SYSTEM' },
                { key: 'tavily_one_query_extract', name: 'Tavily提取', description: 'Tavily数据提取工具', serviceGroup: 'TAVILY' },
                { key: 'tavily_one_query_cloud', name: 'Tavily云服务', description: 'Tavily云服务工具', serviceGroup: 'TAVILY' },
                { key: 'tavily_one_query_search', name: 'Tavily搜索', description: 'Tavily搜索工具', serviceGroup: 'TAVILY' },
                { key: 'tavily_one_query_map', name: 'Tavily地图', description: 'Tavily地图工具', serviceGroup: 'TAVILY' }
            ];

            console.log('使用模拟数据:', { agents: allAgents.length, tools: allTools.length });
            renderAgentGrid();
        }

        function showLoading() {
            agentGrid.innerHTML = `
                <div class="loading-placeholder">
                    <i class="bi bi-hourglass-split"></i>
                    <span>加载Agent列表...</span>
                </div>
            `;
        }

        function renderAgentGrid(agents = allAgents) {
            if (!agents || agents.length === 0) {
                agentGrid.innerHTML = `
                    <div class="new-agent-card" onclick="openDrawer(); createNewAgent();">
                        <i class="bi bi-plus-circle"></i>
                        <h3>创建第一个Agent</h3>
                        <p>开始配置您的第一个智能代理</p>
                    </div>
                `;
                return;
            }

            const newCardHtml = `
                <div class="new-agent-card" onclick="openDrawer(); createNewAgent();">
                    <i class="bi bi-plus-circle"></i>
                    <h3>新建Agent</h3>
                    <p>添加新的智能代理配置</p>
                </div>
            `;

            const agentCardsHtml = agents.map(agent => `
                <div class="agent-card" data-id="${agent.id}" onclick="selectAgent('${agent.id}')">
                    <div class="agent-card-header">
                        <h3 class="agent-card-title">${escapeHtml(agent.name)}</h3>
                        <div class="agent-card-id">${agent.id}</div>
                    </div>
                    <div class="agent-card-description">${escapeHtml(agent.description || '暂无描述')}</div>
                    <div class="agent-card-tools">
                        ${(agent.availableTools || []).slice(0, 3).map(tool =>
                            `<span class="agent-tool-tag">${tool}</span>`
                        ).join('')}
                        ${(agent.availableTools || []).length > 3 ?
                            `<span class="agent-tool-tag">+${(agent.availableTools || []).length - 3}</span>` : ''}
                    </div>
                    <div class="agent-card-actions">
                        <button class="card-action-btn" onclick="event.stopPropagation(); editAgent('${agent.id}')">
                            <i class="bi bi-pencil"></i> 编辑
                        </button>
                        <button class="card-action-btn danger" onclick="event.stopPropagation(); deleteAgent('${agent.id}')">
                            <i class="bi bi-trash"></i> 删除
                        </button>
                    </div>
                </div>
            `).join('');

            agentGrid.innerHTML = newCardHtml + agentCardsHtml;
        }

        function filterAgents(keyword) {
            if (!keyword) {
                renderAgentGrid();
                return;
            }

            const filtered = allAgents.filter(agent =>
                agent.name.toLowerCase().includes(keyword) ||
                (agent.description && agent.description.toLowerCase().includes(keyword)) ||
                (agent.availableTools && agent.availableTools.some(tool => tool.toLowerCase().includes(keyword)))
            );

            renderAgentGrid(filtered);
        }

        function openDrawer() {
            configDrawer.classList.add('show');
            drawerOverlay.classList.add('show');
            document.body.style.overflow = 'hidden';
        }

        function closeDrawer() {
            configDrawer.classList.remove('show');
            drawerOverlay.classList.remove('show');
            document.body.style.overflow = '';
            currentAgent = null;
            isEditing = false;

            // 清除选中状态
            document.querySelectorAll('.agent-card').forEach(card => {
                card.classList.remove('selected');
            });
        }

        function selectAgent(agentId) {
            // 移除之前的选中状态
            document.querySelectorAll('.agent-card').forEach(card => {
                card.classList.remove('selected');
            });

            // 添加当前选中状态
            const selectedCard = document.querySelector(`[data-id="${agentId}"]`);
            if (selectedCard) {
                selectedCard.classList.add('selected');
            }

            // 查找Agent数据
            currentAgent = allAgents.find(agent => agent.id === agentId);
            if (!currentAgent) {
                console.error('未找到Agent:', agentId);
                return;
            }

            // 打开抽屉并显示配置表单
            openDrawer();
            showAgentDetail(currentAgent);
        }

        function editAgent(agentId) {
            selectAgent(agentId);
        }

        function createNewAgent() {
            // 清除选中状态
            document.querySelectorAll('.agent-card').forEach(card => {
                card.classList.remove('selected');
            });

            // 重置当前Agent
            currentAgent = null;

            // 显示新建表单
            showAgentDetail(null);
        }

        function showAgentDetail(agent) {
            const isEdit = !!agent;
            isEditing = isEdit;

            // 更新抽屉标题
            const drawerTitle = document.getElementById('drawerTitle');
            if (drawerTitle) {
                drawerTitle.textContent = isEdit ? `编辑: ${agent.name}` : '新建Agent';
            }

            // 创建表单内容
            createAgentForm(agent);
        }

        function createAgentForm(agent) {
            const isEdit = !!agent;
            const drawerContent = document.getElementById('drawerContent');
            const drawerFooter = document.getElementById('drawerFooter');

            const formHtml = `
                <!-- 基本信息 -->
                <div class="form-section">
                    <div class="section-title">
                        <i class="bi bi-info-circle"></i>
                        <span>基本信息</span>
                    </div>

                    <div class="form-group">
                        <label class="form-label" for="agentName">Agent名称 *</label>
                        <input type="text" class="form-input" id="agentName"
                            placeholder="输入Agent名称" value="${isEdit ? escapeHtml(agent.name) : ''}" required>
                        <small class="text-muted">Agent的唯一标识名称</small>
                    </div>

                    <div class="form-group">
                        <label class="form-label" for="agentDescription">描述 *</label>
                        <textarea class="form-textarea" id="agentDescription"
                            placeholder="描述这个Agent的功能和用途" required>${isEdit ? escapeHtml(agent.description || '') : ''}</textarea>
                        <small class="text-muted">详细描述Agent的功能和使用场景</small>
                    </div>
                </div>

                <!-- 提示词配置 -->
                <div class="form-section">
                    <div class="section-title">
                        <i class="bi bi-chat-text"></i>
                        <span>提示词配置</span>
                    </div>

                    <div class="form-group">
                        <label class="form-label" for="systemPrompt">系统提示词</label>
                        <textarea class="form-textarea large" id="systemPrompt"
                            placeholder="设置Agent的系统提示词（可选）">${isEdit ? escapeHtml(agent.systemPrompt || '') : ''}</textarea>
                        <small class="text-muted">定义Agent的基本行为和角色</small>
                    </div>

                    <div class="form-group">
                        <label class="form-label" for="nextStepPrompt">下一步提示词 *</label>
                        <textarea class="form-textarea large" id="nextStepPrompt"
                            placeholder="设置Agent执行任务时的指导提示词" required>${isEdit ? escapeHtml(agent.nextStepPrompt || '') : ''}</textarea>
                        <small class="text-muted">指导Agent如何使用工具和执行下一步操作</small>
                    </div>
                </div>

                <!-- 工具配置 -->
                <div class="form-section">
                    <div class="section-title">
                        <i class="bi bi-tools"></i>
                        <span>工具配置</span>
                    </div>

                    <div class="tools-section">
                        <div class="tools-header">
                            <div class="tools-title">已选择的工具</div>
                            <button class="add-tool-btn" id="addToolBtn" type="button">
                                <i class="bi bi-plus"></i>
                                <span>添加工具</span>
                            </button>
                        </div>

                        <div class="selected-tools" id="selectedTools">
                            ${isEdit && agent.availableTools && agent.availableTools.length > 0 ?
                                agent.availableTools.map(tool =>
                                    `<div class="selected-tool-item">
                                        <span>${tool}</span>
                                        <button class="remove-tool-btn" onclick="removeTool('${tool}')" type="button">
                                            <i class="bi bi-x"></i>
                                        </button>
                                    </div>`
                                ).join('') :
                                '<div class="empty-tools">暂未选择任何工具，点击"添加工具"按钮来选择</div>'
                            }
                        </div>
                    </div>
                </div>
            `;

            const footerHtml = `
                <button class="btn btn-secondary" onclick="closeDrawer()">
                    <i class="bi bi-x"></i>
                    <span>取消</span>
                </button>
                <button class="btn btn-primary" id="saveAgentBtn">
                    <i class="bi bi-check"></i>
                    <span>保存</span>
                </button>
                ${isEdit ? `
                <button class="btn btn-danger" id="deleteAgentBtn">
                    <i class="bi bi-trash"></i>
                    <span>删除</span>
                </button>
                ` : ''}
            `;

            drawerContent.innerHTML = formHtml;
            drawerFooter.innerHTML = footerHtml;

            // 添加事件监听器
            setupFormEventListeners();
        }

        function setupFormEventListeners() {
            // 保存按钮
            const saveAgentBtn = document.getElementById('saveAgentBtn');
            if (saveAgentBtn) {
                saveAgentBtn.addEventListener('click', saveAgent);
            }

            // 删除按钮
            const deleteAgentBtn = document.getElementById('deleteAgentBtn');
            if (deleteAgentBtn) {
                deleteAgentBtn.addEventListener('click', () => deleteAgent(currentAgent.id));
            }

            // 添加工具按钮
            const addToolBtn = document.getElementById('addToolBtn');
            if (addToolBtn) {
                addToolBtn.addEventListener('click', showToolSelectionModal);
            }

            // 初始化选中的工具
            if (currentAgent && currentAgent.availableTools) {
                selectedTools = [...currentAgent.availableTools];
            } else {
                selectedTools = [];
            }
        }

        async function saveAgent() {
            // 这里可以添加保存逻辑
            console.log('保存Agent');
            closeDrawer();
            showSuccessMessage('Agent保存成功');
        }

        async function deleteAgent(agentId) {
            if (!confirm('确定要删除这个Agent吗？此操作不可撤销。')) {
                return;
            }

            console.log('删除Agent:', agentId);
            closeDrawer();
            showSuccessMessage('Agent删除成功');
        }

        function showSuccessMessage(message) {
            const toast = document.createElement('div');
            toast.className = 'alert alert-success position-fixed';
            toast.style.cssText = 'top: 80px; right: 20px; z-index: 9999; min-width: 300px; background: var(--success-color); color: white; border: none; border-radius: 8px; box-shadow: var(--shadow-lg);';
            toast.innerHTML = `<i class="bi bi-check-circle"></i> ${escapeHtml(message)}`;
            document.body.appendChild(toast);

            setTimeout(() => {
                toast.remove();
            }, 3000);
        }

        function escapeHtml(text) {
            if (!text) return '';
            const div = document.createElement('div');
            div.textContent = text;
            return div.innerHTML;
        }

        function showToolSelectionModal() {
            // 渲染工具分组
            renderToolGroups();

            // 显示模态框
            toolModal.classList.add('show');
            document.body.style.overflow = 'hidden';
        }

        function closeToolModal() {
            toolModal.classList.remove('show');
            document.body.style.overflow = '';
        }

        function renderToolGroups() {
            if (!allTools || allTools.length === 0) {
                toolGroups.innerHTML = '<div class="text-muted text-center py-4">暂无可用工具</div>';
                return;
            }

            // 按服务组分组
            const groupedTools = {};
            allTools.forEach(tool => {
                const group = tool.serviceGroup || 'OTHER';
                if (!groupedTools[group]) {
                    groupedTools[group] = [];
                }
                groupedTools[group].push(tool);
            });

            const groupsHtml = Object.entries(groupedTools).map(([groupName, tools]) => `
                <div class="tool-group" data-group="${groupName}">
                    <div class="tool-group-header" onclick="toggleToolGroup('${groupName}')">
                        <div class="tool-group-title">
                            <span>${groupName}</span>
                            <span class="tool-group-count">${tools.length}</span>
                        </div>
                        <i class="bi bi-chevron-down tool-group-toggle"></i>
                    </div>
                    <div class="tool-group-items">
                        ${tools.map(tool => `
                            <div class="tool-item">
                                <input type="checkbox"
                                    id="tool_${tool.key}"
                                    value="${tool.key}"
                                    ${selectedTools.includes(tool.key) ? 'checked' : ''}>
                                <div class="tool-item-info">
                                    <div class="tool-item-name">${escapeHtml(tool.name || tool.key)}</div>
                                    <div class="tool-item-description">${escapeHtml(tool.description || '暂无描述')}</div>
                                </div>
                            </div>
                        `).join('')}
                    </div>
                </div>
            `).join('');

            toolGroups.innerHTML = groupsHtml;
        }

        function filterTools(keyword) {
            if (!keyword) {
                renderToolGroups();
                return;
            }

            const filteredTools = allTools.filter(tool =>
                (tool.name && tool.name.toLowerCase().includes(keyword)) ||
                (tool.key && tool.key.toLowerCase().includes(keyword)) ||
                (tool.description && tool.description.toLowerCase().includes(keyword))
            );

            // 重新分组并渲染
            const groupedTools = {};
            filteredTools.forEach(tool => {
                const group = tool.serviceGroup || 'OTHER';
                if (!groupedTools[group]) {
                    groupedTools[group] = [];
                }
                groupedTools[group].push(tool);
            });

            const groupsHtml = Object.entries(groupedTools).map(([groupName, tools]) => `
                <div class="tool-group" data-group="${groupName}">
                    <div class="tool-group-header" onclick="toggleToolGroup('${groupName}')">
                        <div class="tool-group-title">
                            <span>${groupName}</span>
                            <span class="tool-group-count">${tools.length}</span>
                        </div>
                        <i class="bi bi-chevron-down tool-group-toggle"></i>
                    </div>
                    <div class="tool-group-items">
                        ${tools.map(tool => `
                            <div class="tool-item">
                                <input type="checkbox"
                                    id="tool_${tool.key}"
                                    value="${tool.key}"
                                    ${selectedTools.includes(tool.key) ? 'checked' : ''}>
                                <div class="tool-item-info">
                                    <div class="tool-item-name">${escapeHtml(tool.name || tool.key)}</div>
                                    <div class="tool-item-description">${escapeHtml(tool.description || '暂无描述')}</div>
                                </div>
                            </div>
                        `).join('')}
                    </div>
                </div>
            `).join('');

            toolGroups.innerHTML = groupsHtml;
        }

        function confirmToolSelection() {
            // 获取所有选中的工具
            const checkedBoxes = document.querySelectorAll('#toolGroups input[type="checkbox"]:checked');
            selectedTools = Array.from(checkedBoxes).map(cb => cb.value);

            // 更新选中工具的显示
            updateSelectedToolsDisplay();

            // 关闭模态框
            closeToolModal();
        }

        function updateSelectedToolsDisplay() {
            const selectedToolsContainer = document.getElementById('selectedTools');

            if (selectedTools.length === 0) {
                selectedToolsContainer.innerHTML = '<div class="empty-tools">暂未选择任何工具，点击"添加工具"按钮来选择</div>';
                return;
            }

            const toolsHtml = selectedTools.map(toolKey => {
                const tool = allTools.find(t => t.key === toolKey);
                const toolName = tool ? (tool.name || toolKey) : toolKey;

                return `
                    <div class="selected-tool-item">
                        <span>${escapeHtml(toolName)}</span>
                        <button class="remove-tool-btn" onclick="removeTool('${toolKey}')" type="button">
                            <i class="bi bi-x"></i>
                        </button>
                    </div>
                `;
            }).join('');

            selectedToolsContainer.innerHTML = toolsHtml;
        }

        function removeTool(toolKey) {
            selectedTools = selectedTools.filter(key => key !== toolKey);
            updateSelectedToolsDisplay();
        }

        function toggleToolGroup(groupName) {
            const groupElement = document.querySelector(`[data-group="${groupName}"]`);
            if (groupElement) {
                groupElement.classList.toggle('collapsed');
            }
        }

        function showDebugInfo() {
            console.log('=== 调试信息 ===');
            console.log('当前Agent:', currentAgent);
            console.log('所有Agents:', allAgents);
            console.log('所有Tools:', allTools);
            console.log('选中Tools:', selectedTools);
            alert('调试信息已输出到控制台，请按F12查看');
        }
    </script>
</body>
</html>
