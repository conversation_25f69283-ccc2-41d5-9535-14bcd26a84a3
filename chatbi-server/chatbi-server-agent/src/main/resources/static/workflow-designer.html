<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>智能体编排 - Spring AI Alibaba</title>

    <!-- Bootstrap Icons -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.0/font/bootstrap-icons.css" rel="stylesheet">
    <!-- SortableJS for drag & drop -->
    <script src="https://cdn.jsdelivr.net/npm/sortablejs@1.15.0/Sortable.min.js"></script>
    <!-- Inter Font -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <style>
        :root {
            /* 现代化配色系统 - 参考 Linear & Notion */
            --primary: #5b21b6;
            --primary-hover: #4c1d95;
            --primary-light: #f3e8ff;
            --primary-dark: #3b0764;

            --accent: #0ea5e9;
            --accent-light: #e0f2fe;

            --success: #059669;
            --success-light: #d1fae5;

            --warning: #d97706;
            --warning-light: #fef3c7;

            --danger: #dc2626;
            --danger-light: #fee2e2;

            /* 精细化灰度系统 */
            --white: #ffffff;
            --gray-25: #fcfcfd;
            --gray-50: #f8fafc;
            --gray-100: #f1f5f9;
            --gray-200: #e2e8f0;
            --gray-300: #cbd5e1;
            --gray-400: #94a3b8;
            --gray-500: #64748b;
            --gray-600: #475569;
            --gray-700: #334155;
            --gray-800: #1e293b;
            --gray-900: #0f172a;

            /* 设计令牌 */
            --radius-sm: 6px;
            --radius: 8px;
            --radius-md: 10px;
            --radius-lg: 12px;
            --radius-xl: 16px;

            /* 阴影系统 */
            --shadow-xs: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
            --shadow-sm: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
            --shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
            --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
            --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);

            /* 间距系统 */
            --space-1: 4px;
            --space-2: 8px;
            --space-3: 12px;
            --space-4: 16px;
            --space-5: 20px;
            --space-6: 24px;
            --space-8: 32px;
            --space-10: 40px;
            --space-12: 48px;
            --space-16: 64px;
        }

        * {
            box-sizing: border-box;
        }

        body {
            background: var(--gray-25);
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 0;
            color: var(--gray-900);
            line-height: 1.5;
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
        }

        /* 主布局 - 现代化设计 */
        .app {
            min-height: 100vh;
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            position: relative;
        }

        .app::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="20" height="20" patternUnits="userSpaceOnUse"><path d="M 20 0 L 0 0 0 20" fill="none" stroke="rgba(255,255,255,0.3)" stroke-width="1"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
            pointer-events: none;
        }

        .app-header {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-bottom: 1px solid rgba(255, 255, 255, 0.2);
            padding: var(--space-5) 0;
            position: sticky;
            top: 0;
            z-index: 50;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.08);
        }

        .header-content {
            max-width: 1400px;
            margin: 0 auto;
            padding: 0 var(--space-6);
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .header-left {
            display: flex;
            align-items: center;
            gap: var(--space-4);
        }

        .app-title {
            font-size: 1.75rem;
            font-weight: 800;
            background: linear-gradient(135deg, var(--primary) 0%, #667eea 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            margin: 0;
            letter-spacing: -0.025em;
        }

        .app-subtitle {
            font-size: 0.875rem;
            color: var(--gray-600);
            margin: 0;
            font-weight: 500;
        }

        .header-actions {
            display: flex;
            align-items: center;
            gap: var(--space-3);
        }

        /* 主内容区域 */
        .main-container {
            max-width: 1400px;
            margin: 0 auto;
            padding: var(--space-10) var(--space-6);
            position: relative;
            z-index: 1;
        }

        .workflow-builder {
            display: grid;
            grid-template-columns: 420px 1fr;
            gap: var(--space-10);
            align-items: start;
        }

        /* 左侧配置面板 - 现代化卡片设计 */
        .config-panel {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-radius: 24px;
            border: 1px solid rgba(255, 255, 255, 0.2);
            overflow: hidden;
            position: sticky;
            top: 140px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1), 0 8px 16px rgba(0, 0, 0, 0.06);
            transition: all 0.3s ease;
        }

        .config-panel:hover {
            transform: translateY(-2px);
            box-shadow: 0 25px 50px rgba(0, 0, 0, 0.15), 0 12px 24px rgba(0, 0, 0, 0.08);
        }

        .panel-header {
            padding: var(--space-6);
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: var(--white);
            position: relative;
            overflow: hidden;
        }

        .panel-header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="20" cy="20" r="2" fill="rgba(255,255,255,0.1)"/><circle cx="80" cy="40" r="1.5" fill="rgba(255,255,255,0.08)"/><circle cx="40" cy="80" r="1" fill="rgba(255,255,255,0.06)"/></svg>');
            pointer-events: none;
        }

        .panel-title {
            font-size: 1.25rem;
            font-weight: 700;
            margin: 0 0 var(--space-2) 0;
            position: relative;
            z-index: 1;
        }

        .panel-description {
            font-size: 0.875rem;
            opacity: 0.9;
            margin: 0;
            position: relative;
            z-index: 1;
            line-height: 1.5;
        }

        .panel-content {
            padding: var(--space-6);
        }

        .form-group {
            margin-bottom: var(--space-6);
        }

        .form-label {
            display: block;
            font-weight: 600;
            color: var(--gray-900);
            margin-bottom: var(--space-3);
            font-size: 0.875rem;
            letter-spacing: 0.025em;
        }

        .form-input {
            width: 100%;
            padding: var(--space-4) var(--space-4);
            border: 2px solid var(--gray-200);
            border-radius: 16px;
            font-size: 0.875rem;
            transition: all 0.3s ease;
            background: rgba(255, 255, 255, 0.8);
            font-family: inherit;
            backdrop-filter: blur(10px);
        }

        .form-input:focus {
            outline: none;
            border-color: var(--primary);
            background: var(--white);
            box-shadow: 0 0 0 4px rgba(102, 126, 234, 0.1);
            transform: translateY(-1px);
        }

        .form-input:hover {
            border-color: var(--gray-300);
            background: var(--white);
        }

        .form-help {
            margin-top: var(--space-3);
            font-size: 0.8125rem;
            color: var(--gray-500);
            line-height: 1.5;
            padding-left: var(--space-1);
        }

        /* 统计卡片 - 现代化设计 */
        .stats-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: var(--space-4);
            margin-top: var(--space-6);
        }

        .stat-card {
            background: linear-gradient(135deg, rgba(255, 255, 255, 0.9) 0%, rgba(255, 255, 255, 0.7) 100%);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.3);
            border-radius: 20px;
            padding: var(--space-5);
            text-align: center;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .stat-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(135deg, rgba(102, 126, 234, 0.05) 0%, rgba(118, 75, 162, 0.05) 100%);
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .stat-card:hover {
            transform: translateY(-4px);
            box-shadow: 0 12px 24px rgba(0, 0, 0, 0.1);
            border-color: rgba(102, 126, 234, 0.3);
        }

        .stat-card:hover::before {
            opacity: 1;
        }

        .stat-number {
            font-size: 1.75rem;
            font-weight: 800;
            background: linear-gradient(135deg, var(--primary) 0%, #667eea 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            margin: 0;
            position: relative;
            z-index: 1;
        }

        .stat-label {
            font-size: 0.75rem;
            color: var(--gray-600);
            margin: var(--space-2) 0 0 0;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.05em;
            position: relative;
            z-index: 1;
        }

        /* 右侧智能体编排区域 - 现代化设计 */
        .workflow-area {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-radius: 24px;
            border: 1px solid rgba(255, 255, 255, 0.2);
            overflow: hidden;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1), 0 8px 16px rgba(0, 0, 0, 0.06);
            transition: all 0.3s ease;
        }

        .workflow-area:hover {
            transform: translateY(-2px);
            box-shadow: 0 25px 50px rgba(0, 0, 0, 0.15), 0 12px 24px rgba(0, 0, 0, 0.08);
        }

        .workflow-header {
            padding: var(--space-6);
            border-bottom: 1px solid rgba(255, 255, 255, 0.3);
            background: linear-gradient(135deg, rgba(255, 255, 255, 0.9) 0%, rgba(248, 250, 252, 0.9) 100%);
            backdrop-filter: blur(10px);
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .workflow-title {
            font-size: 1.25rem;
            font-weight: 700;
            color: var(--gray-900);
            margin: 0;
            letter-spacing: -0.025em;
        }

        .workflow-subtitle {
            font-size: 0.875rem;
            color: var(--gray-600);
            margin: var(--space-1) 0 0 0;
            font-weight: 500;
        }

        .add-agent-btn {
            background: linear-gradient(135deg, var(--primary) 0%, #667eea 100%);
            color: var(--white);
            border: none;
            border-radius: 16px;
            padding: var(--space-3) var(--space-5);
            font-size: 0.875rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: var(--space-2);
            box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
            position: relative;
            overflow: hidden;
        }

        .add-agent-btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
            transition: left 0.5s ease;
        }

        .add-agent-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 24px rgba(102, 126, 234, 0.4);
        }

        .add-agent-btn:hover::before {
            left: 100%;
        }

        .add-agent-btn:active {
            transform: translateY(0);
        }

        /* 智能体列表 */
        .agent-list {
            padding: var(--space-6);
            min-height: 400px;
        }

        .agent-list-empty {
            text-align: center;
            padding: var(--space-16) var(--space-6);
            color: var(--gray-500);
            position: relative;
        }

        .agent-list-empty::before {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 200px;
            height: 200px;
            background: radial-gradient(circle, rgba(102, 126, 234, 0.05) 0%, transparent 70%);
            border-radius: 50%;
            z-index: 0;
        }

        .empty-icon {
            width: 80px;
            height: 80px;
            background: linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%);
            border: 2px solid rgba(102, 126, 234, 0.2);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto var(--space-6);
            font-size: 2rem;
            color: rgba(102, 126, 234, 0.6);
            position: relative;
            z-index: 1;
            backdrop-filter: blur(10px);
        }

        .empty-title {
            font-size: 1.25rem;
            font-weight: 700;
            color: var(--gray-700);
            margin: 0 0 var(--space-3) 0;
            position: relative;
            z-index: 1;
        }

        .empty-description {
            font-size: 0.875rem;
            color: var(--gray-500);
            margin: 0;
            line-height: 1.6;
            position: relative;
            z-index: 1;
            max-width: 300px;
            margin-left: auto;
            margin-right: auto;
        }

        /* 智能体项目 - 现代化卡片设计 */
        .agent-item {
            background: rgba(255, 255, 255, 0.9);
            backdrop-filter: blur(10px);
            border: 2px solid rgba(255, 255, 255, 0.3);
            border-radius: 20px;
            margin-bottom: var(--space-4);
            transition: all 0.3s ease;
            overflow: hidden;
            position: relative;
            box-shadow: 0 8px 16px rgba(0, 0, 0, 0.06);
        }

        .agent-item::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(135deg, rgba(102, 126, 234, 0.03) 0%, rgba(118, 75, 162, 0.03) 100%);
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .agent-item:hover {
            border-color: rgba(102, 126, 234, 0.4);
            box-shadow: 0 12px 32px rgba(0, 0, 0, 0.12);
            transform: translateY(-4px);
        }

        .agent-item:hover::before {
            opacity: 1;
        }

        .agent-item.sortable-ghost {
            opacity: 0.6;
            transform: rotate(3deg) scale(0.98);
            box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
        }

        .agent-item.sortable-chosen {
            box-shadow: 0 16px 40px rgba(102, 126, 234, 0.2);
            transform: scale(1.02);
            border-color: var(--primary);
        }

        .agent-content {
            padding: var(--space-5);
            display: flex;
            align-items: center;
            gap: var(--space-4);
            position: relative;
            z-index: 1;
        }

        .agent-number {
            width: 40px;
            height: 40px;
            background: linear-gradient(135deg, var(--primary) 0%, #667eea 100%);
            color: var(--white);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 0.875rem;
            font-weight: 700;
            flex-shrink: 0;
            box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
            position: relative;
        }

        .agent-number::before {
            content: '';
            position: absolute;
            top: -2px;
            left: -2px;
            right: -2px;
            bottom: -2px;
            background: linear-gradient(135deg, rgba(102, 126, 234, 0.3) 0%, rgba(118, 75, 162, 0.3) 100%);
            border-radius: 50%;
            z-index: -1;
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .agent-item:hover .agent-number::before {
            opacity: 1;
        }

        .agent-main {
            flex: 1;
            min-width: 0;
        }

        .agent-selector {
            width: 100%;
            padding: var(--space-4) var(--space-4);
            border: 2px solid rgba(255, 255, 255, 0.5);
            border-radius: 16px;
            font-size: 0.875rem;
            background: rgba(255, 255, 255, 0.8);
            backdrop-filter: blur(10px);
            cursor: pointer;
            transition: all 0.3s ease;
            font-weight: 500;
        }

        .agent-selector:focus {
            outline: none;
            border-color: var(--primary);
            background: var(--white);
            box-shadow: 0 0 0 4px rgba(102, 126, 234, 0.1);
            transform: translateY(-1px);
        }

        .agent-selector:hover {
            border-color: rgba(102, 126, 234, 0.3);
            background: var(--white);
        }

        .agent-info {
            margin-top: var(--space-3);
            padding: var(--space-3) var(--space-4);
            background: linear-gradient(135deg, rgba(255, 255, 255, 0.9) 0%, rgba(248, 250, 252, 0.9) 100%);
            backdrop-filter: blur(10px);
            border-radius: 16px;
            border: 1px solid rgba(255, 255, 255, 0.5);
            position: relative;
            overflow: hidden;
        }

        .agent-info::before {
            content: '';
            position: absolute;
            left: 0;
            top: 0;
            bottom: 0;
            width: 4px;
            background: linear-gradient(135deg, var(--accent) 0%, #667eea 100%);
            border-radius: 0 2px 2px 0;
        }

        .agent-name {
            font-size: 0.8125rem;
            font-weight: 700;
            color: var(--gray-900);
            margin: 0;
            letter-spacing: 0.025em;
        }

        .agent-description {
            font-size: 0.75rem;
            color: var(--gray-600);
            margin: var(--space-2) 0 0 0;
            line-height: 1.4;
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
            overflow: hidden;
        }

        .agent-actions {
            display: flex;
            gap: var(--space-1);
            flex-shrink: 0;
        }

        .action-btn {
            width: 32px;
            height: 32px;
            background: var(--white);
            border: 1px solid var(--gray-300);
            border-radius: var(--radius-sm);
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.15s ease;
            color: var(--gray-500);
            font-size: 0.875rem;
        }

        .action-btn:hover {
            background: var(--gray-50);
            border-color: var(--gray-400);
            color: var(--gray-700);
        }

        .action-btn.danger:hover {
            background: var(--danger);
            border-color: var(--danger);
            color: var(--white);
        }

        .drag-handle {
            cursor: grab;
            color: var(--gray-400);
            padding: var(--space-1);
            border-radius: var(--radius-sm);
            transition: all 0.15s ease;
        }

        .drag-handle:hover {
            background: var(--gray-100);
            color: var(--gray-600);
        }

        .drag-handle:active {
            cursor: grabbing;
        }

        /* 按钮系统 - 现代化设计 */
        .btn {
            padding: var(--space-3) var(--space-6);
            border: 2px solid transparent;
            border-radius: 16px;
            font-weight: 600;
            font-size: 0.875rem;
            cursor: pointer;
            transition: all 0.3s ease;
            display: inline-flex;
            align-items: center;
            gap: var(--space-2);
            text-decoration: none;
            line-height: 1;
            position: relative;
            overflow: hidden;
        }

        .btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
            transition: left 0.5s ease;
        }

        .btn:hover::before {
            left: 100%;
        }

        .btn-primary {
            background: linear-gradient(135deg, var(--primary) 0%, #667eea 100%);
            color: var(--white);
            border-color: transparent;
            box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 24px rgba(102, 126, 234, 0.4);
        }

        .btn-secondary {
            background: rgba(255, 255, 255, 0.9);
            backdrop-filter: blur(10px);
            color: var(--gray-700);
            border-color: rgba(255, 255, 255, 0.3);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }

        .btn-secondary:hover {
            background: var(--white);
            border-color: rgba(102, 126, 234, 0.3);
            color: var(--gray-900);
            transform: translateY(-2px);
            box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
        }

        .btn-ghost {
            background: transparent;
            color: var(--gray-600);
            border-color: transparent;
        }

        .btn-ghost:hover {
            background: rgba(255, 255, 255, 0.8);
            color: var(--gray-900);
            backdrop-filter: blur(10px);
        }

        .btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none !important;
        }

        .btn:disabled::before {
            display: none;
        }

        .btn-sm {
            padding: var(--space-2) var(--space-4);
            font-size: 0.8125rem;
            border-radius: 12px;
        }

        .btn-lg {
            padding: var(--space-4) var(--space-8);
            font-size: 1rem;
            border-radius: 20px;
        }

        /* 拖拽手柄 */
        .drag-handle {
            cursor: grab;
            color: var(--gray-400);
            padding: var(--space-2);
            border-radius: var(--radius-sm);
            transition: all 0.15s ease;
        }

        .drag-handle:hover {
            background: var(--gray-100);
            color: var(--gray-600);
        }

        .drag-handle:active {
            cursor: grabbing;
        }

        /* 动画效果 */
        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes pulse {
            0%, 100% {
                transform: scale(1);
            }
            50% {
                transform: scale(1.05);
            }
        }

        @keyframes shimmer {
            0% {
                background-position: -200px 0;
            }
            100% {
                background-position: calc(200px + 100%) 0;
            }
        }

        .config-panel,
        .workflow-area {
            animation: fadeInUp 0.6s ease-out;
        }

        .workflow-area {
            animation-delay: 0.1s;
        }

        .agent-item {
            animation: fadeInUp 0.4s ease-out;
        }

        .empty-icon {
            animation: pulse 2s ease-in-out infinite;
        }

        /* 状态指示器 */
        .status-indicator {
            display: inline-flex;
            align-items: center;
            gap: var(--space-1);
            font-size: 0.75rem;
            font-weight: 500;
            padding: var(--space-1) var(--space-2);
            border-radius: var(--radius-sm);
        }

        .status-configured {
            background: var(--success-light);
            color: var(--success);
        }

        .status-pending {
            background: var(--warning-light);
            color: var(--warning);
        }

        /* 响应式设计 */
        @media (max-width: 1024px) {
            .workflow-builder {
                grid-template-columns: 1fr;
                gap: var(--space-6);
            }

            .config-panel {
                position: static;
                order: 2;
            }
        }

        @media (max-width: 768px) {
            .main-container {
                padding: var(--space-6) var(--space-4);
            }

            .header-content {
                padding: 0 var(--space-4);
                flex-direction: column;
                align-items: flex-start;
                gap: var(--space-3);
            }

            .header-actions {
                width: 100%;
                justify-content: flex-end;
            }

            .panel-content, .agent-list {
                padding: var(--space-4);
            }

            .agent-header {
                padding: var(--space-3) var(--space-4);
            }

            .agent-content {
                padding: var(--space-4);
            }
        }

        /* 动画效果 */
        @keyframes slideIn {
            from {
                opacity: 0;
                transform: translateY(10px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .agent-item {
            animation: slideIn 0.3s ease-out;
        }

        /* 滚动条样式 */
        .agent-list::-webkit-scrollbar {
            width: 6px;
        }

        .agent-list::-webkit-scrollbar-track {
            background: var(--gray-100);
            border-radius: 3px;
        }

        .agent-list::-webkit-scrollbar-thumb {
            background: var(--gray-300);
            border-radius: 3px;
        }

        .agent-list::-webkit-scrollbar-thumb:hover {
            background: var(--gray-400);
        }

        /* 编排画布样式 - 参考 n8n 的节点编辑器 */
        .workflow-canvas {
            flex: 1;
            position: relative;
            background:
                radial-gradient(circle at 1px 1px, var(--gray-300) 1px, transparent 0);
            background-size: 20px 20px;
            overflow: hidden;
        }

        .canvas-viewport {
            width: 100%;
            height: 100%;
            position: relative;
            overflow: auto;
            cursor: grab;
        }

        .canvas-viewport:active {
            cursor: grabbing;
        }

        .canvas-content {
            min-width: 100%;
            min-height: 100%;
            position: relative;
            padding: var(--spacing-2xl);
        }

        /* 空状态 */
        .workflow-empty {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            text-align: center;
            color: var(--gray-500);
            max-width: 400px;
        }

        .workflow-empty i {
            font-size: 3rem;
            margin-bottom: var(--spacing-md);
            opacity: 0.5;
            color: var(--gray-400);
        }

        .workflow-empty h3 {
            font-size: 1.125rem;
            font-weight: 600;
            color: var(--gray-700);
            margin-bottom: var(--spacing-sm);
        }

        .workflow-empty p {
            font-size: 0.875rem;
            color: var(--gray-500);
            margin-bottom: var(--spacing-lg);
            line-height: 1.5;
        }

        /* 工作流节点样式 - 参考 n8n 的现代化节点设计 */
        .workflow-steps {
            position: relative;
        }

        .workflow-node {
            position: absolute;
            width: 240px;
            background: white;
            border: 2px solid var(--gray-200);
            border-radius: var(--border-radius-lg);
            box-shadow: var(--shadow-sm);
            cursor: move;
            transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
            user-select: none;
        }

        .workflow-node:hover {
            box-shadow: var(--shadow-lg);
            border-color: var(--primary-color);
            transform: translateY(-2px);
        }

        .workflow-node.selected {
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px var(--primary-light), var(--shadow-lg);
        }

        .workflow-node.dragging {
            transform: rotate(5deg);
            box-shadow: var(--shadow-2xl);
            z-index: 1000;
        }

        .node-header {
            padding: var(--spacing-md);
            border-bottom: 1px solid var(--gray-200);
            background: var(--gray-50);
            border-radius: var(--border-radius-lg) var(--border-radius-lg) 0 0;
            display: flex;
            align-items: center;
            gap: var(--spacing-sm);
        }

        .node-icon {
            width: 32px;
            height: 32px;
            background: var(--primary-color);
            border-radius: var(--border-radius);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 0.875rem;
            font-weight: 600;
            flex-shrink: 0;
        }

        .node-info {
            flex: 1;
            min-width: 0;
        }

        .node-title {
            font-weight: 600;
            color: var(--gray-900);
            font-size: 0.875rem;
            line-height: 1.2;
            margin: 0;
        }

        .node-subtitle {
            font-size: 0.75rem;
            color: var(--gray-500);
            margin: 0;
            margin-top: 2px;
        }

        .node-content {
            padding: var(--spacing-md);
        }

        .node-description {
            font-size: 0.875rem;
            color: var(--gray-700);
            line-height: 1.4;
            margin: 0;
        }

        .node-actions {
            position: absolute;
            top: var(--spacing-sm);
            right: var(--spacing-sm);
            display: flex;
            gap: var(--spacing-xs);
            opacity: 0;
            transition: opacity 0.2s ease;
        }

        .workflow-node:hover .node-actions {
            opacity: 1;
        }

        .node-action-btn {
            width: 24px;
            height: 24px;
            background: white;
            border: 1px solid var(--gray-300);
            border-radius: var(--border-radius);
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.2s ease;
            font-size: 0.75rem;
            color: var(--gray-600);
        }

        .node-action-btn:hover {
            background: var(--gray-50);
            border-color: var(--gray-400);
            color: var(--gray-800);
        }

        .node-action-btn.danger:hover {
            background: var(--danger-color);
            border-color: var(--danger-color);
            color: white;
        }

        /* 连接点样式 */
        .node-handle {
            position: absolute;
            width: 12px;
            height: 12px;
            background: var(--primary-color);
            border: 2px solid white;
            border-radius: 50%;
            cursor: crosshair;
            transition: all 0.2s ease;
        }

        .node-handle.input {
            top: -6px;
            left: 50%;
            transform: translateX(-50%);
        }

        .node-handle.output {
            bottom: -6px;
            left: 50%;
            transform: translateX(-50%);
        }

        .node-handle:hover {
            transform: translateX(-50%) scale(1.2);
            box-shadow: 0 0 0 4px var(--primary-light);
        }

        /* 连接线样式 */
        .workflow-connection {
            position: absolute;
            pointer-events: none;
            z-index: 1;
        }

        .connection-path {
            stroke: var(--primary-color);
            stroke-width: 2;
            fill: none;
            filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.1));
        }

        .connection-arrow {
            fill: var(--primary-color);
        }

        /* 现代化按钮系统 */
        .btn {
            padding: var(--spacing-sm) var(--spacing-md);
            border: 1px solid transparent;
            border-radius: var(--border-radius);
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
            font-size: 0.875rem;
            display: inline-flex;
            align-items: center;
            gap: var(--spacing-xs);
            text-decoration: none;
            line-height: 1.25;
            white-space: nowrap;
        }

        .btn-sm {
            padding: var(--spacing-xs) var(--spacing-sm);
            font-size: 0.8125rem;
        }

        .btn-lg {
            padding: var(--spacing-md) var(--spacing-lg);
            font-size: 1rem;
        }

        .btn-primary {
            background: var(--primary-color);
            color: white;
            border-color: var(--primary-color);
        }

        .btn-primary:hover {
            background: var(--primary-hover);
            border-color: var(--primary-hover);
            transform: translateY(-1px);
            box-shadow: var(--shadow-md);
        }

        .btn-secondary {
            background: white;
            color: var(--gray-700);
            border-color: var(--gray-300);
        }

        .btn-secondary:hover {
            background: var(--gray-50);
            border-color: var(--gray-400);
            color: var(--gray-900);
        }

        .btn-ghost {
            background: transparent;
            color: var(--gray-600);
            border-color: transparent;
        }

        .btn-ghost:hover {
            background: var(--gray-100);
            color: var(--gray-900);
        }

        .btn-success {
            background: var(--success-color);
            color: white;
            border-color: var(--success-color);
        }

        .btn-success:hover {
            background: #047857;
            border-color: #047857;
            transform: translateY(-1px);
            box-shadow: var(--shadow-md);
        }

        .btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none !important;
        }

        /* 表单样式 */
        .form-group {
            margin-bottom: var(--spacing-lg);
        }

        .form-label {
            display: block;
            margin-bottom: var(--spacing-xs);
            font-weight: 500;
            color: var(--gray-900);
            font-size: 0.875rem;
        }

        .form-input, .form-textarea, .form-select {
            width: 100%;
            padding: var(--spacing-sm) var(--spacing-md);
            border: 1px solid var(--gray-300);
            border-radius: var(--border-radius);
            font-size: 0.875rem;
            transition: all 0.2s ease;
            background: white;
        }

        .form-input:focus, .form-textarea:focus, .form-select:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px var(--primary-light);
        }

        .form-textarea {
            resize: vertical;
            min-height: 80px;
            font-family: inherit;
            line-height: 1.5;
        }

        .form-help {
            margin-top: var(--spacing-xs);
            font-size: 0.8125rem;
            color: var(--gray-500);
        }

        /* 响应式设计 */
        @media (max-width: 1200px) {
            .content-area {
                flex-direction: column;
            }
            
            .left-panel, .right-panel {
                width: 100%;
            }
        }
    </style>
</head>
<body>
    <div class="app">
        <!-- 应用头部 -->
        <header class="app-header">
            <div class="header-content">
                <div class="header-left">
                    <div style="display: flex; align-items: center; gap: var(--space-4);">
                        <a href="workflow-list.html" class="btn btn-ghost btn-sm">
                            <i class="bi bi-arrow-left"></i>
                            <span>返回列表</span>
                        </a>
                        <div>
                            <h1 class="app-title">智能体编排</h1>
                            <p class="app-subtitle">设计您的智能体工作流程</p>
                        </div>
                    </div>
                </div>
                <div class="header-actions">
                    <button class="btn btn-ghost btn-sm" id="previewBtn">
                        <i class="bi bi-eye"></i>
                        <span>预览</span>
                    </button>
                    <button class="btn btn-primary btn-sm" id="saveBtn">
                        <i class="bi bi-check"></i>
                        <span>保存编排</span>
                    </button>
                </div>
            </div>
        </header>

        <!-- 主内容区域 -->
        <main class="main-container">
            <div class="workflow-builder">
                <!-- 左侧配置面板 -->
                <aside class="config-panel">
                    <div class="panel-header">
                        <h2 class="panel-title">编排配置</h2>
                        <p class="panel-description">设置基本信息</p>
                    </div>
                    <div class="panel-content">
                        <div class="form-group">
                            <label class="form-label" for="workflowTitle">编排名称 *</label>
                            <input type="text" class="form-input" id="workflowTitle"
                                placeholder="例如：天气查询助手">
                            <div class="form-help">为您的智能体编排起一个清晰的名称</div>
                        </div>

                        <div class="form-group">
                            <label class="form-label">执行统计</label>
                            <div class="stats-grid">
                                <div class="stat-card">
                                    <div class="stat-number" id="agentCount">0</div>
                                    <div class="stat-label">智能体</div>
                                </div>
                                <div class="stat-card">
                                    <div class="stat-number" id="stepCount">0</div>
                                    <div class="stat-label">已配置</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </aside>

                <!-- 右侧智能体编排区域 -->
                <section class="workflow-area">
                    <div class="workflow-header">
                        <div>
                            <h2 class="workflow-title">智能体序列</h2>
                            <p class="workflow-subtitle">拖拽调整执行顺序</p>
                        </div>
                        <button class="add-agent-btn" id="addAgentBtn">
                            <i class="bi bi-plus"></i>
                            <span>添加智能体</span>
                        </button>
                    </div>
                    <div class="agent-list" id="agentList">
                        <!-- 智能体列表将在这里动态生成 -->
                    </div>
                </section>
            </div>
        </main>
    </div>

    <!-- 引入JavaScript -->
    <script src="workflow-designer.js"></script>
</body>
</html>
