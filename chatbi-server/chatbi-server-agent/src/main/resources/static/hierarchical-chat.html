<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>层次化聊天界面 - 计划→智能体→思考</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/echarts@5.4.3/dist/echarts.min.js"></script>
    <style>
        :root {
            --primary-color: #6366f1;
            --secondary-color: #8b5cf6;
            --success-color: #10b981;
            --warning-color: #f59e0b;
            --danger-color: #ef4444;
            --text-primary: #1f2937;
            --text-secondary: #6b7280;
            --bg-primary: #f9fafb;
            --border-color: #e5e7eb;
            --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
            --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1);

            /* 层次化颜色 */
            --plan-color: #6366f1;
            --agent-color: #8b5cf6;
            --think-color: #10b981;
            --tool-color: #f59e0b;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: var(--bg-primary);
            color: var(--text-primary);
            height: 100vh;
            overflow: hidden;
        }

        .app-container {
            display: flex;
            height: 100vh;
            width: 100vw;
        }

        /* 侧边栏样式 */
        .sidebar {
            width: 320px;
            background: white;
            border-right: 1px solid var(--border-color);
            display: flex;
            flex-direction: column;
            transition: all 0.3s ease;
        }

        .sidebar.collapsed {
            width: 60px;
        }

        .sidebar-header {
            padding: 20px;
            border-bottom: 1px solid var(--border-color);
        }

        .app-title {
            font-size: 1.25rem;
            font-weight: 700;
            color: var(--primary-color);
            margin-bottom: 15px;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .new-chat-btn {
            width: 100%;
            background: var(--primary-color);
            color: white;
            border: none;
            padding: 12px 16px;
            border-radius: 8px;
            font-weight: 500;
            cursor: pointer;
            display: flex;
            align-items: center;
            gap: 8px;
            transition: all 0.2s ease;
        }

        .new-chat-btn:hover {
            background: var(--secondary-color);
            transform: translateY(-1px);
        }

        /* 用户信息样式 */
        .user-info {
            margin: 15px 0;
            padding: 12px;
            background: var(--bg-primary);
            border-radius: 8px;
            border: 1px solid var(--border-color);
        }

        /* 历史会话样式 */
        .chat-history {
            flex: 1;
            display: flex;
            flex-direction: column;
            overflow: hidden;
        }

        .history-header {
            padding: 15px 20px 10px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            border-bottom: 1px solid var(--border-color);
        }

        .history-header h5 {
            margin: 0;
            font-size: 0.875rem;
            font-weight: 600;
            color: var(--text-primary);
        }

        .history-search {
            padding: 10px 20px;
        }

        .search-input-wrapper {
            position: relative;
        }

        .search-input {
            width: 100%;
            padding: 8px 12px 8px 32px;
            border: 1px solid var(--border-color);
            border-radius: 6px;
            font-size: 0.75rem;
            background: var(--bg-primary);
            transition: border-color 0.2s ease;
        }

        .search-input:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
        }

        .search-icon {
            position: absolute;
            left: 10px;
            top: 50%;
            transform: translateY(-50%);
            color: var(--text-secondary);
            font-size: 0.75rem;
        }

        .history-content {
            flex: 1;
            overflow-y: auto;
            padding: 0 10px;
        }

        .loading-placeholder {
            text-align: center;
            padding: 20px;
            color: var(--text-secondary);
            font-size: 0.875rem;
        }

        .loading-placeholder i {
            font-size: 1.5rem;
            margin-bottom: 8px;
        }

        .history-group {
            margin-bottom: 20px;
        }

        .history-group-title {
            font-size: 0.75rem;
            font-weight: 600;
            color: var(--text-secondary);
            padding: 8px 10px 4px;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .history-item {
            padding: 10px;
            margin: 2px 0;
            border-radius: 6px;
            cursor: pointer;
            transition: all 0.2s ease;
            border: 1px solid transparent;
        }

        .history-item:hover {
            background: var(--bg-primary);
            border-color: var(--border-color);
        }

        .history-item.active {
            background: var(--primary-color);
            color: white;
            border-color: var(--primary-color);
        }

        .history-item-title {
            font-size: 0.875rem;
            font-weight: 500;
            margin-bottom: 4px;
            line-height: 1.3;
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
            overflow: hidden;
        }

        .history-item-meta {
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-size: 0.75rem;
            opacity: 0.8;
        }

        .history-item-time {
            color: var(--text-secondary);
        }

        .history-item.active .history-item-time {
            color: rgba(255, 255, 255, 0.8);
        }

        .history-item-actions {
            display: flex;
            gap: 4px;
            opacity: 0;
            transition: opacity 0.2s ease;
        }

        .history-item:hover .history-item-actions {
            opacity: 1;
        }

        .history-item-action {
            background: transparent;
            border: none;
            color: var(--text-secondary);
            padding: 2px;
            border-radius: 3px;
            cursor: pointer;
            font-size: 0.75rem;
        }

        .history-item-action:hover {
            background: rgba(0, 0, 0, 0.1);
        }

        .history-item.active .history-item-action {
            color: rgba(255, 255, 255, 0.8);
        }

        .history-item.active .history-item-action:hover {
            background: rgba(255, 255, 255, 0.2);
        }

        .empty-history {
            text-align: center;
            padding: 40px 20px;
            color: var(--text-secondary);
            font-size: 0.875rem;
        }

        .empty-history i {
            font-size: 2rem;
            margin-bottom: 10px;
            display: block;
        }

        .user-id-display {
            display: flex;
            align-items: center;
            gap: 8px;
            font-size: 0.875rem;
            color: var(--text-primary);
        }

        .user-id-display i {
            color: var(--primary-color);
            font-size: 1rem;
        }

        .user-id-display span {
            flex: 1;
            font-weight: 500;
            min-width: 0;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }

        .user-id-edit {
            margin-top: 10px;
        }

        .btn-icon {
            background: transparent;
            border: none;
            color: var(--text-secondary);
            padding: 4px;
            border-radius: 4px;
            cursor: pointer;
            transition: all 0.2s ease;
            font-size: 0.875rem;
        }

        .btn-icon:hover {
            background: var(--bg-primary);
            color: var(--primary-color);
        }

        .mt-2 {
            margin-top: 8px;
        }

        .new-chat-settings {
            margin-top: 15px;
            padding: 15px;
            background: var(--bg-primary);
            border-radius: 8px;
            display: none;
        }

        .form-group {
            margin-bottom: 15px;
        }

        .form-label {
            display: block;
            margin-bottom: 5px;
            font-weight: 500;
            color: var(--text-primary);
            font-size: 0.875rem;
        }

        .form-input {
            width: 100%;
            padding: 8px 12px;
            border: 1px solid var(--border-color);
            border-radius: 6px;
            font-size: 0.875rem;
            transition: border-color 0.2s ease;
        }

        .form-input:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
        }

        .btn {
            padding: 8px 16px;
            border: none;
            border-radius: 6px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s ease;
            font-size: 0.875rem;
        }

        .btn-primary {
            background: var(--primary-color);
            color: white;
        }

        .btn-primary:hover {
            background: var(--secondary-color);
        }

        .btn-secondary {
            background: var(--text-secondary);
            color: white;
        }

        .btn-secondary:hover {
            background: var(--text-primary);
        }

        .w-100 {
            width: 100%;
        }

        .d-flex {
            display: flex;
        }

        .gap-2 {
            gap: 8px;
        }

        .flex-1 {
            flex: 1;
        }

        /* 主内容区域 */
        .main-content {
            flex: 1;
            display: flex;
            flex-direction: column;
            background: white;
        }

        .chat-header {
            padding: 20px 30px;
            border-bottom: 1px solid var(--border-color);
            display: flex;
            justify-content: space-between;
            align-items: center;
            background: white;
        }

        .chat-title {
            font-size: 1.5rem;
            font-weight: 600;
            color: var(--text-primary);
            margin: 0;
        }

        .chat-actions {
            display: flex;
            gap: 10px;
        }

        .action-btn {
            background: transparent;
            border: 1px solid var(--border-color);
            color: var(--text-secondary);
            padding: 8px 12px;
            border-radius: 6px;
            cursor: pointer;
            display: flex;
            align-items: center;
            gap: 6px;
            font-size: 0.875rem;
            transition: all 0.2s ease;
        }

        .action-btn:hover {
            background: var(--bg-primary);
            border-color: var(--primary-color);
            color: var(--primary-color);
        }

        .chat-container {
            flex: 1;
            overflow-y: auto;
            padding: 20px 30px;
            background: var(--bg-primary);
        }

        .message {
            margin-bottom: 20px;
            display: flex;
            gap: 12px;
            animation: messageSlideIn 0.3s ease;
        }

        @keyframes messageSlideIn {
            from {
                opacity: 0;
                transform: translateY(10px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .message-avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.2rem;
            flex-shrink: 0;
        }

        .user-message .message-avatar {
            background: var(--primary-color);
            color: white;
        }

        .ai-message .message-avatar {
            background: var(--success-color);
            color: white;
        }

        .system-message .message-avatar {
            background: var(--warning-color);
            color: white;
        }

        .message-content {
            flex: 1;
            background: white;
            padding: 12px 16px;
            border-radius: 12px;
            border: 1px solid var(--border-color);
            position: relative;
            box-shadow: var(--shadow-sm);
        }

        .user-message .message-content {
            background: var(--primary-color);
            color: white;
            border-color: var(--primary-color);
        }

        .message-time {
            font-size: 0.75rem;
            color: var(--text-secondary);
            margin-top: 8px;
        }

        .user-message .message-time {
            color: rgba(255, 255, 255, 0.8);
        }

        /* 输入区域 */
        .chat-input {
            padding: 20px 30px;
            border-top: 1px solid var(--border-color);
            background: white;
        }

        .input-container {
            display: flex;
            gap: 12px;
            align-items: flex-end;
        }

        .message-input-wrapper {
            flex: 1;
            position: relative;
        }

        .message-input {
            width: 100%;
            min-height: 44px;
            max-height: 120px;
            padding: 12px 16px;
            border: 1px solid var(--border-color);
            border-radius: 12px;
            resize: none;
            font-family: inherit;
            font-size: 0.875rem;
            line-height: 1.5;
            transition: border-color 0.2s ease;
        }

        .message-input:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
        }

        .send-btn {
            background: var(--primary-color);
            color: white;
            border: none;
            padding: 12px 16px;
            border-radius: 12px;
            cursor: pointer;
            display: flex;
            align-items: center;
            gap: 6px;
            font-weight: 500;
            transition: all 0.2s ease;
        }

        .send-btn:hover {
            background: var(--secondary-color);
            transform: translateY(-1px);
        }

        .send-btn:disabled {
            background: var(--text-secondary);
            cursor: not-allowed;
            transform: none;
        }

        /* ==================== 层次化结构样式 ==================== */

        /* 计划容器 - 最外层 */
        .plan-container {
            margin: 20px 0;
            border: 2px solid var(--plan-color);
            border-radius: 16px;
            background: linear-gradient(135deg, rgba(99, 102, 241, 0.05), rgba(99, 102, 241, 0.02));
            overflow: hidden;
            animation: planSlideIn 0.5s ease;
        }

        @keyframes planSlideIn {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .plan-header {
            background: var(--plan-color);
            color: white;
            padding: 16px 20px;
            display: flex;
            align-items: center;
            gap: 12px;
            font-weight: 600;
        }

        .plan-header i {
            font-size: 1.2rem;
        }

        .plan-title {
            flex: 1;
            font-size: 1.1rem;
            line-height: 1.4;
        }

        .plan-status {
            font-size: 0.875rem;
            background: rgba(255, 255, 255, 0.2);
            padding: 4px 8px;
            border-radius: 12px;
        }

        .plan-content {
            padding: 20px;
        }

        .plan-info {
            background: rgba(99, 102, 241, 0.1);
            border-radius: 8px;
            padding: 12px 16px;
            margin-bottom: 16px;
            font-size: 0.875rem;
            border-left: 4px solid var(--plan-color);
        }

        .plan-steps {
            margin: 16px 0;
        }

        .plan-step {
            padding: 8px 12px;
            margin: 4px 0;
            border-radius: 6px;
            font-size: 0.875rem;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .plan-step.not-started {
            background: rgba(107, 114, 128, 0.1);
            color: var(--text-secondary);
        }

        .plan-step.in-progress {
            background: rgba(251, 191, 36, 0.1);
            color: var(--warning-color);
            border-left: 3px solid var(--warning-color);
        }

        .plan-step.completed {
            background: rgba(16, 185, 129, 0.1);
            color: var(--success-color);
            border-left: 3px solid var(--success-color);
        }

        /* 智能体容器 - 中层 */
        .agent-container {
            margin: 16px 0;
            border: 2px solid var(--agent-color);
            border-radius: 12px;
            background: linear-gradient(135deg, rgba(139, 92, 246, 0.05), rgba(139, 92, 246, 0.02));
            overflow: hidden;
            animation: agentSlideIn 0.4s ease;
        }

        @keyframes agentSlideIn {
            from {
                opacity: 0;
                transform: translateX(-10px);
            }
            to {
                opacity: 1;
                transform: translateX(0);
            }
        }

        .agent-header {
            background: var(--agent-color);
            color: white;
            padding: 12px 16px;
            display: flex;
            align-items: center;
            gap: 10px;
            font-weight: 500;
        }

        .agent-header i {
            font-size: 1.1rem;
        }

        .agent-name {
            flex: 1;
            font-size: 1rem;
        }

        .agent-status {
            font-size: 0.75rem;
            background: rgba(255, 255, 255, 0.2);
            padding: 3px 6px;
            border-radius: 10px;
        }

        .agent-content {
            padding: 16px;
        }

        .agent-info {
            background: rgba(139, 92, 246, 0.1);
            border-radius: 6px;
            padding: 10px 12px;
            margin-bottom: 12px;
            font-size: 0.8rem;
            border-left: 3px solid var(--agent-color);
        }

        /* 思考容器 - 内层 */
        .think-container {
            margin: 12px 0;
            border: 1px solid var(--think-color);
            border-radius: 8px;
            background: linear-gradient(135deg, rgba(16, 185, 129, 0.05), rgba(16, 185, 129, 0.02));
            overflow: hidden;
            animation: thinkSlideIn 0.3s ease;
        }

        @keyframes thinkSlideIn {
            from {
                opacity: 0;
                transform: scale(0.98);
            }
            to {
                opacity: 1;
                transform: scale(1);
            }
        }

        .think-header {
            background: var(--think-color);
            color: white;
            padding: 8px 12px;
            display: flex;
            align-items: center;
            gap: 8px;
            font-weight: 500;
            font-size: 0.875rem;
        }

        .think-header i {
            font-size: 1rem;
        }

        .think-title {
            flex: 1;
        }

        .think-status {
            font-size: 0.7rem;
            background: rgba(255, 255, 255, 0.2);
            padding: 2px 6px;
            border-radius: 8px;
        }

        .think-content {
            padding: 12px;
            font-family: 'SF Mono', Monaco, 'Cascadia Code', 'Roboto Mono', Consolas, 'Courier New', monospace;
            font-size: 0.875rem;
            line-height: 1.6;
            white-space: pre-wrap;
            background: rgba(16, 185, 129, 0.03);
        }

        .think-content.streaming {
            position: relative;
        }

        .think-content.streaming::after {
            content: '▊';
            color: var(--think-color);
            animation: blink 1s infinite;
        }

        @keyframes blink {
            0%, 50% { opacity: 1; }
            51%, 100% { opacity: 0; }
        }

        /* 工具调用样式 */
        .tool-container {
            margin: 8px 0;
            border: 1px solid var(--tool-color);
            border-radius: 6px;
            background: linear-gradient(135deg, rgba(245, 158, 11, 0.05), rgba(245, 158, 11, 0.02));
            overflow: hidden;
        }

        .tool-header {
            background: var(--tool-color);
            color: white;
            padding: 8px 12px;
            display: flex;
            align-items: center;
            gap: 8px;
            font-weight: 500;
            font-size: 0.85rem;
        }

        .tool-status {
            margin-left: auto;
            font-size: 0.75rem;
            background: rgba(255, 255, 255, 0.2);
            padding: 2px 8px;
            border-radius: 12px;
        }

        .tool-content {
            padding: 0;
            background: rgba(245, 158, 11, 0.03);
        }

        .tool-params, .tool-result {
            border-bottom: 1px solid rgba(245, 158, 11, 0.1);
        }

        .tool-result {
            border-bottom: none;
        }

        .tool-section-title {
            background: rgba(245, 158, 11, 0.08);
            padding: 8px 12px;
            font-weight: 500;
            font-size: 0.8rem;
            color: #d97706;
            border-bottom: 1px solid rgba(245, 158, 11, 0.15);
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: space-between;
            user-select: none;
            transition: background-color 0.2s ease;
        }

        .tool-section-title:hover {
            background: rgba(245, 158, 11, 0.12);
        }

        .tool-section-title .collapse-icon {
            font-size: 0.7rem;
            transition: transform 0.2s ease;
            color: #d97706;
        }

        .tool-section-title.collapsed .collapse-icon {
            transform: rotate(-90deg);
        }

        .tool-section-content {
            padding: 12px;
            font-family: 'SF Mono', Monaco, 'Cascadia Code', 'Roboto Mono', Consolas, 'Courier New', monospace;
            font-size: 0.8rem;
            line-height: 1.5;
            white-space: pre-wrap;
            color: #374151;
            background: white;
            overflow: hidden;
            transition: max-height 0.3s ease, padding 0.3s ease;
            max-height: 1000px;
        }

        .tool-section-content.collapsed {
            max-height: 0;
            padding: 0 12px;
        }

        .tool-result-content {
            color: #059669;
            min-height: 24px;
        }

        /* 加载状态样式 */
        .tool-loading {
            display: flex;
            align-items: center;
            gap: 8px;
            color: #6b7280;
            font-style: italic;
        }

        .tool-loading .loading-spinner {
            width: 12px;
            height: 12px;
            border: 2px solid #e5e7eb;
            border-top: 2px solid #d97706;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        /* 打字机效果 */
        .typewriter-cursor {
            display: inline-block;
            width: 2px;
            height: 1em;
            background-color: #059669;
            animation: blink 1s infinite;
            margin-left: 2px;
        }

        .think-content.streaming .typewriter-cursor {
            display: inline-block;
        }

        .think-content:not(.streaming) .typewriter-cursor {
            display: none;
        }

        /* 完成状态样式 */
        .completed-indicator {
            background: var(--success-color);
            color: white;
            padding: 12px 16px;
            border-radius: 8px;
            margin: 16px 0;
            display: flex;
            align-items: center;
            gap: 10px;
            font-weight: 500;
            animation: completedPulse 0.5s ease;
        }

        @keyframes completedPulse {
            0% { transform: scale(0.95); opacity: 0; }
            50% { transform: scale(1.02); }
            100% { transform: scale(1); opacity: 1; }
        }

        /* 总结容器样式 */
        .summary-container {
            margin-top: 16px;
            border: 1px solid #e0e0e0;
            border-radius: 8px;
            background: #f8f9fa;
            overflow: hidden;
        }

        .summary-header {
            display: flex;
            align-items: center;
            gap: 8px;
            padding: 12px 16px;
            background: linear-gradient(135deg, #e3f2fd, #bbdefb);
            border-bottom: 1px solid #e0e0e0;
            font-weight: 500;
            color: #1976d2;
        }

        .summary-header i {
            font-size: 1.1rem;
        }

        .summary-title {
            flex: 1;
        }

        .summary-status {
            font-size: 0.85rem;
            color: #666;
            background: rgba(255, 255, 255, 0.7);
            padding: 2px 8px;
            border-radius: 12px;
        }

        .summary-content {
            padding: 16px;
            line-height: 1.6;
            white-space: pre-wrap;
            color: #333;
            min-height: 40px;
        }

        /* 对话轮次样式 */
        .conversation-round {
            margin-bottom: 24px;
            border-bottom: 1px solid #f0f0f0;
            padding-bottom: 16px;
        }

        .conversation-round:last-child {
            border-bottom: none;
            margin-bottom: 0;
        }

        .ai-response-container {
            margin-top: 12px;
        }

        .ai-response-content {
            /* AI回复内容容器，用于放置计划、智能体等组件 */
        }

        /* 图表容器样式 */
        .chart-container {
            margin: 16px 0;
            border: 1px solid var(--border-color);
            border-radius: 8px;
            background: white;
            overflow: hidden;
            box-shadow: var(--shadow-sm);
        }

        .chart-header {
            background: linear-gradient(135deg, #f8fafc, #e2e8f0);
            padding: 12px 16px;
            border-bottom: 1px solid var(--border-color);
            display: flex;
            align-items: center;
            gap: 8px;
            font-weight: 500;
            color: var(--text-primary);
        }

        .chart-header i {
            color: var(--primary-color);
            font-size: 1.1rem;
        }

        .chart-title {
            flex: 1;
            font-size: 0.95rem;
        }

        .chart-type-badge {
            background: var(--primary-color);
            color: white;
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 0.75rem;
            text-transform: uppercase;
        }

        .chart-content {
            padding: 0;
            position: relative;
        }

        .chart-canvas {
            width: 100%;
            height: 400px;
            min-height: 300px;
        }

        .chart-loading {
            display: flex;
            align-items: center;
            justify-content: center;
            height: 200px;
            color: var(--text-secondary);
            font-style: italic;
        }

        .chart-loading .loading-spinner {
            width: 20px;
            height: 20px;
            border: 2px solid #e5e7eb;
            border-top: 2px solid var(--primary-color);
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin-right: 8px;
        }

        .chart-error {
            padding: 20px;
            text-align: center;
            color: var(--danger-color);
            background: rgba(239, 68, 68, 0.05);
            border: 1px solid rgba(239, 68, 68, 0.1);
            border-radius: 6px;
            margin: 16px;
        }

        .chart-error i {
            font-size: 1.5rem;
            margin-bottom: 8px;
            display: block;
        }

        /* 数据表格样式 */
        .data-table-container {
            margin: 16px 0;
            border: 1px solid var(--border-color);
            border-radius: 8px;
            background: white;
            overflow: hidden;
            box-shadow: var(--shadow-sm);
        }

        .data-table-header {
            background: linear-gradient(135deg, #f1f5f9, #e2e8f0);
            padding: 12px 16px;
            border-bottom: 1px solid var(--border-color);
            display: flex;
            align-items: center;
            gap: 8px;
            font-weight: 500;
            color: var(--text-primary);
        }

        .data-table-header i {
            color: var(--success-color);
            font-size: 1.1rem;
        }

        .data-table-content {
            overflow-x: auto;
            max-height: 300px;
            overflow-y: auto;
        }

        .data-table {
            width: 100%;
            border-collapse: collapse;
            font-size: 0.875rem;
        }

        .data-table th {
            background: #f8fafc;
            padding: 8px 12px;
            text-align: left;
            font-weight: 500;
            color: var(--text-primary);
            border-bottom: 1px solid var(--border-color);
            position: sticky;
            top: 0;
            z-index: 1;
        }

        .data-table td {
            padding: 8px 12px;
            border-bottom: 1px solid #f1f5f9;
            color: var(--text-secondary);
        }

        .data-table tbody tr:hover {
            background: #f8fafc;
        }

        .data-table tbody tr:last-child td {
            border-bottom: none;
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .plan-container, .agent-container, .think-container {
                margin: 12px 0;
            }

            .plan-header, .agent-header, .think-header {
                padding: 12px 16px;
            }

            .plan-content, .agent-content, .think-content {
                padding: 12px;
            }

            .chart-canvas {
                height: 300px;
                min-height: 250px;
            }

            .chart-header, .data-table-header {
                padding: 10px 12px;
            }
        }
    </style>
</head>
<body>
    <div class="app-container">
        <!-- 侧边栏 -->
        <div class="sidebar" id="sidebar">
            <div class="sidebar-header">
                <h1 class="app-title">
                    <i class="bi bi-diagram-3"></i>
                    <span>层次化AI助手</span>
                </h1>

                <!-- 用户ID显示区域 -->
                <div class="user-info" id="userInfo">
                    <div class="user-id-display">
                        <i class="bi bi-person-circle"></i>
                        <span id="currentUserDisplay">test-user-001</span>
                        <button class="btn-icon" id="editUserBtn" title="编辑用户ID">
                            <i class="bi bi-pencil"></i>
                        </button>
                    </div>
                    <div class="user-id-edit" id="userIdEdit" style="display: none;">
                        <input type="text" class="form-input" id="userIdInput" placeholder="输入用户ID">
                        <div class="d-flex gap-2 mt-2">
                            <button class="btn btn-primary flex-1" id="saveUserBtn">
                                <i class="bi bi-check"></i>
                                保存
                            </button>
                            <button class="btn btn-secondary" id="cancelUserBtn">
                                <i class="bi bi-x"></i>
                                取消
                            </button>
                        </div>
                    </div>
                </div>

                <button class="new-chat-btn" id="newChatBtn">
                    <i class="bi bi-plus-lg"></i>
                    <span>新对话</span>
                </button>

                <!-- 新对话设置 -->
                <div class="new-chat-settings" id="newChatSettings">
                    <div class="form-group">
                        <label class="form-label">计划模板ID</label>
                        <input type="text" class="form-input" id="newChatPlanTemplate" value="planTemplate-1749011464049">
                    </div>
                    <div class="form-group">
                        <label class="form-label">用户ID</label>
                        <input type="text" class="form-input" id="newChatUserId" value="test-user-001">
                    </div>
                    <div class="form-group">
                        <label class="form-label">对话名称</label>
                        <input type="text" class="form-input" id="newChatName" placeholder="留空自动生成">
                    </div>
                    <div class="form-group">
                        <label class="form-label">工具上下文 (JSON格式)</label>
                        <textarea class="form-input" id="newChatToolContext" rows="4" placeholder='{"dataSourceId": "123", "databaseName": "test_db", "tenantId": "acme"}'></textarea>
                        <small style="color: #6b7280; font-size: 0.75rem; margin-top: 4px; display: block;">
                            输入JSON格式的工具上下文参数，用于传递给数据库工具等。留空则不传递额外参数。
                        </small>
                    </div>
                    <div class="d-flex gap-2">
                        <button class="btn btn-primary flex-1" id="confirmNewChatBtn">
                            <i class="bi bi-check"></i>
                            确认创建
                        </button>
                        <button class="btn btn-secondary" id="cancelNewChatBtn">
                            <i class="bi bi-x"></i>
                            取消
                        </button>
                    </div>
                </div>
            </div>

            <!-- 历史会话区域 -->
            <div class="chat-history" id="chatHistory">
                <div class="history-header">
                    <h5>历史对话</h5>
                    <button class="btn-icon" id="refreshHistoryBtn" title="刷新历史">
                        <i class="bi bi-arrow-clockwise"></i>
                    </button>
                </div>

                <div class="history-search">
                    <div class="search-input-wrapper">
                        <input type="text" class="search-input" id="historySearchInput" placeholder="搜索对话...">
                        <i class="bi bi-search search-icon"></i>
                    </div>
                </div>

                <div class="history-content" id="historyContent">
                    <div class="loading-placeholder">
                        <i class="bi bi-clock-history"></i>
                        <span>加载历史对话...</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- 主内容区域 -->
        <div class="main-content">
            <div class="chat-header">
                <h2 class="chat-title" id="chatTitle">层次化对话</h2>
                <div class="chat-actions">
                    <button class="action-btn" id="debugBtn">
                        <i class="bi bi-bug"></i>
                        <span>调试历史</span>
                    </button>
                    <button class="action-btn" id="clearBtn">
                        <i class="bi bi-trash"></i>
                        <span>清空</span>
                    </button>
                </div>
            </div>

            <div class="chat-container" id="chatContainer">
                <div class="message system-message">
                    <div class="message-avatar">
                        <i class="bi bi-info-circle"></i>
                    </div>
                    <div class="message-content">
                        欢迎使用层次化AI智能助手！<br>
                        <small>📋 计划 → 🤖 智能体 → 💭 思考 的三层结构展示</small>
                        <div class="message-time">${new Date().toLocaleTimeString()}</div>
                    </div>
                </div>
            </div>

            <div class="chat-input">
                <div class="input-container">
                    <div class="message-input-wrapper">
                        <textarea
                            class="message-input"
                            id="messageInput"
                            placeholder="输入您的消息..."
                            rows="1"></textarea>
                    </div>
                    <button class="send-btn" id="sendBtn">
                        <i class="bi bi-send"></i>
                        <span>发送</span>
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script>
        console.log('层次化聊天页面开始加载...');

        // 全局变量
        let currentChatId = null;
        let currentUserId = 'test-user-001';
        let currentPlanTemplateId = 'planTemplate-1749011464049';
        let currentChatTitle = '层次化对话';
        let currentToolContext = {}; // 工具上下文参数

        // 流式事件相关变量
        let eventSource = null;
        let currentConversationRound = null;
        let currentPlanContainer = null;
        let planData = {};
        let agentData = {};
        let thinkData = {};
        let conversationRounds = {}; // 存储所有对话轮次 {planId: roundContainer}

        // DOM元素
        let chatContainer, messageInput, chatTitle, sendBtn, historyContent, historySearchInput;

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            console.log('DOM加载完成，开始初始化...');

            try {
                // 获取DOM元素
                chatContainer = document.getElementById('chatContainer');
                messageInput = document.getElementById('messageInput');
                chatTitle = document.getElementById('chatTitle');
                sendBtn = document.getElementById('sendBtn');
                historyContent = document.getElementById('historyContent');
                historySearchInput = document.getElementById('historySearchInput');

                console.log('DOM元素获取完成:', {
                    chatContainer: !!chatContainer,
                    messageInput: !!messageInput,
                    chatTitle: !!chatTitle,
                    sendBtn: !!sendBtn,
                    historyContent: !!historyContent,
                    historySearchInput: !!historySearchInput
                });

                // 设置事件监听器
                setupEventListeners();

                // 设置跨页面消息监听
                setupCrossPageCommunication();

                // 检查URL参数
                checkUrlParameters();

                console.log('初始化完成');
            } catch (error) {
                console.error('初始化错误:', error);
                alert('页面初始化失败: ' + error.message);
            }
        });

        function setupEventListeners() {
            console.log('设置事件监听器...');

            try {
                // 新对话按钮
                const newChatBtn = document.getElementById('newChatBtn');
                if (newChatBtn) {
                    newChatBtn.addEventListener('click', function() {
                        console.log('新对话按钮被点击');
                        showNewChatSettings();
                    });
                }

                // 确认创建按钮
                const confirmBtn = document.getElementById('confirmNewChatBtn');
                if (confirmBtn) {
                    confirmBtn.addEventListener('click', function() {
                        console.log('确认创建按钮被点击');
                        confirmNewChat();
                    });
                }

                // 取消按钮
                const cancelBtn = document.getElementById('cancelNewChatBtn');
                if (cancelBtn) {
                    cancelBtn.addEventListener('click', function() {
                        console.log('取消按钮被点击');
                        hideNewChatSettings();
                    });
                }

                // 发送按钮
                if (sendBtn) {
                    sendBtn.addEventListener('click', function() {
                        console.log('发送按钮被点击');
                        sendMessage();
                    });
                }

                // 调试按钮
                const debugBtn = document.getElementById('debugBtn');
                if (debugBtn) {
                    debugBtn.addEventListener('click', function() {
                        console.log('调试按钮被点击');
                        testHistoryRendering();
                    });
                }

                // 清空按钮
                const clearBtn = document.getElementById('clearBtn');
                if (clearBtn) {
                    clearBtn.addEventListener('click', function() {
                        console.log('清空按钮被点击');
                        clearChat();
                    });
                }

                // 输入框回车事件
                if (messageInput) {
                    messageInput.addEventListener('keydown', function(event) {
                        if (event.key === 'Enter' && !event.shiftKey) {
                            event.preventDefault();
                            console.log('回车键被按下');
                            sendMessage();
                        }
                    });

                    messageInput.addEventListener('input', autoResizeTextarea);
                }

                // 用户ID编辑功能
                const editUserBtn = document.getElementById('editUserBtn');
                const saveUserBtn = document.getElementById('saveUserBtn');
                const cancelUserBtn = document.getElementById('cancelUserBtn');
                const userIdInput = document.getElementById('userIdInput');
                const userIdEdit = document.getElementById('userIdEdit');
                const currentUserDisplay = document.getElementById('currentUserDisplay');

                if (editUserBtn) {
                    editUserBtn.addEventListener('click', function() {
                        userIdInput.value = currentUserId;
                        userIdEdit.style.display = 'block';
                        userIdInput.focus();
                    });
                }

                if (saveUserBtn) {
                    saveUserBtn.addEventListener('click', function() {
                        const newUserId = userIdInput.value.trim();
                        if (newUserId) {
                            currentUserId = newUserId;
                            currentUserDisplay.textContent = newUserId;
                            userIdEdit.style.display = 'none';

                            // 更新新对话设置中的用户ID
                            const newChatUserIdInput = document.getElementById('newChatUserId');
                            if (newChatUserIdInput) {
                                newChatUserIdInput.value = newUserId;
                            }
                        }
                    });
                }

                if (cancelUserBtn) {
                    cancelUserBtn.addEventListener('click', function() {
                        userIdEdit.style.display = 'none';
                    });
                }

                if (userIdInput) {
                    userIdInput.addEventListener('keydown', function(event) {
                        if (event.key === 'Enter') {
                            saveUserBtn.click();
                        } else if (event.key === 'Escape') {
                            cancelUserBtn.click();
                        }
                    });
                }

                // 历史窗口事件
                const refreshHistoryBtn = document.getElementById('refreshHistoryBtn');
                if (refreshHistoryBtn) {
                    refreshHistoryBtn.addEventListener('click', function() {
                        console.log('刷新历史按钮被点击');
                        loadChatHistory();
                    });
                    console.log('✅ 刷新历史按钮事件监听器已设置');
                }

                // 搜索输入框事件
                if (historySearchInput) {
                    let searchTimeout;
                    historySearchInput.addEventListener('input', function() {
                        clearTimeout(searchTimeout);
                        searchTimeout = setTimeout(() => {
                            const keyword = historySearchInput.value.trim();
                            console.log('搜索历史对话:', keyword);
                            if (keyword) {
                                searchChatHistory(keyword);
                            } else {
                                loadChatHistory();
                            }
                        }, 500);
                    });
                    console.log('✅ 搜索输入框事件监听器已设置');
                }

                console.log('✅ 事件监听器设置完成');

                // 初始化时加载历史对话
                loadChatHistory();
            } catch (error) {
                console.error('❌ 设置事件监听器失败:', error);
            }
        }

        // ==================== 新对话管理 ====================

        function showNewChatSettings() {
            const settings = document.getElementById('newChatSettings');
            if (settings) {
                settings.style.display = 'block';
            }
        }

        function hideNewChatSettings() {
            const settings = document.getElementById('newChatSettings');
            if (settings) {
                settings.style.display = 'none';
            }
        }

        function confirmNewChat() {
            try {
                const planTemplateInput = document.getElementById('newChatPlanTemplate');
                const userIdInput = document.getElementById('newChatUserId');
                const chatNameInput = document.getElementById('newChatName');
                const toolContextInput = document.getElementById('newChatToolContext');

                currentPlanTemplateId = planTemplateInput.value.trim() || 'planTemplate-1749011464049';
                currentUserId = userIdInput.value.trim() || 'test-user-001';
                const chatName = chatNameInput.value.trim();

                // 解析工具上下文
                const toolContextText = toolContextInput.value.trim();
                if (toolContextText) {
                    try {
                        currentToolContext = JSON.parse(toolContextText);
                        console.log('✅ 工具上下文解析成功:', currentToolContext);
                    } catch (parseError) {
                        alert('工具上下文JSON格式错误: ' + parseError.message);
                        return;
                    }
                } else {
                    currentToolContext = {};
                }

                // 生成新的chatId (简化格式)
                currentChatId = 'chat-' + Date.now() + '-' + Math.random().toString(36).substr(2, 6);
                currentChatTitle = chatName || '新对话';

                // 更新标题
                if (chatTitle) {
                    chatTitle.textContent = currentChatTitle;
                }

                // 清空聊天容器
                clearChat();

                // 隐藏设置
                hideNewChatSettings();

                // 添加系统消息
                const toolContextInfo = Object.keys(currentToolContext).length > 0
                    ? `\n工具上下文: ${JSON.stringify(currentToolContext, null, 2)}`
                    : '';
                addMessage(`新对话已创建！\n会话ID: ${currentChatId}\n计划模板: ${currentPlanTemplateId}${toolContextInfo}`, 'system');

                console.log('✅ 新对话创建成功', {
                    chatId: currentChatId,
                    planTemplateId: currentPlanTemplateId,
                    userId: currentUserId,
                    chatTitle: currentChatTitle,
                    toolContext: currentToolContext
                });
            } catch (error) {
                console.error('❌ 创建新对话失败:', error);
                alert('创建新对话失败: ' + error.message);
            }
        }

        function clearChat() {
            if (chatContainer) {
                chatContainer.innerHTML = `
                    <div class="message system-message">
                        <div class="message-avatar">
                            <i class="bi bi-info-circle"></i>
                        </div>
                        <div class="message-content">
                            欢迎使用层次化AI智能助手！<br>
                            <small>📋 计划 → 🤖 智能体 → 💭 思考 的三层结构展示</small>
                            <div class="message-time">${new Date().toLocaleTimeString()}</div>
                        </div>
                    </div>
                `;
            }

            // 重置数据
            planData = {};
            agentData = {};
            thinkData = {};
            conversationRounds = {};
            currentConversationRound = null;
            currentPlanContainer = null;
        }

        function autoResizeTextarea() {
            if (messageInput) {
                messageInput.style.height = 'auto';
                messageInput.style.height = Math.min(messageInput.scrollHeight, 120) + 'px';
            }
        }

        // ==================== 消息发送 ====================

        function sendMessage() {
            const message = messageInput.value.trim();
            if (!message) return;

            if (!currentChatId) {
                alert('请先创建新对话');
                return;
            }

            try {
                // 创建新的对话轮次
                currentConversationRound = createConversationRound(message);

                // 清空输入框
                messageInput.value = '';
                autoResizeTextarea();

                // 禁用发送按钮
                if (sendBtn) {
                    sendBtn.disabled = true;
                }

                // 构建请求体
                const requestBody = {
                    params: {}, // 执行参数，暂时为空
                    chatId: currentChatId,
                    userId: currentUserId,
                    message: message,
                    toolContext: currentToolContext || {}
                };

                console.log('🚀 发送消息:', { message, requestBody });

                // 发送POST请求并连接到流式事件
                sendPostRequestAndConnectStream(requestBody);

            } catch (error) {
                console.error('❌ 发送消息失败:', error);
                alert('发送消息失败: ' + error.message);

                // 重新启用发送按钮
                if (sendBtn) {
                    sendBtn.disabled = false;
                }
            }
        }

        // ==================== 对话轮次管理 ====================

        function createConversationRound(userMessage) {
            if (!chatContainer) return null;

            // 创建对话轮次容器
            const roundContainer = document.createElement('div');
            roundContainer.className = 'conversation-round';

            // 创建用户消息气泡
            const userMessageDiv = document.createElement('div');
            userMessageDiv.className = 'message user-message';
            userMessageDiv.innerHTML = `
                <div class="message-avatar">
                    <i class="bi bi-person-circle"></i>
                </div>
                <div class="message-content">
                    ${escapeHtml(userMessage)}
                    <div class="message-time">${new Date().toLocaleTimeString()}</div>
                </div>
            `;

            // 创建AI回复容器
            const aiResponseContainer = document.createElement('div');
            aiResponseContainer.className = 'ai-response-container';
            aiResponseContainer.innerHTML = `
                <div class="message ai-message">
                    <div class="message-avatar">
                        <i class="bi bi-robot"></i>
                    </div>
                    <div class="message-content">
                        <div class="ai-response-content">
                            <!-- 这里将放置计划、智能体、思考等内容 -->
                        </div>
                        <div class="message-time">${new Date().toLocaleTimeString()}</div>
                    </div>
                </div>
            `;

            // 组装轮次容器
            roundContainer.appendChild(userMessageDiv);
            roundContainer.appendChild(aiResponseContainer);

            // 添加到聊天容器
            chatContainer.appendChild(roundContainer);
            roundContainer.scrollIntoView({ behavior: 'smooth', block: 'end' });

            console.log('✅ 创建新对话轮次');
            return roundContainer;
        }

        function addMessage(content, type, isStreaming = false) {
            if (!chatContainer) return null;

            const messageDiv = document.createElement('div');
            messageDiv.className = `message ${type}-message`;

            let avatarIcon = 'bi-person-circle';
            if (type === 'ai') avatarIcon = 'bi-robot';
            else if (type === 'system') avatarIcon = 'bi-info-circle';

            messageDiv.innerHTML = `
                <div class="message-avatar">
                    <i class="bi ${avatarIcon}"></i>
                </div>
                <div class="message-content">
                    ${content}
                    <div class="message-time">${new Date().toLocaleTimeString()}</div>
                </div>
            `;

            chatContainer.appendChild(messageDiv);
            messageDiv.scrollIntoView({ behavior: 'smooth', block: 'end' });

            return messageDiv;
        }

        // ==================== 流式事件处理 ====================

        function sendPostRequestAndConnectStream(requestBody) {
            console.log('🚀 发送POST请求并连接流式事件:', requestBody);

            try {
                // 构建API URL - 使用相对路径，自动适配当前端口
                const url = `/api/streaming-events/template/${encodeURIComponent(currentPlanTemplateId)}`;

                // 发送POST请求
                fetch(url, {
                    method: 'POST',
                    mode: 'cors', // 明确指定CORS模式
                    headers: {
                        'Content-Type': 'application/json',
                        'Accept': 'text/event-stream'
                    },
                    body: JSON.stringify(requestBody)
                })
                .then(response => {
                    if (!response.ok) {
                        throw new Error(`HTTP error! status: ${response.status}`);
                    }

                    console.log('✅ POST请求成功，开始读取流式响应');

                    // 获取响应的reader
                    const reader = response.body.getReader();
                    const decoder = new TextDecoder();
                    let buffer = ''; // 用于累积不完整的数据

                    // 处理流式响应
                    function readStream() {
                        return reader.read().then(({ done, value }) => {
                            if (done) {
                                console.log('✅ 流式响应完成');

                                // 处理缓冲区中剩余的数据
                                if (buffer.trim()) {
                                    processSSEChunk(buffer);
                                }

                                // 重新启用发送按钮
                                if (sendBtn) {
                                    sendBtn.disabled = false;
                                }
                                return;
                            }

                            // 解码数据并添加到缓冲区
                            const chunk = decoder.decode(value, { stream: true });
                            buffer += chunk;

                            // 按行分割处理完整的事件
                            const lines = buffer.split('\n');

                            // 保留最后一行（可能不完整）
                            buffer = lines.pop() || '';

                            // 处理完整的行
                            if (lines.length > 0) {
                                const completeChunk = lines.join('\n') + '\n';
                                processSSEChunk(completeChunk);
                            }

                            // 继续读取
                            return readStream();
                        });
                    }

                    return readStream();
                })
                .catch(error => {
                    console.error('❌ POST请求失败:', error);
                    addMessage('发送请求失败: ' + error.message, 'system');

                    // 重新启用发送按钮
                    if (sendBtn) {
                        sendBtn.disabled = false;
                    }
                });

            } catch (error) {
                console.error('❌ 发送POST请求失败:', error);
                addMessage('发送请求失败: ' + error.message, 'system');

                // 重新启用发送按钮
                if (sendBtn) {
                    sendBtn.disabled = false;
                }
            }
        }

        function processSSEChunk(chunk) {
            // 处理SSE格式的数据块
            console.log('🔍 处理SSE数据块:', chunk);

            const lines = chunk.split('\n');

            for (const line of lines) {
                const trimmedLine = line.trim();

                if (trimmedLine.startsWith('data:')) {
                    const eventData = trimmedLine.substring(5).trim(); // 移除 "data:" 前缀并去除空格

                    if (eventData && eventData !== '') {
                        try {
                            console.log('📨 解析SSE事件数据:', eventData);
                            const parsedData = JSON.parse(eventData);
                            handleStreamEvent(parsedData);
                        } catch (e) {
                            console.error('❌ 解析SSE事件失败:', e, eventData);
                        }
                    }
                } else if (trimmedLine === '') {
                    // 空行表示事件结束，这是SSE标准格式
                    continue;
                } else if (trimmedLine.startsWith('event:') || trimmedLine.startsWith('id:') || trimmedLine.startsWith('retry:')) {
                    // 其他SSE字段，暂时忽略
                    console.log('📋 SSE元数据:', trimmedLine);
                }
            }
        }

        function connectToEventStream(url) {
            // 关闭现有连接
            if (eventSource) {
                eventSource.close();
            }

            console.log('🔗 连接到流式事件:', url);

            try {
                // 直接连接到流式事件（GET请求）
                eventSource = new EventSource(url);

                eventSource.onopen = function() {
                    console.log('✅ 流式连接已建立');
                };

                eventSource.onmessage = function(event) {
                    try {
                        const eventData = JSON.parse(event.data);
                        handleStreamEvent(eventData);
                    } catch (e) {
                        console.error('❌ 解析事件失败:', e, event.data);
                    }
                };

                eventSource.onerror = function(error) {
                    console.error('❌ 流式连接错误:', error);
                    if (eventSource) {
                        eventSource.close();
                        eventSource = null;
                    }

                    // 重新启用发送按钮
                    if (sendBtn) {
                        sendBtn.disabled = false;
                    }

                    // 显示错误消息
                    addMessage('连接中断，请重试', 'system');
                };

            } catch (error) {
                console.error('❌ 创建EventSource失败:', error);
                addMessage('连接失败: ' + error.message, 'system');

                // 重新启用发送按钮
                if (sendBtn) {
                    sendBtn.disabled = false;
                }
            }
        }

        function handleStreamEvent(eventData) {
            console.log('📨 收到流式事件:', eventData);

            switch (eventData.type) {
                case 'PLAN_FULL':
                    handlePlanEvent(eventData);
                    break;
                case 'PLAN_INCREMENTAL':
                    handlePlanIncrementalEvent(eventData);
                    break;
                case 'AGENT_FULL':
                    handleAgentEvent(eventData);
                    break;
                case 'AGENT_INCREMENTAL':
                    handleAgentIncrementalEvent(eventData);
                    break;
                case 'THINK_COMPLETE':
                    handleThinkCompleteEvent(eventData);
                    break;
                case 'THINK_CHUNK':
                    handleThinkChunkEvent(eventData);
                    break;
                case 'TOOL_START':
                    handleToolStartEvent(eventData);
                    break;
                case 'TOOL_END':
                    handleToolEndEvent(eventData);
                    break;
                case 'SUMMARY_CHUNK':
                    handleSummaryChunkEvent(eventData);
                    break;
                case 'PLAN_COMPLETED':
                    handlePlanCompletedEvent(eventData);
                    break;
                default:
                    console.log('⚠️ 未处理的事件类型:', eventData.type, eventData);
            }
        }

        // ==================== 计划事件处理 ====================

        function handlePlanEvent(eventData) {
            try {
                // 检查是否为压缩数据
                if (eventData.compressed) {
                    console.log('⚠️ 跳过压缩数据处理:', eventData.type);
                    return;
                }

                const planPayload = JSON.parse(eventData.payload);
                // 优先使用事件中的planId，其次使用payload中的planId
                const planId = eventData.planId || planPayload.planId;
                planData[planId] = planPayload;

                console.log('📋 处理计划事件:', { eventPlanId: eventData.planId, payloadPlanId: planPayload.planId, finalPlanId: planId });

                // 将计划ID与当前对话轮次关联
                if (currentConversationRound) {
                    conversationRounds[planId] = currentConversationRound;
                    console.log('📋 注册对话轮次:', { planId, roundExists: !!currentConversationRound });
                }

                // 创建或更新计划容器
                if (!currentPlanContainer) {
                    currentPlanContainer = createPlanContainer(planPayload);

                    // 添加到当前对话轮次的AI回复容器中
                    if (currentConversationRound) {
                        const aiResponseContent = currentConversationRound.querySelector('.ai-response-content');
                        if (aiResponseContent) {
                            aiResponseContent.appendChild(currentPlanContainer);
                        }
                    }
                } else {
                    updatePlanContainer(currentPlanContainer, planPayload);
                }

                console.log('📋 计划事件处理完成:', planId);
            } catch (error) {
                console.error('❌ 处理计划事件失败:', error);
            }
        }

        function handlePlanIncrementalEvent(eventData) {
            try {
                // 解析增量数据
                let incrementalData;
                if (typeof eventData.payload === 'string') {
                    incrementalData = JSON.parse(eventData.payload);
                } else {
                    incrementalData = eventData.payload;
                }

                const planId = eventData.planId;

                console.log('📋 处理计划增量事件:', { planId, incrementalData });

                if (planData[planId]) {
                    // 更新计划数据
                    Object.assign(planData[planId], incrementalData);

                    console.log('📋 更新后的计划数据:', planData[planId]);

                    // 更新UI
                    if (currentPlanContainer) {
                        updatePlanContainer(currentPlanContainer, planData[planId]);
                    }
                } else {
                    console.warn('⚠️ 未找到对应的计划数据:', planId);
                }
            } catch (error) {
                console.error('❌ 处理计划增量事件失败:', error);
            }
        }

        function createPlanContainer(planData) {
            const container = document.createElement('div');
            container.className = 'plan-container';
            container.dataset.planId = planData.planId;

            const header = document.createElement('div');
            header.className = 'plan-header';
            header.innerHTML = `
                <i class="bi bi-diagram-3"></i>
                <div class="plan-title">${planData.title || '计划执行中...'}</div>
                <div class="plan-status">${planData.completed ? '已完成' : '执行中'}</div>
            `;

            const content = document.createElement('div');
            content.className = 'plan-content';

            const info = document.createElement('div');
            info.className = 'plan-info';
            info.innerHTML = `
                <strong>用户请求:</strong> ${planData.userRequest || '处理中...'}<br>
                <strong>开始时间:</strong> ${planData.startTime ? new Date(planData.startTime).toLocaleString() : '未知'}
            `;

            content.appendChild(info);

            // 添加步骤信息
            if (planData.steps && planData.steps.length > 0) {
                const stepsContainer = document.createElement('div');
                stepsContainer.className = 'plan-steps';

                planData.steps.forEach((step, index) => {
                    const stepDiv = document.createElement('div');
                    stepDiv.className = 'plan-step';

                    // 解析步骤状态
                    let status = 'not-started';
                    if (step.includes('[in_progress]')) status = 'in-progress';
                    else if (step.includes('[completed]')) status = 'completed';

                    stepDiv.classList.add(status);

                    let icon = '⏳';
                    if (status === 'in-progress') icon = '🔄';
                    else if (status === 'completed') icon = '✅';

                    stepDiv.innerHTML = `
                        <span>${icon}</span>
                        <span>${step}</span>
                    `;

                    stepsContainer.appendChild(stepDiv);
                });

                content.appendChild(stepsContainer);
            }

            container.appendChild(header);
            container.appendChild(content);

            return container;
        }

        function updatePlanContainer(container, planData) {
            // 更新标题
            const titleElement = container.querySelector('.plan-title');
            if (titleElement && planData.title) {
                titleElement.textContent = planData.title;
            }

            // 更新状态
            const statusElement = container.querySelector('.plan-status');
            if (statusElement) {
                statusElement.textContent = planData.completed ? '已完成' : '执行中';
            }

            // 更新计划信息（用户请求和开始时间）
            const infoElement = container.querySelector('.plan-info');
            if (infoElement) {
                infoElement.innerHTML = `
                    <strong>用户请求:</strong> ${planData.userRequest || '处理中...'}<br>
                    <strong>开始时间:</strong> ${planData.startTime ? new Date(planData.startTime).toLocaleString() : '未知'}
                `;
            }

            // 更新步骤
            if (planData.steps) {
                const stepsContainer = container.querySelector('.plan-steps');
                if (stepsContainer) {
                    stepsContainer.innerHTML = '';

                    planData.steps.forEach((step, index) => {
                        const stepDiv = document.createElement('div');
                        stepDiv.className = 'plan-step';

                        let status = 'not-started';
                        if (step.includes('[in_progress]')) status = 'in-progress';
                        else if (step.includes('[completed]')) status = 'completed';

                        stepDiv.classList.add(status);

                        let icon = '⏳';
                        if (status === 'in-progress') icon = '🔄';
                        else if (status === 'completed') icon = '✅';

                        stepDiv.innerHTML = `
                            <span>${icon}</span>
                            <span>${step}</span>
                        `;

                        stepsContainer.appendChild(stepDiv);
                    });
                }
            }
        }

        // ==================== 智能体事件处理 ====================

        function handleAgentEvent(eventData) {
            try {
                const agentPayload = JSON.parse(eventData.payload);
                agentData[agentPayload.id] = agentPayload;

                // 创建智能体容器
                const agentContainer = createAgentContainer(agentPayload);

                // 添加到计划容器中
                if (currentPlanContainer) {
                    const planContent = currentPlanContainer.querySelector('.plan-content');
                    planContent.appendChild(agentContainer);
                }
            } catch (error) {
                console.error('❌ 处理智能体事件失败:', error);
            }
        }

        function handleAgentIncrementalEvent(eventData) {
            try {
                const incrementalData = eventData.payload;
                const agentId = eventData.entityId;

                if (agentData[agentId]) {
                    // 更新智能体数据
                    Object.assign(agentData[agentId], incrementalData);

                    // 更新UI
                    const agentContainer = document.querySelector(`[data-agent-id="${agentId}"]`);
                    if (agentContainer) {
                        updateAgentContainer(agentContainer, agentData[agentId]);
                    }
                }
            } catch (error) {
                console.error('❌ 处理智能体增量事件失败:', error);
            }
        }

        function createAgentContainer(agentData) {
            const container = document.createElement('div');
            container.className = 'agent-container';
            container.dataset.agentId = agentData.id;

            const header = document.createElement('div');
            header.className = 'agent-header';
            header.innerHTML = `
                <i class="bi bi-robot"></i>
                <div class="agent-name">${agentData.agentName || '智能体'}</div>
                <div class="agent-status">${getAgentStatusText(agentData.status)}</div>
            `;

            const content = document.createElement('div');
            content.className = 'agent-content';

            const info = document.createElement('div');
            info.className = 'agent-info';
            info.innerHTML = `
                <strong>描述:</strong> ${agentData.agentDescription || '执行中...'}<br>
                <strong>步骤:</strong> ${agentData.currentStep || 0}/${agentData.maxSteps || 5}<br>
                <strong>状态:</strong> ${getAgentStatusText(agentData.status)}
            `;

            content.appendChild(info);
            container.appendChild(header);
            container.appendChild(content);

            return container;
        }

        function updateAgentContainer(container, agentData) {
            // 更新状态
            const statusElement = container.querySelector('.agent-status');
            if (statusElement) {
                statusElement.textContent = getAgentStatusText(agentData.status);
            }

            // 更新信息
            const infoElement = container.querySelector('.agent-info');
            if (infoElement) {
                infoElement.innerHTML = `
                    <strong>描述:</strong> ${agentData.agentDescription || '执行中...'}<br>
                    <strong>步骤:</strong> ${agentData.currentStep || 0}/${agentData.maxSteps || 5}<br>
                    <strong>状态:</strong> ${getAgentStatusText(agentData.status)}
                `;
            }
        }

        function getAgentStatusText(status) {
            switch (status) {
                case 'in_progress': return '执行中';
                case 'COMPLETED': return '已完成';
                case 'STUCK': return '卡住';
                case 'ERROR': return '错误';
                default: return status || '未知';
            }
        }

        // ==================== 思考事件处理 ====================

        function handleThinkCompleteEvent(eventData) {
            try {
                const thinkPayload = eventData.payload ? JSON.parse(eventData.payload) : {};
                const agentId = eventData.entityId;

                // 创建思考容器
                const thinkContainer = createThinkContainer(thinkPayload, agentId);

                // 添加到对应的智能体容器中
                const agentContainer = document.querySelector(`[data-agent-id="${agentId}"]`);
                if (agentContainer) {
                    const agentContent = agentContainer.querySelector('.agent-content');
                    agentContent.appendChild(thinkContainer);
                }
            } catch (error) {
                console.error('❌ 处理思考完成事件失败:', error);
            }
        }

        function handleThinkChunkEvent(eventData) {
            try {
                const agentId = eventData.entityId;
                const textChunk = eventData.payload || '';

                // 查找或创建流式思考容器
                let streamingThink = document.querySelector(`[data-agent-id="${agentId}"] .think-content.streaming`);

                if (!streamingThink) {
                    // 创建新的流式思考容器
                    const thinkContainer = createStreamingThinkContainer(agentId);
                    const agentContainer = document.querySelector(`[data-agent-id="${agentId}"]`);
                    if (agentContainer) {
                        const agentContent = agentContainer.querySelector('.agent-content');
                        agentContent.appendChild(thinkContainer);
                        streamingThink = thinkContainer.querySelector('.think-content');
                    }
                }

                if (streamingThink && textChunk) {
                    // 添加文本块到思考内容（保持打字机光标）
                    const cursor = streamingThink.querySelector('.typewriter-cursor');
                    if (cursor) {
                        // 在光标前插入文本
                        const textNode = document.createTextNode(textChunk);
                        streamingThink.insertBefore(textNode, cursor);
                    } else {
                        // 如果没有光标，直接添加文本
                        streamingThink.textContent += textChunk;
                    }

                    // 滚动到底部
                    streamingThink.scrollIntoView({ behavior: 'smooth', block: 'end' });
                }
            } catch (error) {
                console.error('❌ 处理思考块事件失败:', error);
            }
        }

        function createThinkContainer(thinkData, agentId) {
            const container = document.createElement('div');
            container.className = 'think-container';
            container.dataset.agentId = agentId;

            const header = document.createElement('div');
            header.className = 'think-header';
            header.innerHTML = `
                <i class="bi bi-brain"></i>
                <div class="think-title">思考过程</div>
                <div class="think-status">已完成</div>
            `;

            const content = document.createElement('div');
            content.className = 'think-content';
            content.textContent = thinkData.thinkOutput || '思考完成';

            container.appendChild(header);
            container.appendChild(content);

            return container;
        }

        function createStreamingThinkContainer(agentId) {
            const container = document.createElement('div');
            container.className = 'think-container';
            container.dataset.agentId = agentId;

            const header = document.createElement('div');
            header.className = 'think-header';
            header.innerHTML = `
                <i class="bi bi-brain"></i>
                <div class="think-title">思考过程</div>
                <div class="think-status">
                    <div class="tool-loading">
                        <div class="loading-spinner"></div>
                        <span>思考中...</span>
                    </div>
                </div>
            `;

            const content = document.createElement('div');
            content.className = 'think-content streaming';
            content.innerHTML = '<span class="typewriter-cursor"></span>';

            container.appendChild(header);
            container.appendChild(content);

            return container;
        }

        // ==================== 工具调用事件处理 ====================

        function handleToolStartEvent(eventData) {
            try {
                const toolPayload = eventData.payload;
                const agentId = eventData.entityId;

                // 创建整合的工具容器
                const toolContainer = createIntegratedToolContainer(toolPayload, agentId);

                // 添加到对应的智能体容器中
                const agentContainer = document.querySelector(`[data-agent-id="${agentId}"]`);
                if (agentContainer) {
                    const agentContent = agentContainer.querySelector('.agent-content');
                    agentContent.appendChild(toolContainer);
                }
            } catch (error) {
                console.error('❌ 处理工具开始事件失败:', error);
            }
        }

        function handleToolEndEvent(eventData) {
            try {
                const toolPayload = eventData.payload;
                const agentId = eventData.entityId;

                // 查找对应的工具容器并更新结果
                const toolId = `tool-${agentId}-${toolPayload.toolName}`;
                const toolContainer = document.querySelector(`[data-tool-id="${toolId}"]`);

                if (toolContainer) {
                    updateToolResult(toolContainer, toolPayload);
                } else {
                    console.warn('未找到对应的工具容器:', toolId);
                    // 如果找不到容器，创建一个新的（兼容性处理）
                    const agentContainer = document.querySelector(`[data-agent-id="${agentId}"]`);
                    if (agentContainer) {
                        const newToolContainer = createIntegratedToolContainer(toolPayload, agentId, true);
                        const agentContent = agentContainer.querySelector('.agent-content');
                        agentContent.appendChild(newToolContainer);
                    }
                }
            } catch (error) {
                console.error('❌ 处理工具结束事件失败:', error);
            }
        }

        function createIntegratedToolContainer(toolPayload, agentId, hasResult = false) {
            const container = document.createElement('div');
            container.className = 'tool-container';

            // 设置唯一标识符
            const toolId = `tool-${agentId}-${toolPayload.toolName}`;
            container.setAttribute('data-tool-id', toolId);

            const header = document.createElement('div');
            header.className = 'tool-header';
            header.innerHTML = `
                <i class="bi bi-tools"></i>
                <span>工具调用: ${toolPayload.toolName || '未知工具'}</span>
                <span class="tool-status">${hasResult ? '已完成' : '执行中...'}</span>
            `;

            const content = document.createElement('div');
            content.className = 'tool-content';

            // 参数部分
            const paramsSection = document.createElement('div');
            paramsSection.className = 'tool-params';

            const paramsTitle = document.createElement('div');
            paramsTitle.className = 'tool-section-title';
            paramsTitle.innerHTML = `
                <span>📋 调用参数</span>
                <i class="bi bi-chevron-down collapse-icon"></i>
            `;

            const paramsContent = document.createElement('div');
            paramsContent.className = 'tool-section-content';

            if (toolPayload.parameters) {
                try {
                    const params = typeof toolPayload.parameters === 'string'
                        ? JSON.parse(toolPayload.parameters)
                        : toolPayload.parameters;
                    paramsContent.textContent = JSON.stringify(params, null, 2);
                } catch (e) {
                    paramsContent.textContent = String(toolPayload.parameters);
                }
            } else {
                paramsContent.textContent = '无参数';
            }

            // 添加折叠功能
            paramsTitle.addEventListener('click', function() {
                toggleSection(paramsTitle, paramsContent);
            });

            paramsSection.appendChild(paramsTitle);
            paramsSection.appendChild(paramsContent);

            // 结果部分
            const resultSection = document.createElement('div');
            resultSection.className = 'tool-result';

            const resultTitle = document.createElement('div');
            resultTitle.className = 'tool-section-title';
            resultTitle.innerHTML = `
                <span>📄 执行结果</span>
                <i class="bi bi-chevron-down collapse-icon"></i>
            `;

            const resultContent = document.createElement('div');
            resultContent.className = 'tool-section-content tool-result-content';

            if (hasResult) {
                resultContent.textContent = '';
            } else {
                // 显示加载状态
                resultContent.innerHTML = `
                    <div class="tool-loading">
                        <div class="loading-spinner"></div>
                        <span>正在执行工具...</span>
                    </div>
                `;
            }

            // 添加折叠功能
            resultTitle.addEventListener('click', function() {
                toggleSection(resultTitle, resultContent);
            });

            resultSection.appendChild(resultTitle);
            resultSection.appendChild(resultContent);

            content.appendChild(paramsSection);
            content.appendChild(resultSection);
            container.appendChild(header);
            container.appendChild(content);

            // 如果有结果，立即更新
            if (hasResult && toolPayload.result) {
                updateToolResult(container, toolPayload);
            }

            return container;
        }

        function updateToolResult(toolContainer, toolPayload) {
            // 更新状态
            const statusSpan = toolContainer.querySelector('.tool-status');
            if (statusSpan) {
                statusSpan.textContent = '已完成';
            }

            // 更新图标
            const icon = toolContainer.querySelector('.tool-header i');
            if (icon) {
                icon.className = 'bi bi-check-circle';
            }

            // 更新结果内容
            const resultContent = toolContainer.querySelector('.tool-result-content');
            if (resultContent && toolPayload.result) {
                // 根据工具类型处理结果
                if (toolPayload.toolName === 'sql_query') {
                    handleSqlQueryResult(toolPayload.result, resultContent, toolContainer);
                } else if (toolPayload.toolName === 'terminate') {
                    handleTerminateResult(toolPayload.result, resultContent);
                } else {
                    // 默认处理
                    handleDefaultToolResult(toolPayload.result, resultContent);
                }
            }
        }

        function handleSqlQueryResult(resultData, resultContent, toolContainer) {
            try {
                // 解析SQL查询结果
                let sqlData;
                if (typeof resultData === 'string') {
                    sqlData = JSON.parse(resultData);
                } else {
                    sqlData = resultData;
                }

                console.log('📊 SQL查询结果数据:', sqlData);

                // 清空原有内容
                resultContent.innerHTML = '';

                if (sqlData.success) {
                    // 显示基本信息
                    const infoDiv = document.createElement('div');
                    infoDiv.style.marginBottom = '16px';
                    infoDiv.style.padding = '12px';
                    infoDiv.style.background = 'rgba(16, 185, 129, 0.1)';
                    infoDiv.style.borderRadius = '6px';
                    infoDiv.style.fontSize = '0.875rem';
                    infoDiv.innerHTML = `
                        <div><strong>SQL:</strong> ${sqlData.sql || '未知'}</div>
                        <div><strong>执行时间:</strong> ${sqlData.duration || 0}ms</div>
                        <div><strong>返回行数:</strong> ${sqlData.rowCount || 0}</div>
                    `;
                    resultContent.appendChild(infoDiv);

                    // 渲染图表（如果有图表配置）
                    if (sqlData.chart_config) {
                        renderChart(sqlData.chart_config, resultContent);
                    }

                    // 渲染数据表格（如果有数据）
                    if (sqlData.data && sqlData.data.length > 0) {
                        renderDataTable(sqlData.data, sqlData.columns, resultContent);
                    }

                    resultContent.style.color = 'var(--success-color)';
                } else {
                    const error = sqlData.error || sqlData.message || '查询执行失败';
                    resultContent.textContent = error;
                    resultContent.style.color = 'var(--danger-color)';
                }
            } catch (error) {
                console.error('❌ 处理SQL查询结果失败:', error);
                resultContent.innerHTML = `<div class="chart-error">
                    <i class="bi bi-exclamation-triangle"></i>
                    解析查询结果失败: ${error.message}
                </div>`;
            }
        }

        function handleTerminateResult(resultData, resultContent) {
            // 终止工具结果的特殊处理
            const output = resultData.output || resultData.message || resultData || '任务完成';
            resultContent.textContent = output;
            resultContent.style.color = 'var(--success-color)';
        }

        function handleDefaultToolResult(resultData, resultContent) {
            try {
                const result = typeof resultData === 'string'
                    ? JSON.parse(resultData)
                    : resultData;
                const resultText = JSON.stringify(result, null, 2);
                const truncatedResult = resultText.length > 500
                    ? resultText.substring(0, 500) + '\n...(结果已截断)'
                    : resultText;
                resultContent.textContent = truncatedResult;
            } catch (e) {
                const resultText = String(resultData);
                const truncatedResult = resultText.length > 500
                    ? resultText.substring(0, 500) + '...(结果已截断)'
                    : resultText;
                resultContent.textContent = truncatedResult;
            }
        }

        // 折叠/展开切换函数
        function toggleSection(titleElement, contentElement) {
            const isCollapsed = contentElement.classList.contains('collapsed');

            if (isCollapsed) {
                // 展开
                contentElement.classList.remove('collapsed');
                titleElement.classList.remove('collapsed');
            } else {
                // 折叠
                contentElement.classList.add('collapsed');
                titleElement.classList.add('collapsed');
            }
        }

        // ==================== 总结事件处理 ====================

        function handleSummaryChunkEvent(eventData) {
            try {
                const textChunk = eventData.payload || '';
                const planId = eventData.planId;

                console.log('📝 处理总结事件:', { planId, textLength: textChunk.length, isComplete: textChunk.length > 50 });

                // 根据planId查找对应的对话轮次
                let conversationRound = conversationRounds[planId];

                if (!conversationRound) {
                    console.warn('未找到对应的对话轮次:', planId);
                    console.log('📋 当前注册的对话轮次:', Object.keys(conversationRounds));

                    // 尝试使用当前对话轮次作为备选
                    if (currentConversationRound) {
                        console.log('📋 使用当前对话轮次作为备选');
                        conversationRound = currentConversationRound;
                        // 同时注册这个planId
                        conversationRounds[planId] = currentConversationRound;
                    } else {
                        console.error('❌ 无法找到任何对话轮次来显示总结');
                        return;
                    }
                }

                // 在当前对话轮次中查找或创建总结容器
                let summaryContainer = conversationRound.querySelector('.summary-container');

                if (!summaryContainer) {
                    console.log('📝 创建新的总结容器');
                    // 创建总结容器
                    summaryContainer = createSummaryContainer();

                    // 添加到当前轮次的计划容器中
                    const planContainer = conversationRound.querySelector('.plan-container');
                    if (planContainer) {
                        const planContent = planContainer.querySelector('.plan-content');
                        planContent.appendChild(summaryContainer);
                        console.log('📝 总结容器已添加到计划容器');
                    } else {
                        console.warn('❌ 未找到计划容器');
                    }
                }

                // 添加文本块到总结内容
                const summaryContent = summaryContainer.querySelector('.summary-content');
                if (summaryContent) {
                    // 如果是完整的总结文本（通常来自PLAN_COMPLETED），直接设置内容
                    if (textChunk.length > 50 && !summaryContent.textContent.trim()) {
                        console.log('📝 设置完整总结内容');
                        summaryContent.textContent = textChunk;
                    } else {
                        // 如果是文本块（流式），追加内容
                        console.log('📝 追加总结文本块');
                        summaryContent.textContent += textChunk;
                    }

                    // 添加动画效果
                    summaryContainer.style.animation = 'fadeInUp 0.3s ease-out';
                } else {
                    console.warn('❌ 未找到总结内容容器');
                }

                console.log('📝 总结文本处理完成:', { planId, currentLength: summaryContent?.textContent?.length || 0 });
            } catch (error) {
                console.error('❌ 处理总结文本块失败:', error);
            }
        }

        function createSummaryContainer() {
            const container = document.createElement('div');
            container.className = 'summary-container';
            container.innerHTML = `
                <div class="summary-header">
                    <i class="bi bi-file-text"></i>
                    <span class="summary-title">执行总结</span>
                    <span class="summary-status">生成中...</span>
                </div>
                <div class="summary-content"></div>
            `;

            return container;
        }

        // ==================== 计划完成事件处理 ====================

        function handlePlanCompletedEvent(eventData) {
            try {
                const planId = eventData.planId;
                const summaryText = eventData.payload; // 总结信息在payload中

                console.log('📋 计划完成事件:', { planId, summaryText });

                // 更新计划数据为已完成状态
                if (planData[planId]) {
                    planData[planId].completed = true;
                    planData[planId].endTime = new Date().toISOString();
                    console.log('✅ 更新计划数据为已完成:', planData[planId]);
                }

                // 如果有总结信息，先处理总结
                if (summaryText && summaryText.trim()) {
                    console.log('📝 处理计划完成时的总结信息:', summaryText);

                    // 创建总结事件并处理
                    const summaryEvent = {
                        type: 'SUMMARY_CHUNK',
                        planId: planId,
                        payload: summaryText
                    };

                    handleSummaryChunkEvent(summaryEvent);

                    // 延迟标记总结完成状态
                    setTimeout(() => {
                        const conversationRound = conversationRounds[planId];
                        if (conversationRound) {
                            const summaryStatus = conversationRound.querySelector('.summary-status');
                            if (summaryStatus) {
                                summaryStatus.textContent = '已完成';
                                summaryStatus.style.background = 'rgba(76, 175, 80, 0.1)';
                                summaryStatus.style.color = '#4caf50';
                            }
                        }
                    }, 200);
                }

                // 根据planId查找对应的对话轮次
                const conversationRound = conversationRounds[planId];

                if (conversationRound) {
                    // 停止当前轮次中的流式思考
                    const streamingThinks = conversationRound.querySelectorAll('.think-content.streaming');
                    streamingThinks.forEach(think => {
                        think.classList.remove('streaming');
                        // 移除打字机光标
                        const cursor = think.querySelector('.typewriter-cursor');
                        if (cursor) {
                            cursor.remove();
                        }
                        const header = think.closest('.think-container').querySelector('.think-status');
                        if (header) {
                            header.textContent = '已完成';
                        }
                    });

                    // 更新当前轮次中的智能体状态
                    const agentContainers = conversationRound.querySelectorAll('.agent-container');
                    agentContainers.forEach(agentContainer => {
                        const agentStatus = agentContainer.querySelector('.agent-status');
                        if (agentStatus) {
                            agentStatus.textContent = '已完成';
                        }

                        // 更新智能体信息中的状态
                        const agentInfo = agentContainer.querySelector('.agent-info');
                        if (agentInfo) {
                            const statusMatch = agentInfo.innerHTML.match(/<strong>状态:<\/strong>\s*[^<]*/);
                            if (statusMatch) {
                                agentInfo.innerHTML = agentInfo.innerHTML.replace(
                                    /<strong>状态:<\/strong>\s*[^<]*/,
                                    '<strong>状态:</strong> 已完成'
                                );
                            }
                        }
                    });

                    // 更新当前轮次中的总结状态
                    const summaryStatus = conversationRound.querySelector('.summary-status');
                    if (summaryStatus) {
                        summaryStatus.textContent = '已完成';
                    }
                } else {
                    // 兼容旧逻辑：如果找不到对应轮次，则更新所有
                    const streamingThinks = document.querySelectorAll('.think-content.streaming');
                    streamingThinks.forEach(think => {
                        think.classList.remove('streaming');
                        // 移除打字机光标
                        const cursor = think.querySelector('.typewriter-cursor');
                        if (cursor) {
                            cursor.remove();
                        }
                        const header = think.closest('.think-container').querySelector('.think-status');
                        if (header) {
                            header.textContent = '已完成';
                        }
                    });

                    // 更新所有智能体状态
                    const agentContainers = document.querySelectorAll('.agent-container');
                    agentContainers.forEach(agentContainer => {
                        const agentStatus = agentContainer.querySelector('.agent-status');
                        if (agentStatus) {
                            agentStatus.textContent = '已完成';
                        }

                        // 更新智能体信息中的状态
                        const agentInfo = agentContainer.querySelector('.agent-info');
                        if (agentInfo) {
                            const statusMatch = agentInfo.innerHTML.match(/<strong>状态:<\/strong>\s*[^<]*/);
                            if (statusMatch) {
                                agentInfo.innerHTML = agentInfo.innerHTML.replace(
                                    /<strong>状态:<\/strong>\s*[^<]*/,
                                    '<strong>状态:</strong> 已完成'
                                );
                            }
                        }
                    });

                    const summaryStatus = document.querySelector('.summary-status');
                    if (summaryStatus) {
                        summaryStatus.textContent = '已完成';
                    }
                }

                // 添加完成指示器到对应的计划容器
                const targetPlanContainer = conversationRound ?
                    conversationRound.querySelector('.plan-container') :
                    currentPlanContainer;

                if (targetPlanContainer) {
                    const completedIndicator = document.createElement('div');
                    completedIndicator.className = 'completed-indicator';
                    completedIndicator.innerHTML = `
                        <i class="bi bi-check-circle-fill"></i>
                        <span>计划执行完成！</span>
                        <small style="margin-left: auto; font-size: 0.8rem;">
                            ${new Date().toLocaleTimeString()}
                        </small>
                    `;

                    const planContent = targetPlanContainer.querySelector('.plan-content');
                    planContent.appendChild(completedIndicator);

                    // 更新计划状态
                    const planStatus = targetPlanContainer.querySelector('.plan-status');
                    if (planStatus) {
                        planStatus.textContent = '已完成';
                    }

                    // 如果有计划数据，使用updatePlanContainer来更新完整信息
                    if (planData[planId]) {
                        updatePlanContainer(targetPlanContainer, planData[planId]);
                    }
                }

                // 重新启用发送按钮
                if (sendBtn) {
                    sendBtn.disabled = false;
                }

                // 关闭事件源
                if (eventSource) {
                    eventSource.close();
                    eventSource = null;
                }

                // 重置当前轮次状态，准备下一轮对话
                currentConversationRound = null;
                currentPlanContainer = null;

                // 消息发送成功后刷新历史对话列表
                console.log('🔄 计划执行完成，刷新历史对话列表');
                setTimeout(() => {
                    loadChatHistory();
                }, 1000); // 延迟1秒确保后端已保存会话

                console.log('✅ 计划执行完成');
            } catch (error) {
                console.error('❌ 处理计划完成事件失败:', error);
            }
        }

        // ==================== 跨页面通信 ====================

        function setupCrossPageCommunication() {
            console.log('🔗 设置跨页面通信监听器...');

            // 监听来自父页面的消息
            window.addEventListener('message', function(event) {
                console.log('📨 收到跨页面消息:', event.data);

                if (event.data && typeof event.data === 'object') {
                    switch (event.data.type) {
                        case 'LOAD_HISTORY':
                            if (event.data.chatId) {
                                console.log('📜 收到加载历史请求:', event.data.chatId);
                                loadHistoryMessages(event.data.chatId);
                            }
                            break;
                        case 'SET_CHAT_ID':
                            if (event.data.chatId) {
                                console.log('🆔 设置chatId:', event.data.chatId);
                                currentChatId = event.data.chatId;
                                if (chatTitle) {
                                    chatTitle.textContent = event.data.title || '历史对话';
                                }
                            }
                            break;
                        default:
                            console.log('❓ 未知消息类型:', event.data.type);
                    }
                }
            });

            // 向父页面发送准备就绪消息
            if (window.parent !== window) {
                console.log('📤 向父页面发送准备就绪消息');
                window.parent.postMessage({
                    type: 'CHAT_PAGE_READY'
                }, '*');
            }

            console.log('✅ 跨页面通信监听器设置完成');
        }

        function checkUrlParameters() {
            console.log('🔍 检查URL参数...');

            const urlParams = new URLSearchParams(window.location.search);
            const chatId = urlParams.get('chatId');
            const title = urlParams.get('title');

            console.log('URL参数:', { chatId, title });

            if (chatId) {
                console.log('📜 从URL参数加载历史数据:', chatId);
                currentChatId = chatId;

                if (title) {
                    currentChatTitle = decodeURIComponent(title);
                    if (chatTitle) {
                        chatTitle.textContent = currentChatTitle;
                    }
                }

                // 延迟加载历史数据，确保页面完全初始化
                setTimeout(() => {
                    loadHistoryMessages(chatId);
                }, 500);
            }

            console.log('✅ URL参数检查完成');
        }

        // ==================== 调试和测试函数 ====================

        function testStreamingSummary() {
            console.log('🧪 测试流式总结功能');

            const testPlanId = 'test-streaming-plan-' + Date.now();

            // 模拟创建一个对话轮次
            const testRound = document.createElement('div');
            testRound.className = 'conversation-round';
            testRound.setAttribute('data-plan-id', testPlanId);

            // 创建计划容器
            const planContainer = document.createElement('div');
            planContainer.className = 'plan-container';
            planContainer.innerHTML = `
                <div class="plan-content">
                    <div class="plan-header">
                        <h4>测试计划</h4>
                    </div>
                    <div class="plan-info">测试流式总结功能</div>
                </div>
            `;

            testRound.appendChild(planContainer);

            // 添加到页面
            if (chatContainer) {
                chatContainer.appendChild(testRound);
            }

            // 注册到对话轮次
            conversationRounds[testPlanId] = testRound;

            console.log('📝 创建测试对话轮次:', testPlanId);

            // 模拟PLAN_COMPLETED事件
            setTimeout(() => {
                const testSummary = "根据查询执行结果，用户请求的\"极速鲜产品的邮件总数\"已成功获取。系统通过生成并执行SQL语句，从数据库中汇总得出符合条件的邮件总数为 **420**。该过程执行成功，无错误发生，且返回数据准确。因此，最终确认极速鲜产品的邮件总数为 **420** 条。";

                const planCompletedEvent = {
                    type: 'PLAN_COMPLETED',
                    planId: testPlanId,
                    payload: testSummary
                };

                console.log('🧪 模拟PLAN_COMPLETED事件:', planCompletedEvent);
                handlePlanCompletedEvent(planCompletedEvent);

            }, 1000);

            console.log('✅ 流式总结测试启动');
        }

        function testHistoryRendering() {
            console.log('🧪 开始测试历史渲染');

            // 创建测试数据
            const testData = {
                chatId: "test-chat-123",
                executions: [
                    {
                        planId: "test-plan-123",
                        title: "测试计划",
                        userRequest: "这是一个测试请求",
                        startTime: new Date().toISOString(),
                        endTime: new Date().toISOString(),
                        completed: true,
                        steps: ["步骤1", "步骤2", "步骤3"],
                        currentStepIndex: 3,
                        summary: "这是一个测试总结，用于验证总结功能是否正常工作。总结应该显示在计划容器的底部，包含完整的执行结果和分析。",
                        agentExecutionSequence: [
                            {
                                id: 1,
                                agentName: "测试智能体",
                                agentDescription: "这是一个测试智能体",
                                status: "COMPLETED",
                                thinkActSteps: [
                                    {
                                        id: 1,
                                        thinkOutput: "我正在思考如何解决这个问题...",
                                        toolName: "test-tool",
                                        toolParameters: '{"param1": "value1"}',
                                        actionResult: "工具执行成功"
                                    }
                                ]
                            }
                        ]
                    }
                ]
            };

            console.log('🧪 测试数据:', testData);

            // 直接调用渲染函数
            try {
                renderHistoryMessages(testData);
                console.log('✅ 测试渲染完成');
            } catch (error) {
                console.error('❌ 测试渲染失败:', error);
            }
        }

        // ==================== 辅助函数 ====================

        function formatTimestamp(timestamp) {
            if (!timestamp) return '未知时间';
            return new Date(timestamp).toLocaleString();
        }

        function truncateText(text, maxLength = 500) {
            if (!text) return '';
            return text.length > maxLength ? text.substring(0, maxLength) + '...' : text;
        }

        function escapeHtml(text) {
            const div = document.createElement('div');
            div.textContent = text;
            return div.innerHTML;
        }

        // ==================== 历史窗口功能 ====================

        /**
         * 加载聊天历史
         */
        function loadChatHistory() {
            console.log('📚 加载聊天历史');

            if (!historyContent) {
                console.error('❌ 找不到历史内容容器');
                return;
            }

            // 显示加载状态
            historyContent.innerHTML = `
                <div class="loading-placeholder">
                    <i class="bi bi-arrow-clockwise spin"></i>
                    <span>加载历史对话...</span>
                </div>
            `;

            // 构建API URL - 使用正确的端点
            const url = `/api/streaming-events/chat/sessions?page=0&size=50&userId=${encodeURIComponent(currentUserId)}`;

            fetch(url)
                .then(async response => {
                    const data = await handleRResponse(response);
                    console.log('📊 历史数据:', data);
                    renderChatHistory(data);
                })
                .catch(error => {
                    console.error('❌ 加载历史失败:', error);
                    historyContent.innerHTML = `
                        <div class="empty-history">
                            <i class="bi bi-exclamation-triangle"></i>
                            加载历史失败<br>
                            <small>${error.message}</small>
                        </div>
                    `;
                });
        }

        /**
         * 搜索聊天历史
         */
        function searchChatHistory(keyword) {
            console.log('🔍 搜索聊天历史:', keyword);

            if (!historyContent) {
                console.error('❌ 找不到历史内容容器');
                return;
            }

            // 显示搜索状态
            historyContent.innerHTML = `
                <div class="loading-placeholder">
                    <i class="bi bi-search"></i>
                    <span>搜索中...</span>
                </div>
            `;

            // 构建搜索API URL - 使用正确的端点
            const url = `/api/streaming-events/chat/search?userId=${encodeURIComponent(currentUserId)}&keyword=${encodeURIComponent(keyword)}&page=0&size=50`;

            fetch(url)
                .then(async response => {
                    const data = await handleRResponse(response);
                    console.log('📊 搜索结果:', data);
                    renderSearchResults(data, keyword);
                })
                .catch(error => {
                    console.error('❌ 搜索失败:', error);
                    historyContent.innerHTML = `
                        <div class="empty-history">
                            <i class="bi bi-exclamation-triangle"></i>
                            搜索失败<br>
                            <small>${error.message}</small>
                        </div>
                    `;
                });
        }

        /**
         * 渲染聊天历史
         */
        function renderChatHistory(data) {
            console.log('🎨 渲染聊天历史');

            if (!data.sessions || Object.keys(data.sessions).length === 0) {
                historyContent.innerHTML = `
                    <div class="empty-history">
                        <i class="bi bi-chat-dots"></i>
                        暂无历史对话<br>
                        <small>开始新对话来创建历史记录</small>
                    </div>
                `;
                return;
            }

            let html = '';

            // 按时间分组渲染
            for (const [groupName, sessions] of Object.entries(data.sessions)) {
                if (sessions && sessions.length > 0) {
                    html += `<div class="history-group">`;
                    html += `<div class="history-group-title">${groupName}</div>`;

                    sessions.forEach(session => {
                        html += renderHistoryItem(session);
                    });

                    html += `</div>`;
                }
            }

            historyContent.innerHTML = html;

            // 绑定点击事件
            bindHistoryItemEvents();
        }

        /**
         * 渲染搜索结果
         */
        function renderSearchResults(data, keyword) {
            console.log('🎨 渲染搜索结果');

            // 检查搜索结果 - 适配搜索API的数据结构
            let allSessions = [];
            if (Array.isArray(data.sessions)) {
                // 搜索API直接返回sessions数组
                allSessions = data.sessions;
            } else if (data.sessions && typeof data.sessions === 'object') {
                // 如果是分组结构，合并所有分组的会话
                for (const [groupName, sessions] of Object.entries(data.sessions)) {
                    if (Array.isArray(sessions)) {
                        allSessions = allSessions.concat(sessions);
                    }
                }
            }

            if (allSessions.length === 0) {
                historyContent.innerHTML = `
                    <div class="empty-history">
                        <i class="bi bi-search"></i>
                        未找到相关对话<br>
                        <small>关键词: "${keyword}"</small>
                    </div>
                `;
                return;
            }

            let html = `<div class="history-group">`;
            html += `<div class="history-group-title">搜索结果 (${data.totalResults || allSessions.length})</div>`;

            allSessions.forEach(session => {
                html += renderHistoryItem(session);
            });

            html += `</div>`;

            historyContent.innerHTML = html;

            // 绑定点击事件
            bindHistoryItemEvents();
        }

        /**
         * 渲染单个历史项目
         */
        function renderHistoryItem(session) {
            const isActive = session.chatId === currentChatId;
            // 优先使用chatName，其次是chatTitle，最后是chatId
            const title = session.chatName || session.chatTitle || session.chatId || '未命名对话';
            const time = formatTime(session.createdAt || session.updatedAt);
            // 获取planTemplateId，用于切换对话时恢复正确的模板
            const planTemplateId = session.planTemplateId || currentPlanTemplateId || 'planTemplate-1749011464049';

            return `
                <div class="history-item ${isActive ? 'active' : ''}"
                     data-chat-id="${session.chatId}"
                     data-chat-name="${escapeHtml(title)}"
                     data-plan-template-id="${escapeHtml(planTemplateId)}">
                    <div class="history-item-title">${escapeHtml(title)}</div>
                    <div class="history-item-meta">
                        <span class="history-item-time">${time}</span>
                        <div class="history-item-actions">
                            <button class="history-item-action" onclick="deleteChatSession('${session.chatId}')" title="删除">
                                <i class="bi bi-trash"></i>
                            </button>
                        </div>
                    </div>
                </div>
            `;
        }

        /**
         * 绑定历史项目事件
         */
        function bindHistoryItemEvents() {
            const historyItems = document.querySelectorAll('.history-item');
            historyItems.forEach(item => {
                item.addEventListener('click', function(event) {
                    // 如果点击的是删除按钮，不处理切换事件
                    if (event.target.closest('.history-item-action')) {
                        return;
                    }

                    const chatId = this.dataset.chatId;
                    console.log('🔄 切换到历史对话:', chatId);
                    switchToHistoryChat(chatId);
                });
            });
        }

        /**
         * 切换到历史对话
         */
        function switchToHistoryChat(chatId) {
            try {
                // 更新当前会话ID
                currentChatId = chatId;

                // 更新活跃状态
                document.querySelectorAll('.history-item').forEach(item => {
                    item.classList.remove('active');
                });

                const activeItem = document.querySelector(`[data-chat-id="${chatId}"]`);
                if (activeItem) {
                    activeItem.classList.add('active');
                    // 从data-chat-name属性获取聊天名称，确保使用正确的chatName
                    currentChatTitle = activeItem.getAttribute('data-chat-name') || activeItem.querySelector('.history-item-title').textContent;

                    // 从历史数据中获取并设置正确的planTemplateId
                    const historyPlanTemplateId = activeItem.getAttribute('data-plan-template-id');
                    if (historyPlanTemplateId) {
                        const oldPlanTemplateId = currentPlanTemplateId;
                        currentPlanTemplateId = historyPlanTemplateId;
                        console.log('📋 切换planTemplateId:', {
                            from: oldPlanTemplateId,
                            to: currentPlanTemplateId,
                            chatId: chatId
                        });
                    }
                }

                // 更新页面标题
                if (chatTitle) {
                    chatTitle.textContent = currentChatTitle;
                }

                // 清空当前聊天内容
                clearChat();

                // 加载历史对话内容
                loadHistoryMessages(chatId);

                console.log('✅ 已切换到历史对话:', { chatId, title: currentChatTitle });
            } catch (error) {
                console.error('❌ 切换历史对话失败:', error);
                alert('切换对话失败: ' + error.message);
            }
        }

        /**
         * 处理R类统一响应格式
         * @param {Response} response - 响应对象
         * @returns {Promise<any>} - 处理后的数据
         */
        async function handleRResponse(response) {
            if (!response.ok) {
                throw new Error(`API请求失败: ${response.status}`);
            }

            const result = await response.json();

            // 检查R类响应格式
            if (result.code && result.code !== 1) {
                throw new Error(result.message || 'API请求失败');
            }

            // 返回data字段，如果没有data字段则返回整个结果（兼容旧格式）
            return result.data !== undefined ? result.data : result;
        }

        /**
         * 加载历史消息
         */
        async function loadHistoryMessages(chatId) {
            console.log('📜 加载历史消息:', chatId);

            // 显示加载状态
            addMessage('正在加载历史对话...', 'system');

            const url = `/api/streaming-events/chat/${encodeURIComponent(chatId)}/history`;

            try {
                const response = await fetch(url);
                const data = await handleRResponse(response);
                console.log('📊 历史消息数据:', data);
                renderHistoryMessages(data);
            } catch (error) {
                console.error('❌ 加载历史消息失败:', error);
                addMessage('加载历史对话失败: ' + error.message, 'system');
            }
        }

        /**
         * 渲染历史消息
         */
        function renderHistoryMessages(data) {
            console.log('🎨 渲染历史消息', data);

            // 清空聊天容器（移除加载消息）
            clearChat();

            // 详细检查数据结构
            console.log('📊 数据结构分析:');
            console.log('- data.executions:', data.executions);
            console.log('- data.history:', data.history);
            console.log('- data keys:', Object.keys(data));

            // 处理新的数据格式：支持executions数组和旧的history数组
            let executionRecords = [];
            if (data.executions && data.executions.length > 0) {
                // 新格式：使用executions数组
                executionRecords = data.executions;
                console.log('✅ 使用executions格式的历史数据:', executionRecords.length, '条记录');
                console.log('📋 第一条记录结构:', executionRecords[0]);
            } else if (data.history && data.history.length > 0) {
                // 旧格式：使用history数组
                executionRecords = data.history;
                console.log('✅ 使用history格式的历史数据:', executionRecords.length, '条记录');
                console.log('📋 第一条记录结构:', executionRecords[0]);
            } else {
                console.warn('⚠️ 未找到有效的历史数据');
                console.log('- data.executions存在:', !!data.executions);
                console.log('- data.executions长度:', data.executions ? data.executions.length : 'N/A');
                console.log('- data.history存在:', !!data.history);
                console.log('- data.history长度:', data.history ? data.history.length : 'N/A');
                addMessage('该对话暂无历史记录', 'system');
                return;
            }

            // 渲染每个历史记录
            console.log('🔄 开始渲染', executionRecords.length, '条历史记录');
            executionRecords.forEach((record, index) => {
                console.log(`📝 渲染第${index + 1}条记录:`, record);
                try {
                    renderHistoryRecord(record);
                    console.log(`✅ 第${index + 1}条记录渲染成功`);
                } catch (error) {
                    console.error(`❌ 第${index + 1}条记录渲染失败:`, error);
                }
            });

            console.log('✅ 历史消息渲染完成');
        }

        /**
         * 渲染单个历史记录
         * 使用流式渲染逻辑来处理历史数据
         */
        function renderHistoryRecord(record) {
            console.log('🎨 渲染历史记录:', record);

            // 创建对话轮次容器
            const roundContainer = document.createElement('div');
            roundContainer.className = 'conversation-round';

            // 创建用户消息气泡
            const userMessageDiv = document.createElement('div');
            userMessageDiv.className = 'message user-message';
            userMessageDiv.innerHTML = `
                <div class="message-avatar">
                    <i class="bi bi-person-circle"></i>
                </div>
                <div class="message-content">
                    ${escapeHtml(record.userRequest || '用户消息')}
                    <div class="message-time">${formatTimestamp(record.startTime)}</div>
                </div>
            `;

            // 创建AI回复容器
            const aiResponseContainer = document.createElement('div');
            aiResponseContainer.className = 'ai-response-container';
            aiResponseContainer.innerHTML = `
                <div class="message ai-message">
                    <div class="message-avatar">
                        <i class="bi bi-robot"></i>
                    </div>
                    <div class="message-content">
                        <div class="ai-response-content">
                            <!-- 这里将放置计划、智能体、思考等内容 -->
                        </div>
                        <div class="message-time">${formatTimestamp(record.endTime || record.startTime)}</div>
                    </div>
                </div>
            `;

            // 组装轮次容器
            roundContainer.appendChild(userMessageDiv);
            roundContainer.appendChild(aiResponseContainer);

            // 添加到聊天容器
            chatContainer.appendChild(roundContainer);

            // 设置当前对话轮次（模拟流式环境）
            const previousRound = currentConversationRound;
            const previousPlanContainer = currentPlanContainer;
            currentConversationRound = roundContainer;
            currentPlanContainer = null;

            // 使用流式渲染逻辑来渲染历史数据
            renderHistoryAsStream(record);

            // 恢复之前的状态
            currentConversationRound = previousRound;
            currentPlanContainer = previousPlanContainer;
        }

        /**
         * 将历史数据模拟为流式事件来渲染
         */
        function renderHistoryAsStream(record) {
            console.log('🔄 开始模拟流式渲染:', record);
            try {
                // 1. 渲染计划
                if (record.planId || record.title) {
                    console.log('📋 渲染计划部分');
                    const planPayload = {
                        planId: record.planId,
                        title: record.title,
                        userRequest: record.userRequest,
                        startTime: record.startTime,
                        endTime: record.endTime,
                        completed: record.completed || true, // 历史数据默认已完成
                        steps: record.steps,
                        currentStepIndex: record.currentStepIndex,
                        summary: record.summary
                    };

                    console.log('📋 计划数据:', planPayload);

                    // 模拟计划事件
                    const mockPlanEvent = {
                        type: 'PLAN_FULL',
                        payload: JSON.stringify(planPayload)
                    };
                    console.log('📋 模拟计划事件:', mockPlanEvent);
                    handlePlanEvent(mockPlanEvent);
                    console.log('✅ 计划渲染完成');
                } else {
                    console.log('⚠️ 跳过计划渲染 - 无planId或title');
                }

                // 2. 渲染智能体序列
                if (record.agentExecutionSequence && record.agentExecutionSequence.length > 0) {
                    console.log('🤖 渲染智能体序列:', record.agentExecutionSequence.length, '个智能体');
                    record.agentExecutionSequence.forEach((agent, agentIndex) => {
                        console.log(`🤖 渲染智能体 ${agentIndex + 1}:`, agent);

                        // 模拟智能体事件
                        const mockAgentEvent = {
                            type: 'AGENT_FULL',
                            payload: JSON.stringify(agent)
                        };
                        console.log(`🤖 模拟智能体事件:`, mockAgentEvent);
                        handleAgentEvent(mockAgentEvent);
                        console.log(`✅ 智能体 ${agentIndex + 1} 渲染完成`);

                        // 3. 渲染思考行动步骤
                        if (agent.thinkActSteps && agent.thinkActSteps.length > 0) {
                            console.log(`💭 渲染思考步骤:`, agent.thinkActSteps.length, '个步骤');
                            agent.thinkActSteps.forEach((thinkAct, stepIndex) => {
                                console.log(`💭 处理思考步骤 ${stepIndex + 1}:`, thinkAct);

                                // 渲染思考过程
                                if (thinkAct.thinkOutput) {
                                    console.log(`💭 渲染思考内容:`, thinkAct.thinkOutput);
                                    const mockThinkEvent = {
                                        type: 'THINK_COMPLETE',
                                        entityId: agent.id,
                                        payload: JSON.stringify({
                                            thinkOutput: thinkAct.thinkOutput
                                        })
                                    };
                                    handleThinkCompleteEvent(mockThinkEvent);
                                    console.log(`✅ 思考步骤 ${stepIndex + 1} 渲染完成`);
                                }

                                // 渲染工具调用
                                if (thinkAct.toolName) {
                                    console.log(`🔧 渲染工具调用:`, thinkAct.toolName);
                                    // 工具开始事件
                                    const mockToolStartEvent = {
                                        type: 'TOOL_START',
                                        entityId: agent.id,
                                        payload: {
                                            toolName: thinkAct.toolName,
                                            parameters: thinkAct.toolParameters
                                        }
                                    };
                                    handleToolStartEvent(mockToolStartEvent);

                                    // 工具结束事件
                                    const mockToolEndEvent = {
                                        type: 'TOOL_END',
                                        entityId: agent.id,
                                        payload: {
                                            toolName: thinkAct.toolName,
                                            result: thinkAct.actionResult
                                        }
                                    };
                                    handleToolEndEvent(mockToolEndEvent);
                                    console.log(`✅ 工具调用 ${thinkAct.toolName} 渲染完成`);
                                }
                            });
                        } else {
                            console.log(`⚠️ 智能体 ${agentIndex + 1} 无思考步骤`);
                        }
                    });
                } else {
                    console.log('⚠️ 跳过智能体渲染 - 无agentExecutionSequence');
                }

                // 4. 渲染总结
                if (record.summary) {
                    console.log('📝 渲染总结:', record.summary);

                    // 模拟总结事件来渲染总结
                    setTimeout(() => {
                        const summaryEvent = {
                            type: 'SUMMARY_CHUNK',
                            planId: record.planId,
                            payload: record.summary
                        };

                        console.log('📝 模拟总结事件:', summaryEvent);
                        handleSummaryChunkEvent(summaryEvent);

                        // 标记总结完成
                        setTimeout(() => {
                            const conversationRound = document.querySelector(`[data-plan-id="${record.planId}"]`);
                            if (conversationRound) {
                                const summaryStatus = conversationRound.querySelector('.summary-status');
                                if (summaryStatus) {
                                    summaryStatus.textContent = '已完成';
                                    summaryStatus.style.background = 'rgba(76, 175, 80, 0.1)';
                                    summaryStatus.style.color = '#4caf50';
                                }
                            }
                        }, 100);

                    }, 1000); // 延迟1秒渲染总结，确保计划和智能体已渲染完成
                }

            } catch (error) {
                console.error('❌ 渲染历史数据为流式失败:', error);
            }
        }



        /**
         * 创建静态计划容器（用于历史记录显示）
         */
        function createStaticPlanContainer(planData) {
            const container = document.createElement('div');
            container.className = 'plan-container';

            const header = document.createElement('div');
            header.className = 'plan-header';
            header.innerHTML = `
                <i class="bi bi-diagram-3"></i>
                <div class="plan-title">${planData.title || '计划执行'}</div>
                <div class="plan-status">已完成</div>
            `;

            const content = document.createElement('div');
            content.className = 'plan-content';

            const info = document.createElement('div');
            info.className = 'plan-info';
            info.innerHTML = `
                <strong>用户请求:</strong> ${planData.userRequest || '处理中...'}<br>
                <strong>开始时间:</strong> ${formatTimestamp(planData.startTime)}<br>
                <strong>结束时间:</strong> ${formatTimestamp(planData.endTime)}
            `;

            content.appendChild(info);

            // 添加步骤信息
            if (planData.steps && planData.steps.length > 0) {
                const stepsContainer = document.createElement('div');
                stepsContainer.className = 'plan-steps';

                planData.steps.forEach((step, index) => {
                    const stepDiv = document.createElement('div');
                    stepDiv.className = 'plan-step completed';
                    stepDiv.innerHTML = `
                        <span>✅</span>
                        <span>${step}</span>
                    `;
                    stepsContainer.appendChild(stepDiv);
                });

                content.appendChild(stepsContainer);
            }

            // 添加完成指示器
            const completedIndicator = document.createElement('div');
            completedIndicator.className = 'completed-indicator';
            completedIndicator.innerHTML = `
                <i class="bi bi-check-circle-fill"></i>
                <span>计划执行完成！</span>
                <small style="margin-left: auto; font-size: 0.8rem;">
                    ${formatTimestamp(planData.endTime)}
                </small>
            `;
            content.appendChild(completedIndicator);

            container.appendChild(header);
            container.appendChild(content);

            return container;
        }

        /**
         * 删除聊天会话
         */
        function deleteChatSession(chatId) {
            if (!confirm('确定要删除这个对话吗？此操作不可撤销。')) {
                return;
            }

            console.log('🗑️ 删除聊天会话:', chatId);

            // 使用正确的删除API端点
            const url = `/api/streaming-events/chat/session/${encodeURIComponent(chatId)}`;

            fetch(url, {
                method: 'DELETE'
            })
                .then(response => {
                    if (!response.ok) {
                        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                    }
                    return response.json();
                })
                .then(data => {
                    if (data.deleted) {
                        console.log('✅ 会话删除成功');

                        // 如果删除的是当前会话，清空聊天内容
                        if (chatId === currentChatId) {
                            currentChatId = null;
                            currentChatTitle = '层次化对话';
                            if (chatTitle) {
                                chatTitle.textContent = currentChatTitle;
                            }
                            clearChat();
                        }

                        // 重新加载历史
                        loadChatHistory();
                    } else {
                        alert('删除失败: ' + (data.message || '未知错误'));
                    }
                })
                .catch(error => {
                    console.error('❌ 删除会话失败:', error);
                    alert('删除失败: ' + error.message);
                });
        }

        /**
         * 格式化时间
         */
        function formatTime(timestamp) {
            if (!timestamp) return '未知时间';

            const date = new Date(timestamp);
            const now = new Date();
            const diffMs = now - date;
            const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));

            if (diffDays === 0) {
                return date.toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit' });
            } else if (diffDays === 1) {
                return '昨天 ' + date.toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit' });
            } else if (diffDays < 7) {
                return diffDays + '天前';
            } else {
                return date.toLocaleDateString('zh-CN', { month: 'short', day: 'numeric' });
            }
        }

        // ==================== 图表渲染 ====================

        function renderChart(chartConfig, container) {
            try {
                console.log('📈 开始渲染图表:', chartConfig);

                // 创建图表容器
                const chartContainer = document.createElement('div');
                chartContainer.className = 'chart-container';

                // 创建图表头部
                const chartHeader = document.createElement('div');
                chartHeader.className = 'chart-header';

                const chartTitle = chartConfig.title?.text || '数据图表';
                const chartType = getChartTypeFromConfig(chartConfig);

                chartHeader.innerHTML = `
                    <i class="bi bi-bar-chart"></i>
                    <span class="chart-title">${chartTitle}</span>
                    <span class="chart-type-badge">${chartType}</span>
                `;

                // 创建图表内容区域
                const chartContent = document.createElement('div');
                chartContent.className = 'chart-content';

                // 创建图表画布
                const chartCanvas = document.createElement('div');
                chartCanvas.className = 'chart-canvas';
                chartCanvas.id = `chart-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;

                chartContent.appendChild(chartCanvas);
                chartContainer.appendChild(chartHeader);
                chartContainer.appendChild(chartContent);
                container.appendChild(chartContainer);

                // 初始化ECharts
                const chart = echarts.init(chartCanvas);

                // 设置图表配置
                const option = {
                    ...chartConfig,
                    animation: true,
                    animationDuration: 1000,
                    animationEasing: 'cubicOut'
                };

                // 渲染图表
                chart.setOption(option);

                // 响应式处理
                const resizeObserver = new ResizeObserver(() => {
                    chart.resize();
                });
                resizeObserver.observe(chartCanvas);

                // 存储图表实例以便后续操作
                chartCanvas._chartInstance = chart;

                console.log('✅ 图表渲染完成');
            } catch (error) {
                console.error('❌ 图表渲染失败:', error);
                const errorDiv = document.createElement('div');
                errorDiv.className = 'chart-error';
                errorDiv.innerHTML = `
                    <i class="bi bi-exclamation-triangle"></i>
                    图表渲染失败: ${error.message}
                `;
                container.appendChild(errorDiv);
            }
        }

        function getChartTypeFromConfig(chartConfig) {
            if (chartConfig.series && chartConfig.series.length > 0) {
                const firstSeries = chartConfig.series[0];
                switch (firstSeries.type) {
                    case 'line': return '折线图';
                    case 'bar': return '柱状图';
                    case 'pie': return '饼图';
                    case 'scatter': return '散点图';
                    case 'radar': return '雷达图';
                    default: return '图表';
                }
            }
            return '图表';
        }

        // ==================== 数据表格渲染 ====================

        function renderDataTable(data, columns, container) {
            try {
                console.log('📋 开始渲染数据表格:', { data, columns });

                // 创建表格容器
                const tableContainer = document.createElement('div');
                tableContainer.className = 'data-table-container';

                // 创建表格头部
                const tableHeader = document.createElement('div');
                tableHeader.className = 'data-table-header';
                tableHeader.innerHTML = `
                    <i class="bi bi-table"></i>
                    <span>查询结果数据 (${data.length} 行)</span>
                `;

                // 创建表格内容
                const tableContent = document.createElement('div');
                tableContent.className = 'data-table-content';

                const table = document.createElement('table');
                table.className = 'data-table';

                // 创建表头
                const thead = document.createElement('thead');
                const headerRow = document.createElement('tr');

                // 使用columns信息创建表头，如果没有columns则使用数据的键
                const columnNames = columns && columns.length > 0
                    ? columns.map(col => col.name)
                    : Object.keys(data[0] || {});

                columnNames.forEach(columnName => {
                    const th = document.createElement('th');
                    th.textContent = columnName;
                    headerRow.appendChild(th);
                });

                thead.appendChild(headerRow);
                table.appendChild(thead);

                // 创建表体
                const tbody = document.createElement('tbody');
                data.forEach(row => {
                    const tr = document.createElement('tr');
                    columnNames.forEach(columnName => {
                        const td = document.createElement('td');
                        const value = row[columnName];
                        td.textContent = value !== null && value !== undefined ? value : '';
                        tr.appendChild(td);
                    });
                    tbody.appendChild(tr);
                });

                table.appendChild(tbody);
                tableContent.appendChild(table);
                tableContainer.appendChild(tableHeader);
                tableContainer.appendChild(tableContent);
                container.appendChild(tableContainer);

                console.log('✅ 数据表格渲染完成');
            } catch (error) {
                console.error('❌ 数据表格渲染失败:', error);
                const errorDiv = document.createElement('div');
                errorDiv.className = 'chart-error';
                errorDiv.innerHTML = `
                    <i class="bi bi-exclamation-triangle"></i>
                    数据表格渲染失败: ${error.message}
                `;
                container.appendChild(errorDiv);
            }
        }

        console.log('✅ 层次化聊天页面脚本加载完成');
    </script>
</body>
</html>
