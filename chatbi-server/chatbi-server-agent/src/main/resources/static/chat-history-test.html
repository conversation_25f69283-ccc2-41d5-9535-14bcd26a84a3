<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>对话历史查询测试</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            text-align: center;
        }
        .content {
            padding: 20px;
        }
        .section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #e0e0e0;
            border-radius: 8px;
            background: #fafafa;
        }
        .section h3 {
            margin-top: 0;
            color: #333;
            border-bottom: 2px solid #667eea;
            padding-bottom: 10px;
        }
        .form-group {
            margin-bottom: 15px;
        }
        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #555;
        }
        .form-group input, .form-group select {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 14px;
        }
        .btn {
            background: #667eea;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
            margin-right: 10px;
            margin-bottom: 10px;
        }
        .btn:hover {
            background: #5a6fd8;
        }
        .btn-secondary {
            background: #6c757d;
        }
        .btn-secondary:hover {
            background: #5a6268;
        }
        .result {
            margin-top: 20px;
            padding: 15px;
            border-radius: 5px;
            background: white;
            border: 1px solid #ddd;
            max-height: 400px;
            overflow-y: auto;
        }
        .result pre {
            white-space: pre-wrap;
            word-wrap: break-word;
            margin: 0;
            font-size: 12px;
        }
        .loading {
            color: #667eea;
            font-style: italic;
        }
        .error {
            color: #dc3545;
            background: #f8d7da;
            border-color: #f5c6cb;
        }
        .success {
            color: #155724;
            background: #d4edda;
            border-color: #c3e6cb;
        }
        .stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-top: 15px;
        }
        .stat-card {
            background: white;
            padding: 15px;
            border-radius: 8px;
            border: 1px solid #e0e0e0;
            text-align: center;
        }
        .stat-value {
            font-size: 24px;
            font-weight: bold;
            color: #667eea;
        }
        .stat-label {
            font-size: 12px;
            color: #666;
            margin-top: 5px;
        }
        .history-item {
            background: white;
            margin-bottom: 10px;
            padding: 15px;
            border-radius: 8px;
            border: 1px solid #e0e0e0;
        }
        .history-title {
            font-weight: bold;
            color: #333;
            margin-bottom: 5px;
        }
        .history-meta {
            font-size: 12px;
            color: #666;
            margin-bottom: 10px;
        }
        .history-summary {
            color: #555;
            line-height: 1.4;
        }
        .pagination {
            margin-top: 15px;
            text-align: center;
        }
        .pagination button {
            margin: 0 5px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔍 对话历史查询测试</h1>
            <p>测试基于ExecutionRecordPersistenceService的对话历史查询功能</p>
        </div>
        
        <div class="content">
            <!-- 对话历史查询 -->
            <div class="section">
                <h3>📋 对话历史查询</h3>
                <div class="form-group">
                    <label for="chatId">对话ID:</label>
                    <input type="text" id="chatId" placeholder="输入对话ID">
                </div>
                <div class="form-group">
                    <label for="historyPage">页码:</label>
                    <input type="number" id="historyPage" value="0" min="0">
                </div>
                <div class="form-group">
                    <label for="historySize">每页大小:</label>
                    <input type="number" id="historySize" value="10" min="1" max="50">
                </div>
                <button class="btn" onclick="getChatHistory()">查询对话历史</button>
                <button class="btn btn-secondary" onclick="refreshChatHistory()">刷新历史</button>
                <div id="chatHistoryResult" class="result" style="display: none;"></div>
            </div>

            <!-- 用户历史概览 -->
            <div class="section">
                <h3>👤 用户历史概览</h3>
                <div class="form-group">
                    <label for="userId">用户ID:</label>
                    <input type="text" id="userId" placeholder="输入用户ID" value="default-user">
                </div>
                <button class="btn" onclick="getUserOverview()">获取用户概览</button>
                <div id="userOverviewResult" class="result" style="display: none;"></div>
            </div>

            <!-- 搜索功能 -->
            <div class="section">
                <h3>🔍 搜索对话历史</h3>
                <div class="form-group">
                    <label for="searchUserId">用户ID:</label>
                    <input type="text" id="searchUserId" placeholder="输入用户ID" value="default-user">
                </div>
                <div class="form-group">
                    <label for="searchKeyword">搜索关键词:</label>
                    <input type="text" id="searchKeyword" placeholder="输入搜索关键词">
                </div>
                <button class="btn" onclick="searchHistory()">搜索历史</button>
                <div id="searchResult" class="result" style="display: none;"></div>
            </div>

            <!-- 最近记录 -->
            <div class="section">
                <h3>⏰ 最近执行记录</h3>
                <button class="btn" onclick="getRecentHistory()">获取最近记录</button>
                <button class="btn btn-secondary" onclick="getExecutionStats()">获取统计信息</button>
                <div id="recentResult" class="result" style="display: none;"></div>
            </div>
        </div>
    </div>

    <script>
        const API_BASE = '/api';

        // 显示结果
        function showResult(elementId, data, isError = false) {
            const element = document.getElementById(elementId);
            element.style.display = 'block';
            element.className = `result ${isError ? 'error' : 'success'}`;
            
            if (typeof data === 'object') {
                element.innerHTML = `<pre>${JSON.stringify(data, null, 2)}</pre>`;
            } else {
                element.innerHTML = `<pre>${data}</pre>`;
            }
        }

        // 显示加载状态
        function showLoading(elementId) {
            const element = document.getElementById(elementId);
            element.style.display = 'block';
            element.className = 'result loading';
            element.innerHTML = '<p>加载中...</p>';
        }

        // 查询对话历史
        async function getChatHistory() {
            const chatId = document.getElementById('chatId').value.trim();
            if (!chatId) {
                alert('请输入对话ID');
                return;
            }

            const page = document.getElementById('historyPage').value;
            const size = document.getElementById('historySize').value;

            showLoading('chatHistoryResult');

            try {
                const response = await fetch(`${API_BASE}/streaming-events/chat/${chatId}/history?page=${page}&size=${size}`);
                const data = await response.json();
                
                if (response.ok) {
                    showResult('chatHistoryResult', data);
                } else {
                    showResult('chatHistoryResult', data, true);
                }
            } catch (error) {
                showResult('chatHistoryResult', `请求失败: ${error.message}`, true);
            }
        }

        // 刷新对话历史
        async function refreshChatHistory() {
            const chatId = document.getElementById('chatId').value.trim();
            if (!chatId) {
                alert('请输入对话ID');
                return;
            }

            showLoading('chatHistoryResult');

            try {
                const response = await fetch(`${API_BASE}/streaming-events/chat/${chatId}/refresh`, {
                    method: 'POST'
                });
                const data = await response.json();
                
                if (response.ok) {
                    showResult('chatHistoryResult', data);
                } else {
                    showResult('chatHistoryResult', data, true);
                }
            } catch (error) {
                showResult('chatHistoryResult', `刷新失败: ${error.message}`, true);
            }
        }

        // 获取用户概览
        async function getUserOverview() {
            const userId = document.getElementById('userId').value.trim();
            if (!userId) {
                alert('请输入用户ID');
                return;
            }

            showLoading('userOverviewResult');

            try {
                const response = await fetch(`${API_BASE}/streaming-events/user/${userId}/overview`);
                const data = await response.json();
                
                if (response.ok) {
                    showResult('userOverviewResult', data);
                } else {
                    showResult('userOverviewResult', data, true);
                }
            } catch (error) {
                showResult('userOverviewResult', `请求失败: ${error.message}`, true);
            }
        }

        // 搜索历史
        async function searchHistory() {
            const userId = document.getElementById('searchUserId').value.trim();
            const keyword = document.getElementById('searchKeyword').value.trim();
            
            if (!userId || !keyword) {
                alert('请输入用户ID和搜索关键词');
                return;
            }

            showLoading('searchResult');

            try {
                const response = await fetch(`${API_BASE}/streaming-events/user/${userId}/search?keyword=${encodeURIComponent(keyword)}`);
                const data = await response.json();
                
                if (response.ok) {
                    showResult('searchResult', data);
                } else {
                    showResult('searchResult', data, true);
                }
            } catch (error) {
                showResult('searchResult', `搜索失败: ${error.message}`, true);
            }
        }

        // 获取最近记录
        async function getRecentHistory() {
            showLoading('recentResult');

            try {
                const response = await fetch(`${API_BASE}/chat-history/recent`);
                const data = await response.json();
                
                if (response.ok) {
                    showResult('recentResult', data);
                } else {
                    showResult('recentResult', data, true);
                }
            } catch (error) {
                showResult('recentResult', `请求失败: ${error.message}`, true);
            }
        }

        // 获取统计信息
        async function getExecutionStats() {
            showLoading('recentResult');

            try {
                const response = await fetch(`${API_BASE}/chat-history/stats?days=7`);
                const data = await response.json();
                
                if (response.ok) {
                    showResult('recentResult', data);
                } else {
                    showResult('recentResult', data, true);
                }
            } catch (error) {
                showResult('recentResult', `请求失败: ${error.message}`, true);
            }
        }

        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', function() {
            console.log('对话历史查询测试页面已加载');
        });
    </script>
</body>
</html>
