<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>MCP服务器管理</title>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css" rel="stylesheet">
    <style>
        :root {
            --primary-color: #6366f1;
            --primary-hover: #4f46e5;
            --primary-light: #e0e7ff;
            --secondary-color: #64748b;
            --success-color: #10b981;
            --success-light: #d1fae5;
            --danger-color: #ef4444;
            --danger-light: #fee2e2;
            --warning-color: #f59e0b;
            --warning-light: #fef3c7;
            --info-color: #06b6d4;
            --info-light: #cffafe;
            --gray-50: #f9fafb;
            --gray-100: #f3f4f6;
            --gray-200: #e5e7eb;
            --gray-300: #d1d5db;
            --gray-400: #9ca3af;
            --gray-500: #6b7280;
            --gray-600: #4b5563;
            --gray-700: #374151;
            --gray-800: #1f2937;
            --gray-900: #111827;
            --border-radius: 12px;
            --border-radius-lg: 16px;
            --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
            --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
            --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
            --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
        }

        * {
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            margin: 0;
            padding: 0;
            min-height: 100vh;
            color: var(--gray-900);
            line-height: 1.6;
        }

        .app-container {
            min-height: 100vh;
            display: flex;
            flex-direction: column;
            background: var(--gray-50);
        }

        /* 顶部导航栏 */
        .app-header {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-bottom: 1px solid var(--gray-200);
            padding: 0 32px;
            height: 72px;
            display: flex;
            align-items: center;
            justify-content: space-between;
            box-shadow: var(--shadow-sm);
            position: sticky;
            top: 0;
            z-index: 100;
        }

        .app-title {
            display: flex;
            align-items: center;
            gap: 12px;
            font-size: 1.75rem;
            font-weight: 700;
            color: var(--gray-900);
            letter-spacing: -0.025em;
        }

        .app-title i {
            font-size: 2rem;
            color: var(--primary-color);
            background: var(--primary-light);
            padding: 8px;
            border-radius: var(--border-radius);
        }

        .header-actions {
            display: flex;
            gap: 12px;
        }

        .action-btn {
            background: white;
            color: var(--gray-700);
            border: 1px solid var(--gray-300);
            padding: 10px 16px;
            border-radius: var(--border-radius);
            font-size: 0.875rem;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
            display: flex;
            align-items: center;
            gap: 8px;
            text-decoration: none;
            box-shadow: var(--shadow-sm);
        }

        .action-btn:hover {
            background: var(--gray-50);
            border-color: var(--gray-400);
            transform: translateY(-1px);
            box-shadow: var(--shadow-md);
            color: var(--gray-700);
        }

        .action-btn.primary {
            background: var(--primary-color);
            color: white;
            border-color: var(--primary-color);
        }

        .action-btn.primary:hover {
            background: var(--primary-hover);
            border-color: var(--primary-hover);
            color: white;
        }

        /* 主内容区域 */
        .main-content {
            flex: 1;
            background: var(--gray-50);
            min-height: calc(100vh - 72px);
            padding: 32px;
        }

        /* 顶部操作栏 */
        .content-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 32px;
        }

        .content-title {
            font-size: 2rem;
            font-weight: 800;
            color: var(--gray-900);
            letter-spacing: -0.025em;
        }

        .content-actions {
            display: flex;
            gap: 12px;
            align-items: center;
        }

        .search-bar {
            position: relative;
            width: 320px;
        }

        .search-bar input {
            width: 100%;
            padding: 12px 16px 12px 44px;
            border: 1px solid var(--gray-300);
            border-radius: var(--border-radius);
            font-size: 0.875rem;
            background: white;
            box-shadow: var(--shadow-sm);
            transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
        }

        .search-bar input:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px var(--primary-light);
        }

        .search-bar i {
            position: absolute;
            left: 16px;
            top: 50%;
            transform: translateY(-50%);
            color: var(--gray-400);
        }

        /* MCP卡片网格布局 */
        .mcp-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(380px, 1fr));
            gap: 24px;
            margin-bottom: 32px;
        }

        .mcp-card {
            background: white;
            border: 1px solid var(--gray-200);
            border-radius: var(--border-radius-lg);
            padding: 24px;
            box-shadow: var(--shadow-sm);
            transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
            cursor: pointer;
            position: relative;
            overflow: hidden;
        }

        .mcp-card:hover {
            border-color: var(--primary-color);
            box-shadow: var(--shadow-xl);
            transform: translateY(-4px);
        }

        .mcp-card.selected {
            border-color: var(--primary-color);
            background: var(--primary-light);
            box-shadow: var(--shadow-xl);
        }

        .mcp-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: var(--primary-color);
            opacity: 0;
            transition: opacity 0.2s ease;
        }

        .mcp-card.selected::before,
        .mcp-card:hover::before {
            opacity: 1;
        }

        .mcp-card-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 16px;
        }

        .mcp-card-title {
            font-size: 1.25rem;
            font-weight: 700;
            color: var(--gray-900);
            letter-spacing: -0.025em;
            margin: 0;
        }

        .mcp-card-id {
            font-size: 0.75rem;
            color: var(--gray-500);
            background: var(--gray-100);
            padding: 4px 8px;
            border-radius: 6px;
            font-weight: 500;
        }

        .mcp-card-type {
            display: inline-flex;
            align-items: center;
            padding: 6px 12px;
            border-radius: 20px;
            font-size: 0.75rem;
            font-weight: 600;
            margin-bottom: 16px;
            text-transform: uppercase;
            letter-spacing: 0.05em;
        }

        .mcp-card-type.SSE {
            background: var(--info-light);
            color: var(--info-color);
        }

        .mcp-card-type.STREAMING {
            background: var(--warning-light);
            color: var(--warning-color);
        }

        .mcp-card-type.STUDIO {
            background: var(--success-light);
            color: var(--success-color);
        }

        .mcp-card-config {
            font-size: 0.8rem;
            color: var(--gray-600);
            background: var(--gray-50);
            border: 1px solid var(--gray-200);
            padding: 12px;
            border-radius: var(--border-radius);
            font-family: 'JetBrains Mono', 'Fira Code', 'Courier New', monospace;
            white-space: pre-wrap;
            max-height: 100px;
            overflow-y: auto;
            line-height: 1.4;
            margin-bottom: 16px;
        }

        .mcp-card-actions {
            display: flex;
            gap: 8px;
            justify-content: flex-end;
        }

        .card-action-btn {
            padding: 6px 12px;
            border: 1px solid var(--gray-300);
            border-radius: 6px;
            background: white;
            color: var(--gray-600);
            font-size: 0.75rem;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .card-action-btn:hover {
            border-color: var(--primary-color);
            color: var(--primary-color);
        }

        .card-action-btn.danger:hover {
            border-color: var(--danger-color);
            color: var(--danger-color);
        }

        /* 新建MCP卡片 */
        .new-mcp-card {
            background: linear-gradient(135deg, var(--primary-color), var(--primary-hover));
            border: 2px dashed var(--primary-color);
            border-radius: var(--border-radius-lg);
            padding: 40px 24px;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
            color: white;
            text-align: center;
            min-height: 200px;
        }

        .new-mcp-card:hover {
            transform: translateY(-4px);
            box-shadow: var(--shadow-xl);
        }

        .new-mcp-card i {
            font-size: 3rem;
            margin-bottom: 16px;
            opacity: 0.9;
        }

        .new-mcp-card h3 {
            font-size: 1.25rem;
            font-weight: 700;
            margin: 0 0 8px 0;
        }

        .new-mcp-card p {
            font-size: 0.875rem;
            opacity: 0.9;
            margin: 0;
        }

        /* 空状态 */
        .empty-grid {
            text-align: center;
            padding: 80px 40px;
            color: var(--gray-500);
        }

        .empty-grid i {
            font-size: 5rem;
            margin-bottom: 24px;
            display: block;
            opacity: 0.6;
            color: var(--gray-400);
        }

        .empty-grid h3 {
            font-size: 1.5rem;
            margin-bottom: 12px;
            color: var(--gray-900);
            font-weight: 700;
        }

        .empty-grid p {
            font-size: 1rem;
            line-height: 1.6;
            color: var(--gray-600);
            margin-bottom: 24px;
        }

        .mcp-item-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 12px;
        }

        .mcp-name {
            font-weight: 700;
            color: var(--gray-900);
            font-size: 1.125rem;
            letter-spacing: -0.025em;
        }

        .mcp-id {
            font-size: 0.75rem;
            color: var(--gray-500);
            background: var(--gray-100);
            padding: 4px 8px;
            border-radius: 6px;
            font-weight: 500;
        }

        .mcp-type {
            display: inline-flex;
            align-items: center;
            padding: 6px 12px;
            border-radius: 20px;
            font-size: 0.75rem;
            font-weight: 600;
            margin-bottom: 12px;
            text-transform: uppercase;
            letter-spacing: 0.05em;
        }

        .mcp-type.SSE {
            background: var(--info-light);
            color: var(--info-color);
        }

        .mcp-type.STREAMING {
            background: var(--warning-light);
            color: var(--warning-color);
        }

        .mcp-type.STUDIO {
            background: var(--success-light);
            color: var(--success-color);
        }

        .mcp-config {
            font-size: 0.8rem;
            color: var(--gray-600);
            background: var(--gray-50);
            border: 1px solid var(--gray-200);
            padding: 12px;
            border-radius: var(--border-radius);
            font-family: 'JetBrains Mono', 'Fira Code', 'Courier New', monospace;
            white-space: pre-wrap;
            max-height: 120px;
            overflow-y: auto;
            line-height: 1.4;
        }

        /* 侧边抽屉 */
        .drawer-overlay {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0, 0, 0, 0.5);
            z-index: 1000;
            opacity: 0;
            visibility: hidden;
            transition: all 0.3s ease;
        }

        .drawer-overlay.show {
            opacity: 1;
            visibility: visible;
        }

        .config-drawer {
            position: fixed;
            top: 0;
            right: 0;
            width: 600px;
            height: 100vh;
            background: white;
            box-shadow: var(--shadow-xl);
            z-index: 1001;
            transform: translateX(100%);
            transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            display: flex;
            flex-direction: column;
        }

        .config-drawer.show {
            transform: translateX(0);
        }

        .drawer-header {
            padding: 24px 32px;
            border-bottom: 1px solid var(--gray-200);
            background: var(--gray-50);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .drawer-title {
            font-size: 1.5rem;
            font-weight: 700;
            color: var(--gray-900);
            margin: 0;
        }

        .drawer-close {
            background: none;
            border: none;
            font-size: 1.5rem;
            color: var(--gray-500);
            cursor: pointer;
            padding: 8px;
            border-radius: var(--border-radius);
            transition: all 0.2s ease;
        }

        .drawer-close:hover {
            background: var(--gray-200);
            color: var(--gray-700);
        }

        .drawer-content {
            flex: 1;
            padding: 32px;
            overflow-y: auto;
        }

        .drawer-footer {
            padding: 24px 32px;
            border-top: 1px solid var(--gray-200);
            background: var(--gray-50);
            display: flex;
            gap: 12px;
            justify-content: flex-end;
        }

        .detail-form {
            display: flex;
            flex-direction: column;
            height: 100%;
        }

        .detail-header {
            background: var(--gray-50);
            padding: 24px 32px;
            border-bottom: 1px solid var(--gray-200);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .detail-title {
            margin: 0;
            font-size: 1.75rem;
            font-weight: 700;
            color: var(--gray-900);
            letter-spacing: -0.025em;
        }

        .detail-actions {
            display: flex;
            gap: 12px;
        }

        .detail-content {
            flex: 1;
            padding: 32px;
            overflow-y: auto;
            background: white;
        }

        .form-section {
            margin-bottom: 40px;
        }

        .section-title {
            display: flex;
            align-items: center;
            gap: 12px;
            font-size: 1.25rem;
            font-weight: 700;
            color: var(--gray-900);
            margin-bottom: 24px;
            padding-bottom: 12px;
            border-bottom: 2px solid var(--gray-200);
            letter-spacing: -0.025em;
        }

        .section-title i {
            color: var(--primary-color);
            font-size: 1.375rem;
            background: var(--primary-light);
            padding: 8px;
            border-radius: var(--border-radius);
        }

        .form-group {
            margin-bottom: 24px;
        }

        .form-label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: var(--gray-900);
            font-size: 0.875rem;
        }

        .form-input, .form-select, .form-textarea {
            width: 100%;
            padding: 12px 16px;
            border: 1px solid var(--gray-300);
            border-radius: var(--border-radius);
            font-size: 0.875rem;
            transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
            background: white;
            box-shadow: var(--shadow-sm);
        }

        .form-input:focus, .form-select:focus, .form-textarea:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px var(--primary-light);
        }

        .form-textarea {
            resize: vertical;
            min-height: 120px;
            font-family: 'JetBrains Mono', 'Fira Code', 'Courier New', monospace;
            line-height: 1.5;
        }

        .form-textarea.large {
            min-height: 240px;
        }

        .text-muted {
            color: var(--gray-500) !important;
            font-size: 0.8rem;
            margin-top: 6px;
        }

        /* 空状态 */
        .empty-state {
            text-align: center;
            padding: 80px 40px;
            color: var(--gray-500);
        }

        .empty-state i {
            font-size: 5rem;
            margin-bottom: 24px;
            display: block;
            opacity: 0.6;
            color: var(--gray-400);
        }

        .empty-state h3 {
            font-size: 1.5rem;
            margin-bottom: 12px;
            color: var(--gray-900);
            font-weight: 700;
            letter-spacing: -0.025em;
        }

        .empty-state p {
            font-size: 1rem;
            line-height: 1.6;
            color: var(--gray-600);
        }

        /* 按钮样式 */
        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: var(--border-radius);
            font-weight: 600;
            cursor: pointer;
            transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
            font-size: 0.875rem;
            display: inline-flex;
            align-items: center;
            gap: 8px;
            box-shadow: var(--shadow-sm);
        }

        .btn-primary {
            background: var(--primary-color);
            color: white;
            border: 1px solid var(--primary-color);
        }

        .btn-primary:hover {
            background: var(--primary-hover);
            border-color: var(--primary-hover);
            transform: translateY(-2px);
            box-shadow: var(--shadow-lg);
        }

        .btn-danger {
            background: var(--danger-color);
            color: white;
            border: 1px solid var(--danger-color);
        }

        .btn-danger:hover {
            background: #dc2626;
            border-color: #dc2626;
            transform: translateY(-2px);
            box-shadow: var(--shadow-lg);
        }

        .btn-secondary {
            background: white;
            color: var(--gray-700);
            border: 1px solid var(--gray-300);
        }

        .btn-secondary:hover {
            background: var(--gray-50);
            border-color: var(--gray-400);
            transform: translateY(-1px);
        }

        .btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none !important;
        }

        .btn-sm {
            padding: 8px 16px;
            font-size: 0.8rem;
        }

        /* 配置示例样式 */
        .config-examples {
            background: var(--gray-50);
            border: 1px solid var(--gray-200);
            border-radius: var(--border-radius);
            padding: 20px;
        }

        .example-item {
            margin-bottom: 20px;
        }

        .example-item:last-child {
            margin-bottom: 0;
        }

        .example-item strong {
            color: var(--gray-900);
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            font-size: 0.875rem;
        }

        .example-code {
            background: var(--gray-900);
            color: #e5e7eb;
            padding: 16px;
            border-radius: var(--border-radius);
            font-family: 'JetBrains Mono', 'Fira Code', 'Courier New', monospace;
            font-size: 0.8rem;
            margin: 0;
            overflow-x: auto;
            white-space: pre-wrap;
            line-height: 1.5;
            border: 1px solid var(--gray-700);
        }

        /* 消息提示样式 */
        .alert {
            border: none;
            border-radius: 8px;
            box-shadow: var(--shadow-lg);
            animation: slideInRight 0.3s ease;
        }

        @keyframes slideInRight {
            from {
                opacity: 0;
                transform: translateX(100%);
            }
            to {
                opacity: 1;
                transform: translateX(0);
            }
        }

        .alert-success {
            background: var(--success-color);
            color: white;
        }

        .alert-danger {
            background: var(--danger-color);
            color: white;
        }

        /* 表单验证样式 */
        .form-input.is-invalid, .form-select.is-invalid, .form-textarea.is-invalid {
            border-color: var(--danger-color);
            box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1);
        }

        .invalid-feedback {
            display: block;
            color: var(--danger-color);
            font-size: 0.875rem;
            margin-top: 5px;
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .app-header {
                padding: 0 15px;
            }

            .app-title {
                font-size: 1.25rem;
            }

            .header-actions {
                gap: 5px;
            }

            .action-btn {
                padding: 6px 10px;
                font-size: 0.8rem;
            }

            .mcp-sidebar {
                width: 100%;
                position: absolute;
                z-index: 100;
                transform: translateX(-100%);
            }

            .mcp-sidebar.show {
                transform: translateX(0);
            }

            .config-detail {
                width: 100%;
                margin: 10px;
            }

            .detail-content {
                padding: 20px;
            }

            .form-textarea.large {
                min-height: 150px;
            }
        }

        @media (max-width: 480px) {
            .detail-header {
                flex-direction: column;
                gap: 15px;
                align-items: stretch;
            }

            .detail-actions {
                justify-content: stretch;
            }

            .detail-actions .btn {
                flex: 1;
            }
        }
    </style>
</head>
<body>
    <div class="app-container">
        <!-- 顶部导航栏 -->
        <div class="app-header">
            <div class="app-title">
                <i class="bi bi-server"></i>
                <span>MCP服务器管理</span>
            </div>
            <div class="header-actions">
                <button class="action-btn" id="debugBtn" title="调试信息">
                    <i class="bi bi-bug"></i>
                    <span>调试</span>
                </button>
                <a href="/" class="action-btn">
                    <i class="bi bi-arrow-left"></i>
                    <span>返回</span>
                </a>
                <a href="/agent-config.html" class="action-btn primary">
                    <i class="bi bi-robot"></i>
                    <span>Agent管理</span>
                </a>
            </div>
        </div>

        <!-- 主内容区域 -->
        <div class="main-content">
            <!-- 顶部操作栏 -->
            <div class="content-header">
                <h1 class="content-title">MCP服务器</h1>
                <div class="content-actions">
                    <div class="search-bar">
                        <i class="bi bi-search"></i>
                        <input type="text" id="searchInput" placeholder="搜索MCP服务器...">
                    </div>
                    <button class="btn btn-primary" id="newMcpBtn">
                        <i class="bi bi-plus-lg"></i>
                        <span>新建服务器</span>
                    </button>
                </div>
            </div>

            <!-- MCP卡片网格 -->
            <div class="mcp-grid" id="mcpGrid">
                <div class="loading-placeholder">
                    <i class="bi bi-hourglass-split"></i>
                    <span>加载MCP服务器列表...</span>
                </div>
            </div>
        </div>

        <!-- 配置抽屉 -->
        <div class="drawer-overlay" id="drawerOverlay"></div>
        <div class="config-drawer" id="configDrawer">
            <div class="drawer-header">
                <h2 class="drawer-title" id="drawerTitle">MCP服务器配置</h2>
                <button class="drawer-close" id="drawerClose">
                    <i class="bi bi-x"></i>
                </button>
            </div>
            <div class="drawer-content" id="drawerContent">
                <!-- 配置表单将在这里动态生成 -->
            </div>
            <div class="drawer-footer" id="drawerFooter">
                <!-- 操作按钮将在这里动态生成 -->
            </div>
        </div>
    </div>

    <!-- 引入JavaScript -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // 全局变量
        let currentMcp = null;
        let allMcps = [];
        let isEditing = false;

        // DOM元素
        let mcpGrid, searchInput, newMcpBtn, configDrawer, drawerOverlay, drawerClose;

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            console.log('MCP配置页面开始初始化...');

            try {
                initializeElements();
                setupEventListeners();
                loadInitialData();
            } catch (error) {
                console.error('页面初始化失败:', error);
            }
        });

        function initializeElements() {
            // 主要元素
            mcpGrid = document.getElementById('mcpGrid');
            searchInput = document.getElementById('searchInput');
            newMcpBtn = document.getElementById('newMcpBtn');

            // 抽屉元素
            configDrawer = document.getElementById('configDrawer');
            drawerOverlay = document.getElementById('drawerOverlay');
            drawerClose = document.getElementById('drawerClose');

            // 验证关键元素是否存在
            const requiredElements = {
                mcpGrid, searchInput, newMcpBtn, configDrawer, drawerOverlay, drawerClose
            };

            for (const [name, element] of Object.entries(requiredElements)) {
                if (!element) {
                    console.error(`关键元素未找到: ${name}`);
                }
            }

            console.log('DOM元素初始化完成', {
                mcpGrid: !!mcpGrid,
                configDrawer: !!configDrawer,
                drawerOverlay: !!drawerOverlay
            });
        }

        function setupEventListeners() {
            // 搜索功能
            searchInput.addEventListener('input', function() {
                const keyword = this.value.trim().toLowerCase();
                filterMcps(keyword);
            });

            // 新建MCP按钮
            newMcpBtn.addEventListener('click', function() {
                openDrawer();
                createNewMcp();
            });

            // 抽屉关闭
            drawerClose.addEventListener('click', closeDrawer);
            drawerOverlay.addEventListener('click', closeDrawer);

            // ESC键关闭抽屉
            document.addEventListener('keydown', function(e) {
                if (e.key === 'Escape' && configDrawer.classList.contains('show')) {
                    closeDrawer();
                }
            });

            // 调试按钮
            document.getElementById('debugBtn').addEventListener('click', function() {
                showDebugInfo();
            });

            console.log('事件监听器设置完成');
        }

        async function loadInitialData() {
            try {
                // 显示加载状态
                showLoading();

                // 加载MCP列表
                const mcpsData = await loadMcps();
                allMcps = mcpsData || [];

                console.log('数据加载完成:', {
                    mcps: allMcps.length
                });

                // 渲染MCP网格
                renderMcpGrid();

            } catch (error) {
                console.error('加载数据失败:', error);
                showError('加载数据失败: ' + error.message);
                // 加载失败时使用模拟数据
                loadMockData();
            }
        }

        /**
         * 处理R类统一响应格式
         * @param {Response} response - 响应对象
         * @returns {Promise<any>} - 处理后的数据
         */
        async function handleRResponse(response) {
            if (!response.ok) {
                throw new Error(`API请求失败: ${response.status}`);
            }

            const result = await response.json();

            // 检查R类响应格式
            if (result.code && result.code !== 1) {
                throw new Error(result.message || 'API请求失败');
            }

            // 返回data字段，如果没有data字段则返回整个结果（兼容旧格式）
            return result.data !== undefined ? result.data : result;
        }

        async function loadMcps() {
            try {
                const response = await fetch('/api/mcp/list');
                return await handleRResponse(response);
            } catch (error) {
                console.error('加载MCP列表失败:', error);
                throw error;
            }
        }

        function loadMockData() {
            // 模拟数据用于测试
            allMcps = [
                {
                    id: 1,
                    mcpServerName: 'tavily-search',
                    connectionType: 'SSE',
                    connectionConfig: '{"command": "npx", "args": ["-y", "@tavily/mcp-server"], "env": {"TAVILY_API_KEY": "your-api-key"}}',
                    toolNames: ['tavily_search', 'tavily_extract']
                },
                {
                    id: 2,
                    mcpServerName: 'filesystem',
                    connectionType: 'STUDIO',
                    connectionConfig: '{"command": "npx", "args": ["-y", "@modelcontextprotocol/server-filesystem", "/tmp"]}',
                    toolNames: ['read_file', 'write_file', 'list_directory']
                }
            ];

            console.log('使用模拟数据:', { mcps: allMcps.length });
            renderMcpGrid();
        }

        function showLoading() {
            mcpGrid.innerHTML = `
                <div class="loading-placeholder">
                    <i class="bi bi-hourglass-split"></i>
                    <span>加载MCP服务器列表...</span>
                </div>
            `;
        }

        function showError(message) {
            mcpGrid.innerHTML = `
                <div class="loading-placeholder">
                    <i class="bi bi-exclamation-triangle text-danger"></i>
                    <span>${message}</span>
                </div>
            `;
        }

        function renderMcpGrid(mcps = allMcps) {
            if (!mcps || mcps.length === 0) {
                mcpGrid.innerHTML = `
                    <div class="new-mcp-card" onclick="openDrawer(); createNewMcp();">
                        <i class="bi bi-plus-circle"></i>
                        <h3>创建第一个MCP服务器</h3>
                        <p>开始配置您的第一个MCP服务器</p>
                    </div>
                    <div class="empty-grid">
                        <i class="bi bi-server"></i>
                        <h3>暂无MCP服务器</h3>
                        <p>您还没有配置任何MCP服务器，点击上方卡片开始创建</p>
                    </div>
                `;
                return;
            }

            const newCardHtml = `
                <div class="new-mcp-card" onclick="openDrawer(); createNewMcp();">
                    <i class="bi bi-plus-circle"></i>
                    <h3>新建MCP服务器</h3>
                    <p>添加新的MCP服务器配置</p>
                </div>
            `;

            const mcpCardsHtml = mcps.map(mcp => `
                <div class="mcp-card" data-id="${mcp.id}" onclick="selectMcp(${mcp.id})">
                    <div class="mcp-card-header">
                        <h3 class="mcp-card-title">${escapeHtml(mcp.mcpServerName)}</h3>
                        <div class="mcp-card-id">ID: ${mcp.id}</div>
                    </div>
                    <div class="mcp-card-type ${mcp.connectionType}">${mcp.connectionType}</div>
                    <div class="mcp-card-config">${escapeHtml(formatConfig(mcp.connectionConfig))}</div>
                    <div class="mcp-card-actions">
                        <button class="card-action-btn" onclick="event.stopPropagation(); editMcp(${mcp.id})">
                            <i class="bi bi-pencil"></i> 编辑
                        </button>
                        <button class="card-action-btn danger" onclick="event.stopPropagation(); deleteMcp(${mcp.id})">
                            <i class="bi bi-trash"></i> 删除
                        </button>
                    </div>
                </div>
            `).join('');

            mcpGrid.innerHTML = newCardHtml + mcpCardsHtml;
        }

        function formatConfig(configStr) {
            try {
                const config = JSON.parse(configStr);
                return JSON.stringify(config, null, 2);
            } catch (e) {
                return configStr;
            }
        }

        function filterMcps(keyword) {
            if (!keyword) {
                renderMcpGrid();
                return;
            }

            const filtered = allMcps.filter(mcp =>
                mcp.mcpServerName.toLowerCase().includes(keyword) ||
                mcp.connectionType.toLowerCase().includes(keyword) ||
                (mcp.connectionConfig && mcp.connectionConfig.toLowerCase().includes(keyword))
            );

            renderMcpGrid(filtered);
        }

        function openDrawer() {
            configDrawer.classList.add('show');
            drawerOverlay.classList.add('show');
            document.body.style.overflow = 'hidden';
        }

        function closeDrawer() {
            configDrawer.classList.remove('show');
            drawerOverlay.classList.remove('show');
            document.body.style.overflow = '';
            currentMcp = null;
            isEditing = false;

            // 清除选中状态
            document.querySelectorAll('.mcp-card').forEach(card => {
                card.classList.remove('selected');
            });
        }

        function selectMcp(mcpId) {
            // 移除之前的选中状态
            document.querySelectorAll('.mcp-card').forEach(card => {
                card.classList.remove('selected');
            });

            // 添加当前选中状态
            const selectedCard = document.querySelector(`[data-id="${mcpId}"]`);
            if (selectedCard) {
                selectedCard.classList.add('selected');
            }

            // 查找MCP数据
            currentMcp = allMcps.find(mcp => mcp.id === mcpId);
            if (!currentMcp) {
                console.error('未找到MCP:', mcpId);
                return;
            }

            // 打开抽屉并显示配置表单
            openDrawer();
            showMcpDetail(currentMcp);
        }

        function editMcp(mcpId) {
            selectMcp(mcpId);
        }

        function createNewMcp() {
            // 清除选中状态
            document.querySelectorAll('.mcp-card').forEach(card => {
                card.classList.remove('selected');
            });

            // 重置当前MCP
            currentMcp = null;

            // 显示新建表单
            showMcpDetail(null);
        }

        function showMcpDetail(mcp) {
            console.log('开始显示MCP详情:', mcp);

            const isEdit = !!mcp;
            isEditing = isEdit;

            // 更新抽屉标题
            const drawerTitle = document.getElementById('drawerTitle');
            if (drawerTitle) {
                drawerTitle.textContent = isEdit ? `编辑: ${mcp.mcpServerName}` : '新建MCP服务器';
            }

            // 创建表单内容
            createMcpForm(mcp);

            console.log('MCP详情显示完成');
        }

        function escapeHtml(text) {
            if (!text) return '';
            const div = document.createElement('div');
            div.textContent = text;
            return div.innerHTML;
        }

        function createMcpForm(mcp) {
            const isEdit = !!mcp;
            isEditing = isEdit;

            const drawerContent = document.getElementById('drawerContent');
            const drawerFooter = document.getElementById('drawerFooter');

            const formHtml = `
                <!-- 基本信息 -->
                <div class="form-section">
                    <div class="section-title">
                        <i class="bi bi-info-circle"></i>
                        <span>基本信息</span>
                    </div>

                    <div class="form-group">
                        <label class="form-label" for="mcpServerName">服务器名称 *</label>
                        <input type="text" class="form-input" id="mcpServerName"
                            placeholder="输入MCP服务器名称" value="${isEdit ? escapeHtml(mcp.mcpServerName) : ''}" required>
                        <small class="text-muted">唯一标识此MCP服务器的名称</small>
                    </div>

                    <div class="form-group">
                        <label class="form-label" for="connectionType">连接类型 *</label>
                        <select class="form-select" id="connectionType" required>
                            <option value="">请选择连接类型</option>
                            <option value="SSE" ${isEdit && mcp.connectionType === 'SSE' ? 'selected' : ''}>SSE</option>
                            <option value="STREAMING" ${isEdit && mcp.connectionType === 'STREAMING' ? 'selected' : ''}>STREAMING</option>
                            <option value="STUDIO" ${isEdit && mcp.connectionType === 'STUDIO' ? 'selected' : ''}>STUDIO</option>
                        </select>
                        <small class="text-muted">选择MCP服务器的连接方式</small>
                    </div>
                </div>

                <!-- 配置信息 -->
                <div class="form-section">
                    <div class="section-title">
                        <i class="bi bi-gear"></i>
                        <span>配置信息</span>
                    </div>

                    <div class="form-group">
                        <label class="form-label" for="connectionConfig">连接配置 *</label>
                        <textarea class="form-textarea large" id="connectionConfig"
                            placeholder="输入JSON格式的连接配置" required>${isEdit ? escapeHtml(formatConfig(mcp.connectionConfig)) : ''}</textarea>
                        <small class="text-muted">JSON格式的MCP服务器连接配置，例如命令、参数、环境变量等</small>
                    </div>

                    <div class="form-group">
                        <label class="form-label">配置示例</label>
                        <div class="config-examples">
                            <div class="example-item">
                                <strong>Tavily搜索服务器:</strong>
                                <pre class="example-code">{"command": "npx", "args": ["-y", "@tavily/mcp-server"], "env": {"TAVILY_API_KEY": "your-api-key"}}</pre>
                            </div>
                            <div class="example-item">
                                <strong>文件系统服务器:</strong>
                                <pre class="example-code">{"command": "npx", "args": ["-y", "@modelcontextprotocol/server-filesystem", "/tmp"]}</pre>
                            </div>
                        </div>
                    </div>
                </div>
            `;

            const footerHtml = `
                <button class="btn btn-secondary" onclick="closeDrawer()">
                    <i class="bi bi-x"></i>
                    <span>取消</span>
                </button>
                <button class="btn btn-primary" id="saveMcpBtn">
                    <i class="bi bi-check"></i>
                    <span>保存</span>
                </button>
                ${isEdit ? `
                <button class="btn btn-danger" id="deleteMcpBtn">
                    <i class="bi bi-trash"></i>
                    <span>删除</span>
                </button>
                ` : ''}
            `;

            drawerContent.innerHTML = formHtml;
            drawerFooter.innerHTML = footerHtml;

            // 添加事件监听器
            setupFormEventListeners();
        }

        function setupFormEventListeners() {
            // 保存按钮
            const saveMcpBtn = document.getElementById('saveMcpBtn');
            if (saveMcpBtn) {
                saveMcpBtn.addEventListener('click', saveMcp);
            }

            // 删除按钮
            const deleteMcpBtn = document.getElementById('deleteMcpBtn');
            if (deleteMcpBtn) {
                deleteMcpBtn.addEventListener('click', deleteMcp);
            }
        }

        async function saveMcp() {
            try {
                // 验证表单
                if (!validateMcpForm()) {
                    return;
                }

                const saveMcpBtn = document.getElementById('saveMcpBtn');

                // 禁用保存按钮
                saveMcpBtn.disabled = true;
                saveMcpBtn.innerHTML = '<i class="bi bi-hourglass-split"></i> 保存中...';

                // 构建MCP配置对象
                const mcpConfig = {
                    connectionType: document.getElementById('connectionType').value,
                    configJson: document.getElementById('connectionConfig').value.trim()
                };

                // 调用API保存
                const response = await fetch('/api/mcp/add', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(mcpConfig)
                });

                await handleRResponse(response);

                // 重新加载数据
                await loadInitialData();

                // 关闭抽屉
                closeDrawer();

                // 显示成功消息
                showSuccessMessage('MCP服务器保存成功');

                console.log('MCP服务器保存成功');

            } catch (error) {
                console.error('保存MCP服务器失败:', error);
                showErrorMessage('保存失败: ' + error.message);
            } finally {
                // 恢复保存按钮
                const saveMcpBtn = document.getElementById('saveMcpBtn');
                if (saveMcpBtn) {
                    saveMcpBtn.disabled = false;
                    saveMcpBtn.innerHTML = '<i class="bi bi-check"></i> 保存';
                }
            }
        }

        function validateMcpForm() {
            const errors = [];

            const mcpServerName = document.getElementById('mcpServerName').value.trim();
            const connectionType = document.getElementById('connectionType').value;
            const connectionConfig = document.getElementById('connectionConfig').value.trim();

            if (!mcpServerName) {
                errors.push('服务器名称不能为空');
            }

            if (!connectionType) {
                errors.push('请选择连接类型');
            }

            if (!connectionConfig) {
                errors.push('连接配置不能为空');
            } else {
                try {
                    JSON.parse(connectionConfig);
                } catch (e) {
                    errors.push('连接配置必须是有效的JSON格式');
                }
            }

            if (errors.length > 0) {
                showErrorMessage('请修正以下错误：\n' + errors.join('\n'));
                return false;
            }

            return true;
        }

        async function deleteMcp(mcpId = null) {
            // 如果传入了mcpId，则直接删除；否则删除当前编辑的MCP
            const targetMcp = mcpId ? allMcps.find(mcp => mcp.id === mcpId) : currentMcp;

            if (!targetMcp) {
                return;
            }

            if (!confirm(`确定要删除MCP服务器 "${targetMcp.mcpServerName}" 吗？此操作不可撤销。`)) {
                return;
            }

            try {
                const deleteMcpBtn = document.getElementById('deleteMcpBtn');

                // 禁用删除按钮
                deleteMcpBtn.disabled = true;
                deleteMcpBtn.innerHTML = '<i class="bi bi-hourglass-split"></i> 删除中...';

                const response = await fetch(`/api/mcp/remove?id=${targetMcp.id}`, {
                    method: 'GET'
                });

                await handleRResponse(response);

                // 重新加载数据
                await loadInitialData();

                // 如果删除的是当前编辑的MCP，关闭抽屉
                if (targetMcp.id === (currentMcp && currentMcp.id)) {
                    closeDrawer();
                }

                // 显示成功消息
                showSuccessMessage('MCP服务器删除成功');

                console.log('MCP服务器删除成功:', targetMcp.mcpServerName);

            } catch (error) {
                console.error('删除MCP服务器失败:', error);
                showErrorMessage('删除失败: ' + error.message);
            } finally {
                // 恢复删除按钮
                const deleteMcpBtn = document.getElementById('deleteMcpBtn');
                if (deleteMcpBtn) {
                    deleteMcpBtn.disabled = false;
                    deleteMcpBtn.innerHTML = '<i class="bi bi-trash"></i> 删除此MCP服务器';
                }
            }
        }



        function showSuccessMessage(message) {
            const toast = document.createElement('div');
            toast.className = 'alert alert-success position-fixed';
            toast.style.cssText = 'top: 80px; right: 20px; z-index: 9999; min-width: 300px;';
            toast.innerHTML = `
                <i class="bi bi-check-circle"></i>
                ${escapeHtml(message)}
            `;
            document.body.appendChild(toast);

            setTimeout(() => {
                toast.remove();
            }, 3000);
        }

        function showErrorMessage(message) {
            const toast = document.createElement('div');
            toast.className = 'alert alert-danger position-fixed';
            toast.style.cssText = 'top: 80px; right: 20px; z-index: 9999; min-width: 300px;';
            toast.innerHTML = `
                <i class="bi bi-exclamation-triangle"></i>
                ${escapeHtml(message)}
            `;
            document.body.appendChild(toast);

            setTimeout(() => {
                toast.remove();
            }, 5000);
        }

        function showDebugInfo() {
            const debugInfo = {
                '页面状态': {
                    'currentMcp': currentMcp ? currentMcp.mcpServerName : 'null',
                    'isEditing': isEditing,
                    'allMcps': allMcps.length
                },
                'DOM元素状态': {
                    'mcpGrid': !!mcpGrid,
                    'configDrawer': !!configDrawer,
                    'drawerOverlay': !!drawerOverlay
                }
            };

            console.log('=== 调试信息 ===');
            console.table(debugInfo);

            alert('调试信息已输出到控制台，请按F12查看');
        }
    </script>
</body>
</html>
