/* agent.css - Agent相关样式 */

/* Agent配置面板布局 */
.agent-layout {
    display: flex;
    height: calc(100vh - 180px);
    gap: 20px;
}

/* 左侧Agent列表区域 */
.agent-list {
    width: 280px;
    flex-shrink: 0;
    display: flex;
    flex-direction: column;
    border-right: 1px solid #e1e2e3;
    background-color: #f8f9fa;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}

.agent-list-container {
    flex: 1;
    overflow-y: auto;
    padding: 12px;
}

/* Agent列表项 */
.agent-item {
    background: #fff;
    border: 1px solid #e1e2e3;
    border-radius: 8px;
    margin-bottom: 12px;
    overflow: hidden;
    transition: all 0.3s ease;
}

.agent-item:hover {
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    transform: translateY(-2px);
}

.agent-item-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 16px;
    background: #f8f9fa;
    border-bottom: 1px solid #e1e2e3;
}

.agent-name {
    font-weight: 600;
    color: #202124;
}

.expand-btn {
    background: none;
    border: none;
    color: #1a73e8;
    cursor: pointer;
    width: 24px;
    height: 24px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: background-color 0.3s ease;
}

.expand-btn:hover {
    background-color: rgba(26, 115, 232, 0.1);
}

.agent-item-content {
    padding: 12px 16px;
}

.agent-desc {
    font-size: 13px;
    color: #5f6368;
    margin-bottom: 10px;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
    line-height: 1.4;
}

/* Agent工具标签 */
.agent-tools {
    display: flex;
    flex-wrap: wrap;
    gap: 6px;
}

.tool-tag {
    font-size: 12px;
    color: #1a73e8;
    background-color: #e8f0fe;
    padding: 2px 8px;
    border-radius: 12px;
    white-space: nowrap;
}

/* 添加Agent按钮区域 */
.add-agent-container {
    padding: 16px;
    border-top: 1px solid #e1e2e3;
}

.add-agent-btn {
    width: 100%;
    padding: 10px;
    background-color: #1a73e8;
    color: #fff;
    border: none;
    border-radius: 4px;
    font-weight: 500;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    cursor: pointer;
    transition: background-color 0.3s ease;
}

.add-agent-btn:hover {
    background-color: #1557b0;
}

/* 右侧Agent详情区域 */
.agent-detail {
    flex: 1;
    background-color: #fff;
    border-radius: 8px;
    padding: 20px;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
    overflow-y: auto;
}

.detail-section {
    margin-bottom: 24px;
}

.detail-section label {
    display: block;
    font-weight: 500;
    color: #202124;
    margin-bottom: 8px;
}

.detail-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 12px;
}

/* 工具列表 */
.tool-list {
    margin: 10px 0;
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
}

.tool-item {
    display: flex;
    align-items: center;
    gap: 6px;
    padding: 6px 10px;
    background-color: #f8f9fa;
    border: 1px solid #e1e2e3;
    border-radius: 4px;
    transition: all 0.2s ease;
}

.tool-item:hover {
    background-color: #f1f3f4;
    border-color: #dadce0;
}

.tool-name {
    font-size: 13px;
    color: #202124;
}

.delete-tool-btn {
    background: none;
    border: none;
    color: #5f6368;
    font-size: 16px;
    cursor: pointer;
    padding: 0 2px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.delete-tool-btn:hover {
    color: #ea4335;
}

.add-tool-btn {
    margin-top: 10px;
    padding: 8px 16px;
    background-color: #fff;
    color: #1a73e8;
    border: 1px solid #1a73e8;
    border-radius: 4px;
    font-weight: 500;
    display: inline-flex;
    align-items: center;
    gap: 6px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.add-tool-btn:hover {
    background-color: #f8f9fa;
}

.detail-actions {
    margin-top: 24px;
    display: flex;
    justify-content: flex-end;
}

.save-agent-btn {
    padding: 10px 20px;
    background-color: #1a73e8;
    color: #fff;
    font-weight: 500;
}
