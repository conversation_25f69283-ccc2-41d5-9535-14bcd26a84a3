# API 返回格式统一修改说明

## 修改概述

将静态资源中的 JavaScript 文件适配新的统一 R 类返回格式：
```json
{
    "code": 1,
    "message": "success",
    "data": [实际数据]
}
```

## 修改的文件

### JavaScript API 文件

#### 1. mcp-api.js
- 添加了 `_handleRResponse()` 方法处理 R 类格式
- 修改了 `getAllMcpServers()`, `addMcpServer()`, `removeMcpServer()` 方法

#### 2. workflow-list.js
- 添加了 `handleRResponse()` 函数
- 修改了 `loadWorkflowList()` 和 `deleteWorkflow()` 方法

#### 3. workflow-designer.js
- 添加了 `handleRResponse()` 函数
- 修改了 `loadWorkflowData()`, `loadAgents()`, `saveWorkflow()` 方法

#### 4. admin-api.js
- 添加了 `_handleRResponse()` 方法
- 修改了 `getConfigsByGroup()`, `batchUpdateConfigs()`, `getAllAgents()` 方法

#### 5. api.js
- 修复了 `handleResponse()` 函数中的 code 检查逻辑（从 '200' 改为 1）

### HTML 页面文件

#### 6. agent-config.html
- 添加了 `handleRResponse()` 函数
- 修改了 `loadAgents()`, `loadTools()`, `createAgent()`, `updateAgent()`, `deleteAgentAPI()` 方法

#### 7. mcp-config.html
- 添加了 `handleRResponse()` 函数
- 修改了 `loadMcps()`, `saveMcp()`, `deleteMcp()` 方法

#### 8. main-dashboard.html
- 无需修改（仅为导航页面，无直接 API 调用）

## 核心处理逻辑

所有添加的 `handleRResponse()` 函数都包含以下逻辑：

```javascript
async function handleRResponse(response) {
    if (!response.ok) {
        throw new Error(`API请求失败: ${response.status}`);
    }

    const result = await response.json();

    // 检查R类响应格式
    if (result.code && result.code !== 1) {
        throw new Error(result.message || 'API请求失败');
    }

    // 返回data字段，如果没有data字段则返回整个结果（兼容旧格式）
    return result.data !== undefined ? result.data : result;
}
```

## 测试验证

修改后的前端应该能够正确处理以下 API 端点的新格式：

1. `/api/mcp/list` - MCP 服务器列表
2. `/api/plan-template/list` - 计划模板列表
3. `/api/agents` - 智能体列表
4. `/api/config/group/{groupName}` - 配置组数据
5. 其他相关 API 端点

## 兼容性

修改后的代码保持向后兼容，如果 API 返回的不是 R 类格式，会直接返回原始数据。
