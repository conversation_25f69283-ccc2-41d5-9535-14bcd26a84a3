/* chat-area.css - 计划模板页面中的聊天区域样式 */

/* 聊天区域容器 */
.simple-chat-area {
    background-color: #ffffff;
    border-radius: 8px;
    border: 1px solid #e0e0e0;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
    padding: 16px;
    margin-top: 20px;
    margin-bottom: 20px;
    min-height: 400px; /* 增加最小高度 */
    max-height: 600px; /* 增加最大高度 */
    overflow-y: auto;
    display: flex;
    flex-direction: column; /* 确保消息垂直排列 */
}

/* 聊天区域标题 */
.chat-area-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
    padding-bottom: 8px;
    border-bottom: 1px solid #e8eaed;
}

.chat-area-header h2 {
    font-size: 16px;
    font-weight: 600;
    color: #202124;
    margin: 0;
}

.chat-area-actions {
    display: flex;
    gap: 8px;
}

/* 消息通用样式 */
.message {
    margin-bottom: 20px;
    max-width: 90%; /* 限制宽度 */
}

/* 用户消息 */
.user-message {
    background-color: #f0f0f0; /* 用户消息浅灰色背景 */
    padding: 12px 16px;
    border-radius: 18px 18px 18px 4px; /* 气泡形状 */
    margin-left: auto; /* 右对齐 */
}

/* AI消息 */
.ai-message {
    background-color: #ffffff;
    padding: 16px;
    border-radius: 8px;
    margin-right: auto; /* 左对齐 */
    width: 100%; /* 让它占据可用的全宽 */
    max-width: 100%; /* 覆盖.message的max-width */
    border: 1px solid #f0f0f0;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}

/* AI消息头部 */
.ai-header {
    display: flex;
    align-items: center;
    font-weight: 600;
    margin-bottom: 10px;
    color: #1f1f1f;
}

.ai-logo {
    margin-right: 8px;
    font-size: 16px;
}

/* AI消息内容 */
.ai-content {
    font-size: 14px;
    line-height: 1.5;
    color: #333;
}

/* 对话轮次容器 */
.dialog-round-container {
    margin-bottom: 24px;
}

/* AI消息分节 */
.ai-section {
    margin-top: 16px;
    padding-top: 16px;
    border-top: 1px solid #f0f0f0; /* 分隔线 */
}

/* 分节标题 */
.section-header {
    font-weight: 500;
    display: flex;
    align-items: center;
    cursor: pointer;
    color: #1f1f1f;
}

.section-header.checked .icon {
    color: #34a853; /* 绿色勾选标记 */
    margin-right: 6px;
}

.section-header .toggle-arrow {
    margin-left: auto;
    color: #888;
}

/* 分节内容 */
.section-content {
    font-size: 13px;
    color: #555;
    margin-top: 6px;
    padding-left: 22px; /* 在图标下方缩进内容 */
}

/* 状态更新 */
.status-update {
    font-size: 12px;
    color: #777;
    background-color: #f8f9fa;
    border: 1px solid #e8eaed;
    border-radius: 4px;
    padding: 6px 10px;
    margin-top: 8px;
    margin-left: 22px; /* 缩进 */
    display: inline-flex; /* 适应内容 */
    align-items: center;
}

.status-update .icon {
    margin-right: 6px;
}

/* 操作信息样式 */
.action-info {
    margin-top: 8px;
    margin-left: 22px;
    font-size: 12px;
    color: #666;
    background-color: #f8f9fa;
    border: 1px solid #eee;
    border-radius: 6px;
    padding: 8px 12px;
}

.action-info .action-description,
.action-info .tool-params {
    display: flex;
    align-items: center;
    line-height: 1.4;
}

.action-info .tool-params {
    margin-top: 4px;
    color: #888;
    font-family: monospace;
}

.action-info .icon {
    margin-right: 8px;
    font-size: 14px;
}

/* 空消息提示 */
.empty-chat-message {
    text-align: center;
    color: #757575;
    padding: 30px 0;
    font-size: 14px;
}
