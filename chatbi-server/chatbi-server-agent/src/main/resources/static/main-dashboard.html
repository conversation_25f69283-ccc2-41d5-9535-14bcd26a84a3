<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>JTaskPilot - 多智能体推理执行框架</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.0/font/bootstrap-icons.css" rel="stylesheet">
    <style>
        :root {
            --primary: #667eea;
            --primary-hover: #5a6fd8;
            --secondary: #764ba2;
            --accent: #f093fb;
            --success: #4ade80;
            --warning: #fbbf24;
            --danger: #f87171;
            --gray-50: #f9fafb;
            --gray-100: #f3f4f6;
            --gray-200: #e5e7eb;
            --gray-300: #d1d5db;
            --gray-400: #9ca3af;
            --gray-500: #6b7280;
            --gray-600: #4b5563;
            --gray-700: #374151;
            --gray-800: #1f2937;
            --gray-900: #111827;
            --white: #ffffff;
        }

        * {
            box-sizing: border-box;
        }

        body {
            margin: 0;
            padding: 0;
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            position: relative;
        }

        body::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="20" height="20" patternUnits="userSpaceOnUse"><path d="M 20 0 L 0 0 0 20" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="1"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
            pointer-events: none;
        }

        /* 主导航栏 */
        .main-navbar {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-bottom: 1px solid rgba(255, 255, 255, 0.2);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            position: sticky;
            top: 0;
            z-index: 1000;
        }

        .navbar-brand {
            font-size: 1.5rem;
            font-weight: 800;
            background: linear-gradient(135deg, var(--primary) 0%, var(--secondary) 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            text-decoration: none;
        }

        .nav-link {
            color: var(--gray-700) !important;
            font-weight: 500;
            padding: 0.75rem 1rem !important;
            border-radius: 12px;
            transition: all 0.3s ease;
            margin: 0 0.25rem;
        }

        .nav-link:hover {
            background: rgba(102, 126, 234, 0.1);
            color: var(--primary) !important;
            transform: translateY(-1px);
        }

        .nav-link.active {
            background: linear-gradient(135deg, var(--primary) 0%, var(--secondary) 100%);
            color: white !important;
            box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
        }

        /* 主内容区域 */
        .main-container {
            position: relative;
            z-index: 1;
            min-height: calc(100vh - 76px);
        }

        .content-frame {
            width: 100%;
            height: calc(100vh - 76px);
            border: none;
            background: transparent;
        }

        /* 加载状态 */
        .loading-overlay {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(255, 255, 255, 0.9);
            backdrop-filter: blur(10px);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 999;
            opacity: 0;
            visibility: hidden;
            transition: all 0.3s ease;
        }

        .loading-overlay.show {
            opacity: 1;
            visibility: visible;
        }

        .loading-spinner {
            width: 48px;
            height: 48px;
            border: 4px solid rgba(102, 126, 234, 0.2);
            border-top: 4px solid var(--primary);
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .navbar-nav {
                background: rgba(255, 255, 255, 0.95);
                backdrop-filter: blur(20px);
                border-radius: 16px;
                margin-top: 1rem;
                padding: 1rem;
            }
        }

        /* 动画效果 */
        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .main-container {
            animation: fadeInUp 0.6s ease-out;
        }
    </style>
</head>
<body>
    <!-- 主导航栏 -->
    <nav class="navbar navbar-expand-lg main-navbar">
        <div class="container-fluid px-4">
            <a class="navbar-brand" href="#" onclick="loadPage('index.html')">
                <i class="bi bi-robot me-2"></i>
                JTaskPilot
            </a>

            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>

            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link active" href="#" onclick="loadPage('hierarchical-chat.html')" data-page="hierarchical-chat.html">
                            <i class="bi bi-chat-dots me-1"></i>
                            智能对话
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#" onclick="loadPage('workflow-list.html')" data-page="workflow-list.html">
                            <i class="bi bi-diagram-3 me-1"></i>
                            智能体编排
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#" onclick="loadPage('agent-config.html')" data-page="agent-config.html">
                            <i class="bi bi-gear me-1"></i>
                            智能体配置
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#" onclick="loadPage('mcp-config.html')" data-page="mcp-config.html">
                            <i class="bi bi-tools me-1"></i>
                            工具管理
                        </a>
                    </li>
                </ul>

                <ul class="navbar-nav">
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                            <i class="bi bi-three-dots me-1"></i>
                            更多
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="#" onclick="loadPage('streaming-event-viewer.html')">
                                <i class="bi bi-activity me-2"></i>流式事件查看器
                            </a></li>
                            <li><a class="dropdown-item" href="#" onclick="loadPage('plan-template/index.html')">
                                <i class="bi bi-journal-text me-2"></i>计划模板编辑器
                            </a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="/swagger-ui/index.html" target="_blank">
                                <i class="bi bi-code-square me-2"></i>API 文档
                            </a></li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- 主内容区域 -->
    <div class="main-container">
        <!-- 加载状态覆盖层 -->
        <div class="loading-overlay" id="loadingOverlay">
            <div class="loading-spinner"></div>
        </div>

        <!-- 内容框架 -->
        <iframe id="contentFrame" class="content-frame" src="hierarchical-chat.html"></iframe>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // 页面加载管理
        let currentPage = 'hierarchical-chat.html';

        function loadPage(pagePath, params = {}) {
            // 构建完整的URL
            let fullUrl = pagePath;
            if (Object.keys(params).length > 0) {
                const urlParams = new URLSearchParams(params);
                fullUrl = `${pagePath}?${urlParams.toString()}`;
            }

            if (currentPage === fullUrl) return;

            const frame = document.getElementById('contentFrame');
            const loading = document.getElementById('loadingOverlay');

            console.log('🔄 加载页面:', fullUrl);

            // 显示加载状态
            loading.classList.add('show');

            // 更新导航状态
            updateNavigation(pagePath);

            // 加载页面
            frame.src = fullUrl;
            currentPage = fullUrl;

            // 监听加载完成
            frame.onload = function() {
                console.log('✅ 页面加载完成:', fullUrl);

                // 如果是聊天页面且有参数，发送消息给iframe
                if (pagePath.includes('hierarchical-chat.html') && Object.keys(params).length > 0) {
                    setTimeout(() => {
                        console.log('📤 向聊天页面发送参数:', params);
                        frame.contentWindow.postMessage({
                            type: 'SET_CHAT_ID',
                            ...params
                        }, '*');
                    }, 500);
                }

                setTimeout(() => {
                    loading.classList.remove('show');
                }, 300);
            };

            // 设置加载超时
            setTimeout(() => {
                if (loading.classList.contains('show')) {
                    loading.classList.remove('show');
                    console.warn('页面加载超时:', fullUrl);
                }
            }, 10000);
        }

        // 新增：带参数加载聊天页面的函数
        function loadChatPage(chatId, title = '对话历史') {
            console.log('📜 加载聊天页面:', { chatId, title });
            loadPage('hierarchical-chat.html', {
                chatId: chatId,
                title: encodeURIComponent(title)
            });
        }

        function updateNavigation(pagePath) {
            // 移除所有活动状态
            document.querySelectorAll('.nav-link').forEach(link => {
                link.classList.remove('active');
            });

            // 添加当前页面的活动状态
            const activeLink = document.querySelector(`[data-page="${pagePath}"]`);
            if (activeLink) {
                activeLink.classList.add('active');
            }
        }

        // 初始化
        document.addEventListener('DOMContentLoaded', function() {
            console.log('JTaskPilot 多智能体框架已启动');

            // 设置初始页面 - 加载新的独立导航页面
            loadPage('dashboard.html');

            // 添加测试功能
            addTestFeatures();
        });

        // 添加测试功能
        function addTestFeatures() {
            // 在导航栏添加测试按钮
            const navbar = document.querySelector('.navbar-nav');
            if (navbar) {
                const testItem = document.createElement('li');
                testItem.className = 'nav-item dropdown';
                testItem.innerHTML = `
                    <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                        <i class="bi bi-bug me-1"></i>
                        测试功能
                    </a>
                    <ul class="dropdown-menu">
                        <li><a class="dropdown-item" href="#" onclick="testChatHistory()">测试聊天历史</a></li>
                        <li><a class="dropdown-item" href="#" onclick="loadChatPage('test-chat-123', '测试对话')">加载测试对话</a></li>
                        <li><hr class="dropdown-divider"></li>
                        <li><a class="dropdown-item" href="#" onclick="loadPage('hierarchical-chat.html')">直接打开聊天页面</a></li>
                    </ul>
                `;
                navbar.appendChild(testItem);
            }
        }

        // 测试聊天历史功能
        function testChatHistory() {
            console.log('🧪 测试聊天历史功能');

            // 模拟一些历史对话数据
            const testChats = [
                { chatId: 'chat-1748943584083', title: '测试对话1' },
                { chatId: 'chat-1749381252395-l7x8jj6he', title: '测试对话2' },
                { chatId: 'test-chat-123', title: '调试对话' }
            ];

            // 创建一个简单的对话列表弹窗
            const modal = document.createElement('div');
            modal.style.cssText = `
                position: fixed;
                top: 50%;
                left: 50%;
                transform: translate(-50%, -50%);
                background: white;
                border: 1px solid #ddd;
                border-radius: 8px;
                padding: 20px;
                box-shadow: 0 4px 12px rgba(0,0,0,0.15);
                z-index: 9999;
                max-width: 400px;
                width: 90%;
            `;

            let modalContent = '<h4>选择历史对话</h4>';
            testChats.forEach(chat => {
                modalContent += `
                    <div style="margin: 10px 0; padding: 10px; border: 1px solid #eee; border-radius: 4px; cursor: pointer;"
                         onclick="loadChatPage('${chat.chatId}', '${chat.title}'); document.body.removeChild(this.parentElement.parentElement);">
                        <strong>${chat.title}</strong><br>
                        <small style="color: #666;">${chat.chatId}</small>
                    </div>
                `;
            });
            modalContent += `
                <div style="margin-top: 15px; text-align: right;">
                    <button onclick="document.body.removeChild(this.parentElement.parentElement)"
                            style="padding: 8px 16px; background: #6c757d; color: white; border: none; border-radius: 4px; cursor: pointer;">
                        关闭
                    </button>
                </div>
            `;

            modal.innerHTML = modalContent;

            // 添加背景遮罩
            const overlay = document.createElement('div');
            overlay.style.cssText = `
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: rgba(0,0,0,0.5);
                z-index: 9998;
            `;
            overlay.onclick = () => {
                document.body.removeChild(overlay);
                document.body.removeChild(modal);
            };

            document.body.appendChild(overlay);
            document.body.appendChild(modal);
        }
    </script>
</body>
</html>
