<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>流式事件查看器</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/marked/marked.min.js"></script>
    <style>
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(10px); }
            to { opacity: 1; transform: translateY(0); }
        }

        @keyframes pulse {
            0% { transform: scale(1); opacity: 1; }
            50% { transform: scale(1.05); opacity: 0.8; }
            100% { transform: scale(1); opacity: 1; }
        }

        @keyframes typing {
            from { width: 0 }
            to { width: 100% }
        }

        @keyframes blink {
            50% { border-right-color: transparent }
        }

        @keyframes slideIn {
            from { transform: translateX(-20px); opacity: 0; }
            to { transform: translateX(0); opacity: 1; }
        }

        @keyframes slideOut {
            from { transform: translateX(0); opacity: 1; }
            to { transform: translateX(20px); opacity: 0; }
        }

        :root {
            --primary-gradient: linear-gradient(135deg, #6366f1 0%, #a855f7 100%);
            --glass-bg: rgba(255, 255, 255, 0.7);
            --glass-border: 1px solid rgba(255, 255, 255, 0.2);
            --glass-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.15);
            --card-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
            --transition-speed: 0.3s;
            --scrollbar-width: 8px;
            --scrollbar-track: rgba(255, 255, 255, 0.1);
            --scrollbar-thumb: rgba(99, 102, 241, 0.5);
        }

        /* 滚动条样式 */
        ::-webkit-scrollbar {
            width: var(--scrollbar-width);
        }

        ::-webkit-scrollbar-track {
            background: var(--scrollbar-track);
            border-radius: var(--scrollbar-width);
        }

        ::-webkit-scrollbar-thumb {
            background: var(--scrollbar-thumb);
            border-radius: var(--scrollbar-width);
            transition: background var(--transition-speed);
        }

        ::-webkit-scrollbar-thumb:hover {
            background: rgba(99, 102, 241, 0.8);
        }
        body {
            font-family: 'Microsoft YaHei', -apple-system, BlinkMacSystemFont, sans-serif;
            padding: 20px;
            background: var(--primary-gradient);
            min-height: 100vh;
            color: #1a1a1a;
        }
        .card {
            margin-bottom: 20px;
            background: var(--glass-bg);
            backdrop-filter: blur(10px);
            border: var(--glass-border);
            box-shadow: var(--glass-shadow);
            border-radius: 16px;
            transition: transform var(--transition-speed);
        }
        .card:hover {
            transform: translateY(-5px);
        }
        .event-container {
            max-height: 600px;
            overflow-y: auto;
            padding: 20px;
            background: var(--glass-bg);
            backdrop-filter: blur(10px);
            border: var(--glass-border);
            border-radius: 16px;
            box-shadow: var(--glass-shadow);
        }
        .event-item {
            padding: 15px;
            margin-bottom: 15px;
            border-radius: 12px;
            background: rgba(255, 255, 255, 0.9);
            border: var(--glass-border);
            box-shadow: var(--card-shadow);
            transition: transform var(--transition-speed), box-shadow var(--transition-speed);
            animation: fadeIn 0.5s ease-out;
        }
        .event-item:hover {
            transform: translateY(-2px);
            box-shadow: 0 12px 24px rgba(0, 0, 0, 0.15);
        }
        .event-item.plan-snapshot {
            border-left-color: #0d6efd;
        }
        .event-item.think-output {
            border-left-color: #198754;
        }
        .event-item.error {
            border-left-color: #dc3545;
        }
        .event-item.completed {
            border-left-color: #ffc107;
        }
        .event-timestamp {
            font-size: 0.8rem;
            color: #6c757d;
        }
        .event-type {
            font-weight: bold;
            margin-bottom: 5px;
        }
        .event-content {
            white-space: pre-wrap;
            word-break: break-word;
        }
        .thinking-container {
            background: rgba(240, 248, 255, 0.8);
            padding: 20px;
            border-radius: 16px;
            margin-bottom: 20px;
            border: var(--glass-border);
            backdrop-filter: blur(10px);
            box-shadow: var(--glass-shadow);
        }

        .thinking-container.active {
            animation: pulse 2s infinite;
        }

        .thinking-container.completed {
            animation: none;
            opacity: 0.8;
            transition: opacity var(--transition-speed);
        }

        /* 工作流程思考块样式 */
        .workflow-thinking {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 12px;
            padding: 15px;
            margin: 10px 0;
            font-size: 14px;
            line-height: 1.5;
            color: #333;
            position: relative;
            backdrop-filter: blur(10px);
            border: var(--glass-border);
            box-shadow: var(--glass-shadow);
            animation: thinkingPulse 2s infinite;
            transition: all var(--transition-speed);
        }

        .workflow-thinking.completed {
            animation: none;
            background: rgba(255, 255, 255, 0.15);
            border-color: rgba(255, 255, 255, 0.3);
            opacity: 0.9;
        }

        @keyframes thinkingPulse {
            0% { background: rgba(255, 255, 255, 0.1); }
            50% { background: rgba(255, 255, 255, 0.15); }
            100% { background: rgba(255, 255, 255, 0.1); }
        }

        /* 工作流程工具调用样式 */
        .workflow-tool-call {
            background: linear-gradient(135deg, rgba(253, 126, 20, 0.1), rgba(253, 126, 20, 0.05));
            border-radius: 12px;
            padding: 15px;
            margin: 20px 0 10px 0;
            font-size: 14px;
            color: #333;
            border: 1px solid rgba(253, 126, 20, 0.2);
            box-shadow: var(--card-shadow);
            opacity: 0;
            transform: translateY(10px);
            animation: toolCallStart 0.3s ease forwards;
        }

        .workflow-tool-content {
            font-family: monospace;
            white-space: nowrap;
            overflow: hidden;
            border-right: 3px solid;
            animation: typing 1s steps(30), blink .5s step-end 3 alternate;
        }

        @keyframes toolCallStart {
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        /* 工作流程工具结果样式 */
        .workflow-tool-result {
            background: linear-gradient(135deg, rgba(255, 255, 255, 0.15), rgba(255, 255, 255, 0.1));
            border-radius: 12px;
            padding: 15px;
            margin: 10px 0;
            font-size: 14px;
            color: #333;
            border: var(--glass-border);
            box-shadow: var(--glass-shadow);
            opacity: 0;
            transform: translateY(-10px);
            animation: toolResultShow 0.3s ease forwards;
        }

        @keyframes toolResultShow {
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        /* 思考过程样式 */
        .thinking-entry {
            margin-bottom: 15px;
            padding: 10px;
            border-radius: 5px;
            background-color: #ffffff;
            border-left: 4px solid #6c757d;
        }

        /* 工具调用样式 */
        .tool-call {
            margin: 15px 0;
            padding: 15px;
            border-radius: 12px;
            background: linear-gradient(135deg, rgba(253, 126, 20, 0.1), rgba(253, 126, 20, 0.05));
            border: 1px solid rgba(253, 126, 20, 0.2);
            box-shadow: var(--card-shadow);
            transition: all var(--transition-speed);
            position: relative;
            overflow: hidden;
        }

        .tool-call .tool-name {
            display: inline-block;
            font-family: monospace;
            white-space: nowrap;
            overflow: hidden;
            border-right: 3px solid;
            animation: typing 1s steps(30), blink .5s step-end infinite alternate;
            margin-bottom: 10px;
        }

        .tool-call.starting {
            animation: slideIn 0.3s ease-out;
        }

        .tool-call.completed {
            animation: slideOut 0.3s ease-in forwards;
        }
        .tool-call:hover {
            transform: translateY(-2px);
            box-shadow: 0 12px 24px rgba(253, 126, 20, 0.15);
        }

        /* 工具结果样式 */
        .tool-result {
            margin: 15px 0;
            padding: 15px;
            border-radius: 12px;
            background: linear-gradient(135deg, rgba(25, 135, 84, 0.1), rgba(25, 135, 84, 0.05));
            border: 1px solid rgba(25, 135, 84, 0.2);
            box-shadow: var(--card-shadow);
            transition: all var(--transition-speed);
        }
        .tool-result:hover {
            transform: translateY(-2px);
            box-shadow: 0 12px 24px rgba(25, 135, 84, 0.15);
        }

        /* 折叠/展开图标 */
        .toggle-icon {
            cursor: pointer;
            margin-right: 5px;
            display: inline-block;
            width: 16px;
            text-align: center;
        }

        /* 思考步骤标题 */
        .thinking-title {
            font-weight: bold;
            margin-bottom: 5px;
            display: flex;
            align-items: center;
        }

        /* 思考内容 */
        .thinking-content {
            white-space: pre-wrap;
            word-break: break-word;
            margin-left: 20px;
        }

        /* Markdown内容样式 */
        .markdown-content {
            white-space: normal;
        }

        .markdown-content pre {
            background-color: rgba(0, 0, 0, 0.05);
            padding: 10px;
            border-radius: 5px;
            overflow-x: auto;
            margin: 10px 0;
        }

        .markdown-content code {
            font-family: monospace;
            background-color: rgba(0, 0, 0, 0.05);
            padding: 2px 4px;
            border-radius: 3px;
        }

        .markdown-content pre code {
            background-color: transparent;
            padding: 0;
        }

        .markdown-content blockquote {
            border-left: 4px solid rgba(100, 100, 255, 0.3);
            padding-left: 10px;
            margin-left: 0;
            color: #555;
        }

        .markdown-content table {
            border-collapse: collapse;
            width: 100%;
            margin: 10px 0;
        }

        .markdown-content th, .markdown-content td {
            border: 1px solid rgba(0, 0, 0, 0.1);
            padding: 8px;
            text-align: left;
        }

        .markdown-content th {
            background-color: rgba(0, 0, 0, 0.05);
        }
        .agent-card {
            margin-bottom: 10px;
            border-left: 4px solid #6610f2;
        }
        .step-card {
            margin-bottom: 10px;
            border-left: 4px solid #fd7e14;
        }
        .summary-container {
            background-color: #f0fff0;
            padding: 15px;
            border-radius: 5px;
            margin-top: 15px;
            border: 1px solid #d4edda;
        }
        #loadingIndicator {
            display: none;
            text-align: center;
            padding: 30px;
            background: var(--glass-bg);
            border-radius: 16px;
            backdrop-filter: blur(10px);
            border: var(--glass-border);
            box-shadow: var(--glass-shadow);
            margin: 20px 0;
        }
        .spinner-border {
            width: 3rem;
            height: 3rem;
        }
        .badge {
            font-size: 0.8rem;
            padding: 0.5em 1em;
            border-radius: 20px;
            font-weight: 500;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            background: var(--primary-gradient);
            border: none;
            color: white;
            transition: all var(--transition-speed);
        }
        .badge:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
        }

        /* 智能体工作流程样式 */
        .agent-workflow-container {
            margin-bottom: 20px;
            border: var(--glass-border);
            border-radius: 16px;
            padding: 20px;
            background: var(--glass-bg);
            backdrop-filter: blur(10px);
            box-shadow: var(--glass-shadow);
        }

        .agent-workflow {
            display: flex;
            flex-direction: column;
            gap: 10px;
        }

        .workflow-item {
            padding: 20px;
            border-radius: 12px;
            background: rgba(255, 255, 255, 0.9);
            border: var(--glass-border);
            box-shadow: var(--card-shadow);
            transition: all var(--transition-speed);
        }
        .workflow-item:hover {
            transform: translateY(-2px);
            box-shadow: 0 12px 24px rgba(0, 0, 0, 0.15);
        }

        .workflow-agent-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
            padding-bottom: 10px;
            border-bottom: 1px solid #eee;
        }

        .workflow-agent-name {
            font-weight: bold;
            background: var(--primary-gradient);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            padding: 5px 0;
        }

        .workflow-agent-status {
            font-size: 0.8rem;
        }

        .workflow-content {
            margin-top: 10px;
        }

        .workflow-thinking {
            background: linear-gradient(135deg, rgba(99, 102, 241, 0.1), rgba(168, 85, 247, 0.05));
            padding: 20px;
            border-radius: 12px;
            margin-bottom: 15px;
            white-space: pre-wrap;
            word-break: break-word;
            position: relative;
            border: 1px solid rgba(99, 102, 241, 0.2);
            box-shadow: var(--card-shadow);
            transition: all var(--transition-speed);
        }
        .workflow-thinking:hover {
            transform: translateY(-2px);
            box-shadow: 0 12px 24px rgba(99, 102, 241, 0.15);
        }

        .workflow-thinking::before {
            content: '▼ 思考中...';
            display: block;
            font-weight: bold;
            margin-bottom: 10px;
            color: #495057;
        }

        .workflow-thinking.active::before {
            animation: pulse 2s infinite ease-in-out;
        }

        .workflow-thinking.completed::before {
            content: '✓ 思考完成';
            animation: none;
            color: #198754;
        }

        .workflow-tool-call {
            background: linear-gradient(135deg, rgba(253, 126, 20, 0.1), rgba(253, 126, 20, 0.05));
            padding: 20px;
            border-radius: 12px;
            margin-bottom: 15px;
            border: 1px solid rgba(253, 126, 20, 0.2);
            box-shadow: var(--card-shadow);
            transition: all var(--transition-speed);
            position: relative;
            overflow: hidden;
        }

        .workflow-tool-call::before {
            content: '► 开始调用工具';
            display: block;
            font-weight: bold;
            margin-bottom: 10px;
            color: #fd7e14;
        }

        .workflow-tool-call .tool-command {
            font-family: monospace;
            display: inline-block;
            white-space: nowrap;
            overflow: hidden;
            border-right: 3px solid;
            animation: typing 1s steps(30), blink .5s step-end infinite alternate;
            margin: 10px 0;
            padding: 5px 10px;
            background: rgba(253, 126, 20, 0.1);
            border-radius: 4px;
        }

        .workflow-tool-result {
            background: linear-gradient(135deg, rgba(25, 135, 84, 0.1), rgba(25, 135, 84, 0.05));
            padding: 20px;
            border-radius: 12px;
            margin-bottom: 15px;
            border: 1px solid rgba(25, 135, 84, 0.2);
            box-shadow: var(--card-shadow);
            transition: all var(--transition-speed);
            animation: fadeIn 0.5s ease-out;
        }

        .workflow-tool-result::before {
            content: '✓ 完成工具调用';
            display: block;
            font-weight: bold;
            margin-bottom: 10px;
            color: #198754;
        }

        .workflow-tool-header {
            font-weight: bold;
            margin-bottom: 10px;
        }

        .workflow-tool-content {
            white-space: pre-wrap;
            word-break: break-word;
            background-color: rgba(0, 0, 0, 0.03);
            padding: 10px;
            border-radius: 3px;
            font-family: monospace;
        }

        /* 记忆查看相关样式 */
        .memory-messages-container {
            max-height: 400px;
            overflow-y: auto;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 15px;
            background-color: #f8f9fa;
        }

        .memory-message-item {
            margin-bottom: 15px;
            padding: 12px;
            border-radius: 8px;
            background: white;
            border-left: 4px solid #6c757d;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
        }

        .memory-message-item:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }

        .memory-message-item.user-message {
            border-left-color: #0d6efd;
            background: linear-gradient(135deg, rgba(13, 110, 253, 0.05), rgba(13, 110, 253, 0.02));
        }

        .memory-message-item.assistant-message {
            border-left-color: #198754;
            background: linear-gradient(135deg, rgba(25, 135, 84, 0.05), rgba(25, 135, 84, 0.02));
        }

        .memory-message-item.tool-response-message {
            border-left-color: #fd7e14;
            background: linear-gradient(135deg, rgba(253, 126, 20, 0.05), rgba(253, 126, 20, 0.02));
        }

        .memory-message-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 8px;
            font-size: 0.9rem;
        }

        .memory-message-type {
            font-weight: bold;
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 0.8rem;
        }

        .memory-message-type.user {
            background-color: #0d6efd;
            color: white;
        }

        .memory-message-type.assistant {
            background-color: #198754;
            color: white;
        }

        .memory-message-type.tool-response {
            background-color: #fd7e14;
            color: white;
        }

        .memory-message-content {
            white-space: pre-wrap;
            word-break: break-word;
            line-height: 1.5;
        }

        .memory-tool-calls {
            margin-top: 10px;
            padding: 8px;
            background-color: rgba(253, 126, 20, 0.1);
            border-radius: 4px;
            border: 1px solid rgba(253, 126, 20, 0.2);
        }

        .memory-tool-call-item {
            margin-bottom: 5px;
            font-family: monospace;
            font-size: 0.9rem;
        }

        .memory-stats-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 8px 0;
            border-bottom: 1px solid #eee;
        }

        .memory-stats-item:last-child {
            border-bottom: none;
        }

        .memory-stats-label {
            font-weight: 500;
            color: #495057;
        }

        .memory-stats-value {
            font-weight: bold;
            color: #0d6efd;
        }

        .memory-loading {
            text-align: center;
            padding: 20px;
            color: #6c757d;
        }

        .memory-error {
            background-color: #f8d7da;
            color: #721c24;
            padding: 12px;
            border-radius: 4px;
            border: 1px solid #f5c6cb;
        }

        .memory-empty {
            text-align: center;
            padding: 20px;
            color: #6c757d;
            font-style: italic;
        }
    </style>
</head>
<body>
<div class="container">
    <h1 class="mb-4">流式事件查看器</h1>

    <div class="row mb-4">
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    执行新计划
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <label for="queryInput" class="form-label">请输入您的请求：</label>
                        <textarea class="form-control" id="queryInput" rows="3" placeholder="例如：帮我写一个Python程序计算斐波那契数列"></textarea>
                    </div>
                    <button id="executeBtn" class="btn btn-primary">执行计划</button>
                </div>
            </div>
        </div>

        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    执行计划模板
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <label for="templateIdInput" class="form-label">计划模板ID：</label>
                        <input type="text" class="form-control" id="templateIdInput" placeholder="输入计划模板ID">
                    </div>
                    <div class="mb-3">
                        <label for="paramsInput" class="form-label">参数（可选）：</label>
                        <input type="text" class="form-control" id="paramsInput" placeholder="格式：key1=value1&key2=value2">
                    </div>
                    <div class="mb-3">
                        <label for="chatIdInput" class="form-label">会话ID（可选）：</label>
                        <input type="text" class="form-control" id="chatIdInput" placeholder="用于连续对话，留空则创建新会话">
                        <div class="form-text">相同会话ID可实现连续对话，保持上下文记忆</div>
                    </div>
                    <div class="mb-3">
                        <label for="messageInput" class="form-label">用户消息（连续对话时必填）：</label>
                        <input type="text" class="form-control" id="messageInput" placeholder="输入您的问题或指令">
                        <div class="form-text">连续对话模式下，这个消息会替换计划模板中的预设任务</div>
                    </div>
                    <button id="executeTemplateBtn" class="btn btn-success">执行模板</button>
                    <button id="generateChatIdBtn" class="btn btn-outline-secondary ms-2">生成新会话ID</button>
                </div>
            </div>
        </div>
    </div>

    <div class="card mb-4">
        <div class="card-header d-flex justify-content-between align-items-center">
            <div>
                计划执行状态
                <span id="statusBadge" class="badge bg-secondary ms-2">未开始</span>
            </div>
            <div>
                <span id="planIdDisplay" class="badge bg-info">-</span>
            </div>
        </div>
        <div class="card-body">
            <div id="planInfoContainer" style="display: none;">
                <div class="row mb-3">
                    <div class="col-md-6">
                        <strong>标题：</strong> <span id="titleDisplay">-</span>
                    </div>
                    <div class="col-md-6">
                        <strong>用户请求：</strong> <span id="userRequestDisplay">-</span>
                    </div>
                </div>

                <div id="agentWorkflowContainer" class="agent-workflow-container">
                    <h5>智能体工作流程</h5>
                    <div id="agentWorkflow" class="agent-workflow"></div>
                </div>

                <div id="summaryContainer" class="summary-container" style="display: none;">
                    <h5>执行总结</h5>
                    <div id="summaryText"></div>
                </div>
            </div>

            <div id="loadingIndicator">
                <div class="spinner-border text-primary" role="status">
                    <span class="visually-hidden">加载中...</span>
                </div>
                <p class="mt-2">正在连接到事件流...</p>
            </div>
        </div>
    </div>

    <!-- 记忆查看面板 -->
    <div class="card mb-4">
        <div class="card-header d-flex justify-content-between align-items-center">
            <div>
                <i class="fas fa-brain me-2"></i>记忆查看
            </div>
            <div>
                <button id="refreshMemoryBtn" class="btn btn-sm btn-outline-primary me-2">刷新</button>
                <button id="clearMemoryBtn" class="btn btn-sm btn-outline-danger">清理记忆</button>
            </div>
        </div>
        <div class="card-body">
            <div class="row mb-3">
                <div class="col-md-6">
                    <label for="memoryConversationId" class="form-label">会话ID：</label>
                    <div class="input-group">
                        <input type="text" class="form-control" id="memoryConversationId" placeholder="输入会话ID或使用当前计划ID">
                        <button class="btn btn-outline-secondary" id="useCurrentPlanIdBtn" type="button">使用当前</button>
                    </div>
                </div>
                <div class="col-md-3">
                    <label for="memoryType" class="form-label">记忆类型：</label>
                    <select class="form-select" id="memoryType">
                        <option value="agent">Agent记忆</option>
                        <option value="conversation">对话记忆</option>
                        <option value="summary">记忆摘要</option>
                    </select>
                </div>
                <div class="col-md-3">
                    <label for="memoryLimit" class="form-label">消息限制：</label>
                    <input type="number" class="form-control" id="memoryLimit" placeholder="留空显示全部" min="1" max="100">
                </div>
            </div>

            <div class="row mb-3">
                <div class="col-12">
                    <button id="viewMemoryBtn" class="btn btn-primary me-2">查看记忆</button>
                    <button id="exportMemoryBtn" class="btn btn-outline-success me-2">导出记忆</button>
                    <span id="memoryStatus" class="text-muted"></span>
                </div>
            </div>

            <!-- 记忆显示区域 -->
            <div id="memoryDisplayArea" style="display: none;">
                <div class="row">
                    <div class="col-md-4">
                        <div class="card">
                            <div class="card-header">
                                <h6 class="mb-0">记忆统计</h6>
                            </div>
                            <div class="card-body">
                                <div id="memoryStats"></div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-8">
                        <div class="card">
                            <div class="card-header">
                                <h6 class="mb-0">消息列表</h6>
                            </div>
                            <div class="card-body">
                                <div id="memoryMessages" class="memory-messages-container"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="card">
        <div class="card-header">
            事件日志
            <button id="clearEventsBtn" class="btn btn-sm btn-outline-danger float-end">清空</button>
        </div>
        <div class="card-body">
            <div id="eventContainer" class="event-container"></div>
        </div>
    </div>
</div>

<script>
    // 全局变量
    let eventSource = null;
    let thinkingText = '';
    let currentPlanId = null;

    // 工作流程相关变量
    let currentAgentId = null;
    let currentAgentName = null;
    let currentAgentItem = null;
    let currentThinkingBlock = null;
    let agentWorkflowMap = {};
    let lastEventType = null; // 记录上一个事件类型
    let receivedThinkingEvents = {}; // 记录已收到的思考事件内容
    let lastThinkingContent = ''; // 记录最后一次思考内容
    let processedThinkingContents = new Set(); // 使用Set记录已处理的思考内容，避免重复

    // DOM元素
    const executeBtn = document.getElementById('executeBtn');
    const executeTemplateBtn = document.getElementById('executeTemplateBtn');
    const queryInput = document.getElementById('queryInput');
    const templateIdInput = document.getElementById('templateIdInput');
    const paramsInput = document.getElementById('paramsInput');
    const chatIdInput = document.getElementById('chatIdInput');
    const messageInput = document.getElementById('messageInput');
    const generateChatIdBtn = document.getElementById('generateChatIdBtn');
    const eventContainer = document.getElementById('eventContainer');
    const clearEventsBtn = document.getElementById('clearEventsBtn');
    const statusBadge = document.getElementById('statusBadge');
    const planIdDisplay = document.getElementById('planIdDisplay');
    const titleDisplay = document.getElementById('titleDisplay');
    const userRequestDisplay = document.getElementById('userRequestDisplay');
    const summaryText = document.getElementById('summaryText');
    const planInfoContainer = document.getElementById('planInfoContainer');
    const summaryContainer = document.getElementById('summaryContainer');
    const loadingIndicator = document.getElementById('loadingIndicator');
    const agentWorkflow = document.getElementById('agentWorkflow');

    // 记忆查看相关DOM元素
    const memoryConversationId = document.getElementById('memoryConversationId');
    const memoryType = document.getElementById('memoryType');
    const memoryLimit = document.getElementById('memoryLimit');
    const useCurrentPlanIdBtn = document.getElementById('useCurrentPlanIdBtn');
    const viewMemoryBtn = document.getElementById('viewMemoryBtn');
    const refreshMemoryBtn = document.getElementById('refreshMemoryBtn');
    const clearMemoryBtn = document.getElementById('clearMemoryBtn');
    const exportMemoryBtn = document.getElementById('exportMemoryBtn');
    const memoryStatus = document.getElementById('memoryStatus');
    const memoryDisplayArea = document.getElementById('memoryDisplayArea');
    const memoryStats = document.getElementById('memoryStats');
    const memoryMessages = document.getElementById('memoryMessages');

    // 事件处理函数
    executeBtn.addEventListener('click', function() {
        const query = queryInput.value.trim();
        if (!query) {
            alert('请输入请求内容');
            return;
        }

        executeBtn.disabled = true;
        executeBtn.innerHTML = '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> 执行中...';
        statusBadge.innerHTML = '<span class="badge bg-primary">处理中</span>';

        // 重置UI
        resetUI();

        // 显示加载指示器
        loadingIndicator.style.display = 'block';

        // 发送请求
        fetch('/api/streaming-events/execute', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({ query: query })
        })
        .then(response => response.json())
        .then(data => {
            currentPlanId = data.planId;
            planIdDisplay.textContent = data.planId;

            // 连接到事件流
            connectToEventStream(data.planId);
        })
        .catch(error => {
            console.error('Error:', error);
            executeBtn.disabled = false;
            executeBtn.innerHTML = '执行计划';
            statusBadge.innerHTML = '<span class="badge bg-danger">错误</span>';
            loadingIndicator.style.display = 'none';
            alert('执行请求失败，请查看控制台获取详细信息');
        });
    });

    executeTemplateBtn.addEventListener('click', function() {
        const templateId = templateIdInput.value.trim();
        if (!templateId) {
            alert('请输入计划模板ID');
            return;
        }

        executeTemplateBtn.disabled = true;
        executeTemplateBtn.innerHTML = '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> 执行中...';
        statusBadge.innerHTML = '<span class="badge bg-primary">处理中</span>';

        // 重置UI
        resetUI();

        // 显示加载指示器
        loadingIndicator.style.display = 'block';

        // 构建URL
        let url = `/api/streaming-events/template/${templateId}`;
        const params = paramsInput.value.trim();
        const chatId = chatIdInput.value.trim();

        // 构建查询参数
        const queryParams = new URLSearchParams();
        if (params) {
            // 解析params并添加到queryParams
            const paramPairs = params.split('&');
            paramPairs.forEach(pair => {
                const [key, value] = pair.split('=');
                if (key && value) {
                    queryParams.append(key, value);
                }
            });
        }
        if (chatId) {
            queryParams.append('chatId', chatId);
        }

        // 添加用户消息参数（连续对话时使用）
        const userMessage = messageInput.value.trim();
        if (userMessage) {
            queryParams.append('message', userMessage);
        }

        if (queryParams.toString()) {
            url += `?${queryParams.toString()}`;
        }

        // 连接到事件流
        connectToEventStream(url);
    });

    clearEventsBtn.addEventListener('click', function() {
        eventContainer.innerHTML = '';
    });

    generateChatIdBtn.addEventListener('click', function() {
        // 生成新的会话ID (简化格式)
        const newChatId = 'chat-' + Date.now() + '-' + Math.random().toString(36).substr(2, 6);
        chatIdInput.value = newChatId;
        console.log('生成新会话ID:', newChatId);
    });

    // 记忆查看相关事件监听器
    useCurrentPlanIdBtn.addEventListener('click', function() {
        if (currentPlanId) {
            memoryConversationId.value = currentPlanId;
            memoryStatus.textContent = '已使用当前计划ID';
            memoryStatus.className = 'text-success';
        } else {
            memoryStatus.textContent = '当前没有活动的计划ID';
            memoryStatus.className = 'text-warning';
        }
    });

    viewMemoryBtn.addEventListener('click', function() {
        viewMemory();
    });

    refreshMemoryBtn.addEventListener('click', function() {
        if (memoryDisplayArea.style.display !== 'none') {
            viewMemory();
        }
    });

    clearMemoryBtn.addEventListener('click', function() {
        clearMemory();
    });

    exportMemoryBtn.addEventListener('click', function() {
        exportMemory();
    });

    // 连接到事件流
    function connectToEventStream(planIdOrUrl) {
        // 关闭现有连接
        if (eventSource) {
            eventSource.close();
        }

        // 确定URL
        let url = typeof planIdOrUrl === 'string' && planIdOrUrl.startsWith('/')
            ? planIdOrUrl
            : `/api/streaming-events/${planIdOrUrl}`;

        // 创建新的EventSource连接
        let retryCount = 0;
        const MAX_RETRIES = 3;
        const RETRY_DELAY = 2000; // 2秒

        function createEventSource() {
            if (retryCount >= MAX_RETRIES) {
                statusBadge.innerHTML = '<span class="badge bg-danger">连接失败</span>';
                executeBtn.disabled = false;
                executeBtn.innerHTML = '执行计划';
                executeTemplateBtn.disabled = false;
                executeTemplateBtn.innerHTML = '执行模板';
                loadingIndicator.style.display = 'none';
                alert('连接失败，请稍后重试');
                return;
            }

            if (retryCount > 0) {
                console.log(`尝试重新连接 (第${retryCount}次)...`);
            }

            eventSource = new EventSource(url);

            // 连接打开时
            eventSource.onopen = function() {
                console.log('连接已建立');
                loadingIndicator.style.display = 'none';
                planInfoContainer.style.display = 'block';
            };

            // 接收消息时
            eventSource.onmessage = function(event) {
                try {
                    const eventData = JSON.parse(event.data);
                    processEvent(eventData);
                } catch (e) {
                    console.error('解析事件数据失败:', e);
                    addEventToLog('解析错误', 'error', event.data);
                }
            };

            // 错误处理
            eventSource.onerror = function(error) {
                console.error('EventSource错误:', error);

                // 检查连接状态
                if (eventSource.readyState === EventSource.CLOSED) {
                    console.log('连接已关闭');
                    return; // 如果连接已经关闭，不需要重连
                }

                // 关闭连接
                try {
                    eventSource.close();
                    eventSource = null;
                } catch (e) {
                    console.error('关闭连接时出错:', e);
                }

                // 尝试重连
                retryCount++;
                if (retryCount <= MAX_RETRIES) {
                    console.log(`连接出错，${RETRY_DELAY/1000}秒后尝试重连... (第${retryCount}次)`);
                    statusBadge.innerHTML = `<span class="badge bg-warning">重连中 (${retryCount}/${MAX_RETRIES})</span>`;
                    setTimeout(createEventSource, RETRY_DELAY);
                    return;
                }

                // 超过重试次数，处理最终错误
                console.log('超过最大重试次数，放弃重连');
                executeBtn.disabled = false;
                executeBtn.innerHTML = '执行计划';
                executeTemplateBtn.disabled = false;
                executeTemplateBtn.innerHTML = '执行模板';
                statusBadge.innerHTML = '<span class="badge bg-danger">出错</span>';
                loadingIndicator.style.display = 'none';

                // 显示错误信息
                let errorMessage = '连接失败，请检查网络或服务器状态';
                console.error(errorMessage);

                // 在UI上显示错误信息
                if (summaryContainer) {
                    summaryContainer.style.display = 'block';
                    summaryText.innerHTML = '<div class="alert alert-danger">错误: ' + errorMessage + '</div>';
                }
            };
        }

        // 初始化连接
        createEventSource();
    }

    // 处理接收到的事件
    function processEvent(event) {
        try {
            // 打印事件类型和负载的前50个字符
            const payloadPreview = typeof event.payload === 'string'
                ? event.payload.substring(0, 50) + '...'
                : JSON.stringify(event.payload).substring(0, 50) + '...';
            console.log(`收到事件: ${event.type}, 负载预览: ${payloadPreview}`);

            // 根据事件类型更新UI和事件日志
            switch (event.type) {
                case 'PLAN_SNAPSHOT':
                    // 只更新UI，不显示在事件日志中
                    updatePlanSnapshot(event.payload);
                    break;

                case 'THINKING':
                    // 处理完整思考事件 - 只用于后端存储，前端不显示
                    try {
                        let content = '';
                        let agentId = 'default';
                        let agentName = 'AI助手';

                        if (typeof event.payload === 'object') {
                            const thinkingData = event.payload;
                            agentId = thinkingData.agentId || 'default';
                            agentName = thinkingData.agentName || 'AI助手';
                            content = thinkingData.content || '';
                        } else if (typeof event.payload === 'string') {
                            try {
                                const thinkingData = JSON.parse(event.payload);
                                agentId = thinkingData.agentId || 'default';
                                agentName = thinkingData.agentName || 'AI助手';
                                content = thinkingData.content || event.payload;
                            } catch (e) {
                                // 如果不是JSON，则直接使用原始内容
                                content = event.payload;
                            }
                        }

                        // 只记录完整思考内容，不显示在前端
                        if (content && content.trim() !== '') {
                            console.log(`收到完整思考事件: "${content}"，仅用于后端存储，前端不显示`);

                            // 将内容添加到已处理集合中，避免重复处理
                            processedThinkingContents.add(content);

                            // 记录当前思考内容
                            lastThinkingContent = content;
                        }
                    } catch (e) {
                        console.error('处理思考事件失败:', e);
                    }
                    break;

                case 'THINK_OUTPUT_CHUNK':
                    // 处理思考输出块事件
                    try {
                        let content = '';
                        let agentId = 'default';

                        // 如果是心跳或连接成功事件，忽略
                        if (event.payload === '' || event.payload === '连接成功') {
                            break;
                        }

                        if (typeof event.payload === 'string') {
                            content = event.payload;
                        } else {
                            // 如果不是字符串，忽略
                            break;
                        }

                        // 如果内容为空，忽略
                        if (!content || content.trim() === '') {
                            break;
                        }

                        console.log(`收到思考块: "${content}"`);

                        // 如果上一个事件是工具调用结束，则创建新的思考块
                        if (lastEventType === 'TOOL_END') {
                            completeThinkingBlock(); // 完成之前的思考块
                        }

                        // 更新或创建思考块
                        if (currentThinkingBlock && currentThinkingBlock.agentId === agentId) {
                            // 更新现有思考块，追加内容
                            currentThinkingBlock.content += content;
                            currentThinkingBlock.element.textContent = currentThinkingBlock.content;
                            console.log('追加内容到现有思考块');
                        } else {
                            // 创建新的思考块
                            addThinkingBlock(agentId, 'AI助手', content);
                            console.log('创建新的思考块');
                        }

                        // 滚动到底部
                        if (currentThinkingBlock) {
                            currentThinkingBlock.element.scrollIntoView({ behavior: 'smooth', block: 'end' });
                        }
                    } catch (e) {
                        console.error('处理思考输出块事件失败:', e);
                    }
                    break;

                case 'TOOL_START':
                    // 处理工具调用开始事件
                    try {
                        // 完成当前思考块
                        completeThinkingBlock();

                        const toolData = typeof event.payload === 'object' ? event.payload : JSON.parse(event.payload);
                        const agentId = toolData.agentId || 'default';
                        const agentName = toolData.agentName || 'AI助手';
                        const toolName = toolData.toolName || '未知工具';
                        const parameters = toolData.parameters || {};

                        // 添加到智能体工作流程
                        addToolCallBlock(agentId, agentName, toolName, parameters);
                    } catch (e) {
                        console.error('处理工具调用开始事件失败:', e);
                        // 如果解析失败，使用原始数据
                        addToolCallBlock('default', 'AI助手', '未知工具', event.payload);
                    }
                    break;

                case 'TOOL_END':
                    // 处理工具调用结束事件
                    try {
                        const toolData = typeof event.payload === 'object' ? event.payload : JSON.parse(event.payload);
                        const agentId = toolData.agentId || 'default';
                        const agentName = toolData.agentName || 'AI助手';
                        const toolName = toolData.toolName || '未知工具';
                        const result = toolData.result || {};

                        // 添加到智能体工作流程
                        addToolResultBlock(agentId, agentName, toolName, result);

                        // 完成当前思考块，为下一个思考块做准备
                        completeThinkingBlock();
                    } catch (e) {
                        console.error('处理工具调用结束事件失败:', e);
                        // 如果解析失败，使用原始数据
                        addToolResultBlock('default', 'AI助手', '未知工具', event.payload);
                    }
                    break;

                case 'AGENT_ERROR':
                    handleError(event.payload);
                    // 更新智能体状态
                    try {
                        const errorData = typeof event.payload === 'object' ? event.payload : JSON.parse(event.payload);
                        const agentId = errorData.agentId || 'default';
                        updateAgentStatus(agentId, 'error');
                    } catch (e) {
                        // 如果解析失败，使用默认智能体
                        updateAgentStatus('default', 'error');
                    }
                    break;

                case 'PLAN_COMPLETED':
                    console.log('收到计划完成事件，准备关闭连接');
                    handlePlanCompleted(event.payload);
                    // 更新所有智能体状态为已完成
                    for (const agentId in agentWorkflowMap) {
                        updateAgentStatus(agentId, 'completed');
                    }

                    // 延迟1秒后关闭连接，确保数据传输完成
                    if (eventSource) {
                        console.log('等待1秒后关闭事件源连接...');
                        setTimeout(() => {
                            console.log('计划已完成，关闭事件源连接');
                            eventSource.close();
                            eventSource = null;

                            // 重置按钮状态
                            executeBtn.disabled = false;
                            executeBtn.innerHTML = '执行计划';
                            executeTemplateBtn.disabled = false;
                            executeTemplateBtn.innerHTML = '执行模板';
                        }, 1000);
                    }
                    break;

                case 'AGENT_STATUS':
                    // 处理智能体状态更新事件
                    try {
                        const statusData = typeof event.payload === 'object' ? event.payload : JSON.parse(event.payload);
                        const agentId = statusData.agentId || 'default';
                        const status = statusData.status || 'running';
                        updateAgentStatus(agentId, status);
                    } catch (e) {
                        // 如果解析失败，忽略该事件
                        console.error('解析智能体状态事件失败:', e);
                    }
                    break;
            }

            // 记录当前事件类型，便于下一个事件判断
            console.log(`事件类型从 ${lastEventType} 变为 ${event.type}`);
            lastEventType = event.type;
        } catch (error) {
            console.error('处理事件时出错:', error);
        }
    }

    // 更新计划快照
    function updatePlanSnapshot(snapshotJson) {
        try {
            const response = JSON.parse(snapshotJson);
            console.log('更新计划快照:', response);

            // 处理新的数据格式：支持executions数组和直接的record格式
            let record;
            if (response.executions && response.executions.length > 0) {
                // 新格式：使用executions数组中的第一个执行记录
                record = response.executions[0];
                console.log('使用executions格式的数据:', record);
            } else if (response.planId || response.agentExecutionSequence) {
                // 旧格式：直接使用response作为record
                record = response;
                console.log('使用直接格式的数据:', record);
            } else {
                console.warn('无法识别的数据格式:', response);
                return;
            }

            // 更新基本信息
            planIdDisplay.textContent = record.planId || '-';
            titleDisplay.textContent = record.title || '-';
            userRequestDisplay.textContent = record.userRequest || '-';

            // 更新状态
            if (record.completed) {
                statusBadge.innerHTML = '<span class="badge bg-success">已完成</span>';
            } else {
                statusBadge.innerHTML = '<span class="badge bg-primary">执行中</span>';
            }

            // 更新步骤
            if (record.steps && record.steps.length > 0) {
                stepsContainer.style.display = 'block';
                stepsList.innerHTML = '';

                record.steps.forEach((step, index) => {
                    const stepElement = document.createElement('div');
                    stepElement.className = 'card step-card';

                    // 确定步骤状态
                    let statusBadge = '';
                    if (index < record.currentStepIndex) {
                        statusBadge = '<span class="badge bg-success">已完成</span>';
                    } else if (index === record.currentStepIndex) {
                        statusBadge = '<span class="badge bg-primary">执行中</span>';
                    } else {
                        statusBadge = '<span class="badge bg-secondary">等待中</span>';
                    }

                    stepElement.innerHTML = `
                        <div class="card-body">
                            <h6 class="card-title">步骤 ${index + 1} ${statusBadge}</h6>
                            <p class="card-text">${step}</p>
                        </div>
                    `;

                    stepsList.appendChild(stepElement);
                });
            }

            // 更新智能体执行记录
            if (record.agentExecutionSequence && record.agentExecutionSequence.length > 0) {
                agentsContainer.style.display = 'block';
                agentsList.innerHTML = '';

                // 限制显示的智能体数量
                const MAX_AGENTS_TO_DISPLAY = 10;
                const agentsToShow = record.agentExecutionSequence.slice(0, MAX_AGENTS_TO_DISPLAY);

                agentsToShow.forEach((agent, index) => {
                    const agentElement = document.createElement('div');
                    agentElement.className = 'card agent-card mb-3';

                    let thinkActStepsHtml = '';
                    if (agent.thinkActSteps && agent.thinkActSteps.length > 0) {
                        thinkActStepsHtml = '<div class="mt-2"><strong>思考-行动步骤:</strong><ul>';
                        agent.thinkActSteps.forEach(step => {
                            // 兼容不同的字段名
                            const thinkOutput = step.thinkOutput || step.thinkingOutput || '';
                            const actionOutput = step.actionResult || step.actionOutput || '';
                            thinkActStepsHtml += `<li>${step.status || ''}: ${thinkOutput}${actionOutput ? ' → ' + actionOutput : ''}</li>`;
                        });
                        thinkActStepsHtml += '</ul></div>';
                    }

                    agentElement.innerHTML = `
                        <div class="card-body">
                            <h6 class="card-title">${agent.agentName || '未命名智能体'} <span class="badge bg-info">${agent.status || '状态未知'}</span></h6>
                            <p class="card-text">${agent.agentDescription || '无描述'}</p>
                            ${thinkActStepsHtml}
                        </div>
                    `;

                    agentsList.appendChild(agentElement);
                });

                // 如果有更多智能体，显示提示信息
                if (record.agentExecutionSequence.length > MAX_AGENTS_TO_DISPLAY) {
                    const noteElement = document.createElement('div');
                    noteElement.className = 'alert alert-info mt-2';
                    noteElement.textContent = `注意: 仅显示前${MAX_AGENTS_TO_DISPLAY}个智能体，共${record.agentExecutionSequence.length}个。`;
                    agentsList.appendChild(noteElement);
                }
            }

            // 更新总结
            if (record.summary) {
                summaryContainer.style.display = 'block';
                summaryText.textContent = record.summary;
            }

        } catch (e) {
            console.error('解析计划快照失败:', e);
        }
    }

    // 更新思考输出
    function updateThinkingOutput(chunk) {
        if (chunk && chunk !== '') {
            // 将思考输出添加到当前智能体的工作流程中
            addThinkingBlock('default', 'AI助手', chunk);
        }
    }

    // 处理错误
    function handleError(errorData) {
        let errorMessage = '未知错误';

        if (typeof errorData === 'object') {
            errorMessage = errorData.message || JSON.stringify(errorData);
        } else if (typeof errorData === 'string') {
            errorMessage = errorData;
        }

        statusBadge.innerHTML = '<span class="badge bg-danger">错误</span>';
        executeBtn.disabled = false;
        executeBtn.innerHTML = '执行计划';
        executeTemplateBtn.disabled = false;
        executeTemplateBtn.innerHTML = '执行模板';

        // 显示错误信息
        summaryContainer.style.display = 'block';
        summaryText.innerHTML = `<div class="alert alert-danger">${errorMessage}</div>`;
    }

    // 处理计划完成
    function handlePlanCompleted(completionData) {
        console.log('处理计划完成事件:', completionData);
        let summary = '计划执行完成';

        if (typeof completionData === 'object' && completionData.summary) {
            summary = completionData.summary;
        } else if (typeof completionData === 'string') {
            summary = completionData;
        }

        // 更新UI状态
        statusBadge.innerHTML = '<span class="badge bg-success">已完成</span>';
        executeBtn.disabled = false;
        executeBtn.innerHTML = '执行计划';
        executeTemplateBtn.disabled = false;
        executeTemplateBtn.innerHTML = '执行模板';
        loadingIndicator.style.display = 'none';

        // 显示总结
        summaryContainer.style.display = 'block';
        summaryText.textContent = summary;
    }

    // 添加事件到日志
    function addEventToLog(eventType, eventClass, content) {
        const eventItem = document.createElement('div');
        eventItem.className = `event-item ${eventClass}`;

        const timestamp = new Date().toLocaleTimeString();

        // 根据事件类型创建不同的显示内容
        if (eventType === 'THINK_OUTPUT_CHUNK') {
            // 思考输出
            const thinkingEntry = document.createElement('div');
            thinkingEntry.className = 'thinking-entry';

            // 创建思考标题
            const thinkingTitle = document.createElement('div');
            thinkingTitle.className = 'thinking-title';

            // 添加折叠/展开图标
            const toggleIcon = document.createElement('span');
            toggleIcon.className = 'toggle-icon';
            toggleIcon.textContent = '▼';
            toggleIcon.onclick = function() {
                const content = this.parentNode.nextElementSibling;
                if (content.style.display === 'none') {
                    content.style.display = 'block';
                    this.textContent = '▼';
                } else {
                    content.style.display = 'none';
                    this.textContent = '►';
                }
            };

            thinkingTitle.appendChild(toggleIcon);
            thinkingTitle.appendChild(document.createTextNode('思考中...'));

            // 创建思考内容
            const thinkingContent = document.createElement('div');
            thinkingContent.className = 'thinking-content';
            thinkingContent.textContent = content;

            thinkingEntry.appendChild(thinkingTitle);
            thinkingEntry.appendChild(thinkingContent);

            eventItem.innerHTML = `<div class="event-timestamp">${timestamp}</div>`;
            eventItem.appendChild(thinkingEntry);
        } else if (eventType === 'TOOL_START') {
            // 工具调用开始
            try {
                const toolData = JSON.parse(content);
                const toolName = toolData.toolName || '未知工具';
                const toolParams = toolData.parameters || {};

                // 创建工具调用容器
                const toolCall = document.createElement('div');
                toolCall.className = 'tool-call';

                // 创建工具调用标题
                const toolTitle = document.createElement('div');
                toolTitle.className = 'thinking-title';

                // 添加折叠/展开图标
                const toggleIcon = document.createElement('span');
                toggleIcon.className = 'toggle-icon';
                toggleIcon.textContent = '▼';
                toggleIcon.onclick = function() {
                    const content = this.parentNode.nextElementSibling;
                    if (content.style.display === 'none') {
                        content.style.display = 'block';
                        this.textContent = '▼';
                    } else {
                        content.style.display = 'none';
                        this.textContent = '►';
                    }
                };

                toolTitle.appendChild(toggleIcon);
                toolTitle.appendChild(document.createTextNode(`开始调用工具: ${toolName}`));

                // 创建工具调用内容
                const toolContent = document.createElement('div');
                toolContent.className = 'thinking-content';
                toolContent.textContent = JSON.stringify(toolParams, null, 2);

                toolCall.appendChild(toolTitle);
                toolCall.appendChild(toolContent);

                eventItem.innerHTML = `<div class="event-timestamp">${timestamp}</div>`;
                eventItem.appendChild(toolCall);
            } catch (e) {
                eventItem.innerHTML = `
                    <div class="event-timestamp">${timestamp}</div>
                    <div class="event-type">${eventType}</div>
                    <div class="event-content">${content}</div>
                `;
            }
        } else if (eventType === 'TOOL_END') {
            // 工具调用结束
            try {
                const toolData = JSON.parse(content);
                const toolName = toolData.toolName || '未知工具';
                const toolResult = toolData.result || {};

                // 创建工具结果容器
                const toolResultDiv = document.createElement('div');
                toolResultDiv.className = 'tool-result';

                // 创建工具结果标题
                const resultTitle = document.createElement('div');
                resultTitle.className = 'thinking-title';

                // 添加折叠/展开图标
                const toggleIcon = document.createElement('span');
                toggleIcon.className = 'toggle-icon';
                toggleIcon.textContent = '▼';
                toggleIcon.onclick = function() {
                    const content = this.parentNode.nextElementSibling;
                    if (content.style.display === 'none') {
                        content.style.display = 'block';
                        this.textContent = '▼';
                    } else {
                        content.style.display = 'none';
                        this.textContent = '►';
                    }
                };

                resultTitle.appendChild(toggleIcon);
                resultTitle.appendChild(document.createTextNode(`工具调用完成: ${toolName}`));

                // 创建工具结果内容
                const resultContent = document.createElement('div');
                resultContent.className = 'thinking-content';
                resultContent.textContent = JSON.stringify(toolResult, null, 2);

                toolResultDiv.appendChild(resultTitle);
                toolResultDiv.appendChild(resultContent);

                eventItem.innerHTML = `<div class="event-timestamp">${timestamp}</div>`;
                eventItem.appendChild(toolResultDiv);
            } catch (e) {
                eventItem.innerHTML = `
                    <div class="event-timestamp">${timestamp}</div>
                    <div class="event-type">${eventType}</div>
                    <div class="event-content">${content}</div>
                `;
            }
        } else {
            // 其他类型的事件
            eventItem.innerHTML = `
                <div class="event-timestamp">${timestamp}</div>
                <div class="event-type">${eventType}</div>
                <div class="event-content">${content}</div>
            `;
        }

        eventContainer.appendChild(eventItem);
        // 自动滚动到底部
        eventContainer.scrollTop = eventContainer.scrollHeight;
    }

    // 获取事件类型对应的CSS类
    function getEventTypeClass(eventType) {
        switch (eventType) {
            case 'PLAN_SNAPSHOT': return 'plan-snapshot';
            case 'THINK_OUTPUT_CHUNK': return 'think-output';
            case 'THINKING': return 'think-output';
            case 'TOOL_START': return 'tool-call';
            case 'TOOL_END': return 'tool-result';
            case 'AGENT_ERROR': return 'error';
            case 'PLAN_COMPLETED': return 'completed';
            default: return '';
        }
    }

    // 创建或获取智能体工作流程项
    function getOrCreateAgentItem(agentId, agentName) {
        // 如果已经存在该智能体的工作流程项，直接返回
        if (agentWorkflowMap[agentId]) {
            return agentWorkflowMap[agentId];
        }

        // 创建新的智能体工作流程项
        const agentItem = document.createElement('div');
        agentItem.className = 'workflow-item';
        agentItem.id = `agent-${agentId}`;

        // 创建智能体头部
        const agentHeader = document.createElement('div');
        agentHeader.className = 'workflow-agent-header';

        // 智能体名称
        const agentNameElement = document.createElement('div');
        agentNameElement.className = 'workflow-agent-name';
        agentNameElement.textContent = agentName || `智能体 ${agentId}`;

        // 智能体状态
        const agentStatus = document.createElement('div');
        agentStatus.className = 'workflow-agent-status badge bg-primary';
        agentStatus.textContent = '执行中';
        agentStatus.id = `agent-status-${agentId}`;

        agentHeader.appendChild(agentNameElement);
        agentHeader.appendChild(agentStatus);

        // 创建工作流程内容容器
        const workflowContent = document.createElement('div');
        workflowContent.className = 'workflow-content';
        workflowContent.id = `agent-content-${agentId}`;

        agentItem.appendChild(agentHeader);
        agentItem.appendChild(workflowContent);

        // 添加到工作流程容器
        agentWorkflow.appendChild(agentItem);

        // 存储到映射中
        agentWorkflowMap[agentId] = {
            element: agentItem,
            contentElement: workflowContent,
            statusElement: agentStatus
        };

        return agentWorkflowMap[agentId];
    }

    // 添加思考块
    function addThinkingBlock(agentId, agentName, content, isFullReplacement = false) {
        console.log(`添加思考块: agentId=${agentId}, isFullReplacement=${isFullReplacement}, 内容长度=${content.length}`);
        console.log(`当前思考块状态: ${currentThinkingBlock ? '存在' : '不存在'}`);

        const agentItem = getOrCreateAgentItem(agentId, agentName);

        // 如果是完整替换模式，则创建新的思考块
        if (isFullReplacement || !currentThinkingBlock || currentThinkingBlock.agentId !== agentId) {
            console.log('创建新的思考块');
            // 创建新的思考块
            const thinkingBlock = document.createElement('div');
            thinkingBlock.className = 'workflow-thinking markdown-content';

            // 使用innerHTML以支持Markdown格式
            if (!window.marked) {
                // 如果没有marked库，先加载它
                const script = document.createElement('script');
                script.src = 'https://cdn.jsdelivr.net/npm/marked/marked.min.js';
                document.head.appendChild(script);
                script.onload = function() {
                    thinkingBlock.innerHTML = window.marked.parse(content);
                };
                // 临时显示原始内容
                thinkingBlock.textContent = content;
            } else {
                // 如果已加载marked库，直接使用
                thinkingBlock.innerHTML = window.marked.parse(content);
            }

            agentItem.contentElement.appendChild(thinkingBlock);

            // 更新当前思考块
            currentThinkingBlock = {
                element: thinkingBlock,
                agentId: agentId,
                content: content,
                markdownContent: true
            };
        } else {
            console.log('追加到现有思考块');
            // 如果已有思考块，则追加内容
            currentThinkingBlock.content += content;

            // 使用innerHTML以支持Markdown格式
            if (currentThinkingBlock.markdownContent && window.marked) {
                currentThinkingBlock.element.innerHTML = window.marked.parse(currentThinkingBlock.content);
            } else {
                currentThinkingBlock.element.textContent = currentThinkingBlock.content;
            }
        }

        // 滚动到底部
        currentThinkingBlock.element.scrollIntoView({ behavior: 'smooth', block: 'end' });
    }

    // 完成当前思考块
    function completeThinkingBlock() {
        if (currentThinkingBlock) {
            console.log(`完成思考块: agentId=${currentThinkingBlock.agentId}, 内容长度=${currentThinkingBlock.content.length}`);
            // 添加完成状态的类，停止动画
            currentThinkingBlock.element.classList.add('completed');
            currentThinkingBlock = null;
        } else {
            console.log('没有活跃的思考块需要完成');
        }
    }

    // 添加工具调用块
    function addToolCallBlock(agentId, agentName, toolName, parameters) {
        const agentItem = getOrCreateAgentItem(agentId, agentName);

        // 完成当前思考块
        completeThinkingBlock();

        // 创建工具调用块
        const toolCallBlock = document.createElement('div');
        toolCallBlock.className = 'workflow-tool-call';

        // 工具调用内容
        const toolContent = document.createElement('div');
        toolContent.className = 'workflow-tool-content';

        // 格式化工具名称和参数
        let formattedContent = `"${toolName || '未知工具'}" ...`;
        if (typeof parameters === 'object' && Object.keys(parameters).length > 0) {
            const paramPreview = JSON.stringify(parameters).slice(0, 50) +
                (JSON.stringify(parameters).length > 50 ? '...' : '');
            formattedContent = `"${toolName || '未知工具'}" ${paramPreview}`;
        }

        toolContent.textContent = formattedContent;

        toolCallBlock.appendChild(toolContent);
        agentItem.contentElement.appendChild(toolCallBlock);

        // 滚动到新添加的工具调用块
        toolCallBlock.scrollIntoView({ behavior: 'smooth', block: 'end' });

        // 滚动到底部
        toolCallBlock.scrollIntoView({ behavior: 'smooth', block: 'end' });

        return toolCallBlock;
    }

    // 添加工具结果块
    function addToolResultBlock(agentId, agentName, toolName, result) {
        const agentItem = getOrCreateAgentItem(agentId, agentName);

        // 创建工具结果块
        const toolResultBlock = document.createElement('div');
        toolResultBlock.className = 'workflow-tool-result';

        agentItem.contentElement.appendChild(toolResultBlock);

        // 滚动到底部
        toolResultBlock.scrollIntoView({ behavior: 'smooth', block: 'end' });

        return toolResultBlock;
    }

    // 更新智能体状态
    function updateAgentStatus(agentId, status) {
        if (agentWorkflowMap[agentId] && agentWorkflowMap[agentId].statusElement) {
            const statusElement = agentWorkflowMap[agentId].statusElement;

            // 清除现有的背景色类
            statusElement.classList.remove('bg-primary', 'bg-success', 'bg-danger', 'bg-warning');

            // 根据状态设置新的背景色和文本
            switch (status) {
                case 'completed':
                    statusElement.classList.add('bg-success');
                    statusElement.textContent = '已完成';
                    break;
                case 'error':
                    statusElement.classList.add('bg-danger');
                    statusElement.textContent = '出错';
                    break;
                case 'waiting':
                    statusElement.classList.add('bg-warning');
                    statusElement.textContent = '等待中';
                    break;
                default:
                    statusElement.classList.add('bg-primary');
                    statusElement.textContent = '执行中';
            }
        }
    }

    // 重置UI
    function resetUI() {
        thinkingText = '';
        summaryText.textContent = '';
        agentWorkflow.innerHTML = '';

        // 重置工作流程相关变量
        currentAgentId = null;
        currentAgentName = null;
        currentAgentItem = null;
        currentThinkingBlock = null;
        agentWorkflowMap = {};
        lastEventType = null;
        receivedThinkingEvents = {}; // 重置已收到的思考事件内容
        lastThinkingContent = ''; // 重置最后一次思考内容
        processedThinkingContents = new Set(); // 重置已处理的思考内容

        planInfoContainer.style.display = 'none';
        summaryContainer.style.display = 'none';
    }

    // ==================== 记忆查看功能 ====================

    // 查看记忆
    async function viewMemory() {
        const conversationId = memoryConversationId.value.trim();
        if (!conversationId) {
            memoryStatus.textContent = '请输入会话ID';
            memoryStatus.className = 'text-danger';
            return;
        }

        const type = memoryType.value;
        const limit = memoryLimit.value ? parseInt(memoryLimit.value) : null;

        // 显示加载状态
        memoryStatus.textContent = '正在加载记忆数据...';
        memoryStatus.className = 'text-info';
        viewMemoryBtn.disabled = true;
        viewMemoryBtn.innerHTML = '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> 加载中...';

        try {
            let url;
            if (type === 'summary') {
                url = `/api/streaming-events/memory/summary/${conversationId}`;
            } else {
                url = `/api/streaming-events/memory/${type}/${conversationId}`;
                if (limit) {
                    url += `?limit=${limit}`;
                }
            }

            const response = await fetch(url);
            const data = await response.json();

            if (response.ok) {
                displayMemoryData(data, type);
                memoryStatus.textContent = `记忆数据加载成功 (${new Date().toLocaleTimeString()})`;
                memoryStatus.className = 'text-success';
            } else {
                throw new Error(data.message || '加载记忆数据失败');
            }
        } catch (error) {
            console.error('加载记忆数据失败:', error);
            memoryStatus.textContent = '加载失败: ' + error.message;
            memoryStatus.className = 'text-danger';

            // 显示错误信息
            memoryDisplayArea.style.display = 'block';
            memoryStats.innerHTML = '<div class="memory-error">加载记忆数据失败: ' + error.message + '</div>';
            memoryMessages.innerHTML = '';
        } finally {
            viewMemoryBtn.disabled = false;
            viewMemoryBtn.innerHTML = '查看记忆';
        }
    }

    // 显示记忆数据
    function displayMemoryData(data, type) {
        memoryDisplayArea.style.display = 'block';

        if (type === 'summary') {
            // 显示摘要数据
            displayMemorySummary(data);
        } else {
            // 显示具体消息数据
            displayMemoryMessages(data);
        }
    }

    // 显示记忆摘要
    function displayMemorySummary(data) {
        const statsHtml = `
            <div class="memory-stats-item">
                <span class="memory-stats-label">会话ID:</span>
                <span class="memory-stats-value">${data.conversationId}</span>
            </div>
            <div class="memory-stats-item">
                <span class="memory-stats-label">Agent消息数:</span>
                <span class="memory-stats-value">${data.agentMemory.messageCount}</span>
            </div>
            <div class="memory-stats-item">
                <span class="memory-stats-label">对话消息数:</span>
                <span class="memory-stats-value">${data.conversationMemory.messageCount}</span>
            </div>
            <div class="memory-stats-item">
                <span class="memory-stats-label">总消息数:</span>
                <span class="memory-stats-value">${data.totalMessages}</span>
            </div>
        `;
        memoryStats.innerHTML = statsHtml;

        const messagesHtml = `
            <div class="mb-3">
                <h6>Agent记忆摘要:</h6>
                <pre class="memory-message-content">${data.agentMemory.summary}</pre>
            </div>
            <div>
                <h6>对话记忆摘要:</h6>
                <pre class="memory-message-content">${data.conversationMemory.summary}</pre>
            </div>
        `;
        memoryMessages.innerHTML = messagesHtml;
    }

    // 显示记忆消息
    function displayMemoryMessages(data) {
        const statsHtml = `
            <div class="memory-stats-item">
                <span class="memory-stats-label">会话ID:</span>
                <span class="memory-stats-value">${data.conversationId}</span>
            </div>
            <div class="memory-stats-item">
                <span class="memory-stats-label">消息数量:</span>
                <span class="memory-stats-value">${data.messageCount}</span>
            </div>
            <div class="memory-stats-item">
                <span class="memory-stats-label">更新时间:</span>
                <span class="memory-stats-value">${new Date(data.timestamp).toLocaleString()}</span>
            </div>
        `;
        memoryStats.innerHTML = statsHtml;

        if (data.messages && data.messages.length > 0) {
            const messagesHtml = data.messages.map(message => createMemoryMessageHtml(message)).join('');
            memoryMessages.innerHTML = messagesHtml;
        } else {
            memoryMessages.innerHTML = '<div class="memory-empty">暂无消息记录</div>';
        }
    }

    // 创建消息HTML
    function createMemoryMessageHtml(message) {
        const messageType = message.type.toLowerCase();
        let typeClass = '';
        let typeName = '';

        switch (messageType) {
            case 'usermessage':
                typeClass = 'user-message';
                typeName = '用户消息';
                break;
            case 'assistantmessage':
                typeClass = 'assistant-message';
                typeName = '助手消息';
                break;
            case 'toolresponsemessage':
                typeClass = 'tool-response-message';
                typeName = '工具响应';
                break;
            default:
                typeClass = '';
                typeName = message.type;
        }

        let toolCallsHtml = '';
        if (message.toolCalls && message.toolCalls.length > 0) {
            const toolCallsItems = message.toolCalls.map(toolCall =>
                `<div class="memory-tool-call-item">🔧 ${toolCall.name}(${toolCall.arguments})</div>`
            ).join('');
            toolCallsHtml = `<div class="memory-tool-calls">${toolCallsItems}</div>`;
        }

        let responsesHtml = '';
        if (message.responses && message.responses.length > 0) {
            const responsesItems = message.responses.map(response =>
                `<div class="memory-tool-call-item">📋 ${response.name}: ${response.responseData.substring(0, 100)}${response.responseData.length > 100 ? '...' : ''}</div>`
            ).join('');
            responsesHtml = `<div class="memory-tool-calls">${responsesItems}</div>`;
        }

        return `
            <div class="memory-message-item ${typeClass}">
                <div class="memory-message-header">
                    <span class="memory-message-type ${messageType.replace('message', '')}">${typeName}</span>
                </div>
                <div class="memory-message-content">${message.text || '(无文本内容)'}</div>
                ${toolCallsHtml}
                ${responsesHtml}
            </div>
        `;
    }

    // 清理记忆
    async function clearMemory() {
        const conversationId = memoryConversationId.value.trim();
        if (!conversationId) {
            memoryStatus.textContent = '请输入会话ID';
            memoryStatus.className = 'text-danger';
            return;
        }

        if (!confirm(`确定要清理会话 "${conversationId}" 的记忆吗？此操作不可撤销。`)) {
            return;
        }

        // 显示加载状态
        memoryStatus.textContent = '正在清理记忆...';
        memoryStatus.className = 'text-info';
        clearMemoryBtn.disabled = true;
        clearMemoryBtn.innerHTML = '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> 清理中...';

        try {
            const response = await fetch(`/api/streaming-events/memory/${conversationId}?type=all`, {
                method: 'DELETE'
            });
            const data = await response.json();

            if (response.ok) {
                memoryStatus.textContent = '记忆清理成功';
                memoryStatus.className = 'text-success';

                // 如果当前显示的是这个会话的记忆，清空显示
                if (memoryDisplayArea.style.display !== 'none') {
                    memoryDisplayArea.style.display = 'none';
                }
            } else {
                throw new Error(data.message || '清理记忆失败');
            }
        } catch (error) {
            console.error('清理记忆失败:', error);
            memoryStatus.textContent = '清理失败: ' + error.message;
            memoryStatus.className = 'text-danger';
        } finally {
            clearMemoryBtn.disabled = false;
            clearMemoryBtn.innerHTML = '清理记忆';
        }
    }

    // 导出记忆
    async function exportMemory() {
        const conversationId = memoryConversationId.value.trim();
        if (!conversationId) {
            memoryStatus.textContent = '请输入会话ID';
            memoryStatus.className = 'text-danger';
            return;
        }

        try {
            // 获取摘要数据
            const summaryResponse = await fetch(`/api/streaming-events/memory/summary/${conversationId}`);
            const summaryData = await summaryResponse.json();

            if (!summaryResponse.ok) {
                throw new Error(summaryData.message || '获取摘要数据失败');
            }

            // 获取Agent消息
            const agentResponse = await fetch(`/api/streaming-events/memory/agent/${conversationId}`);
            const agentData = await agentResponse.json();

            // 获取对话消息
            const conversationResponse = await fetch(`/api/streaming-events/memory/conversation/${conversationId}`);
            const conversationData = await conversationResponse.json();

            // 构建导出数据
            const exportData = {
                conversationId: conversationId,
                exportTime: new Date().toISOString(),
                summary: summaryData,
                agentMemory: agentData,
                conversationMemory: conversationData
            };

            // 创建下载链接
            const blob = new Blob([JSON.stringify(exportData, null, 2)], { type: 'application/json' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `memory-${conversationId}-${new Date().toISOString().slice(0, 19).replace(/:/g, '-')}.json`;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            URL.revokeObjectURL(url);

            memoryStatus.textContent = '记忆导出成功';
            memoryStatus.className = 'text-success';
        } catch (error) {
            console.error('导出记忆失败:', error);
            memoryStatus.textContent = '导出失败: ' + error.message;
            memoryStatus.className = 'text-danger';
        }
    }
</script>
</body>
</html>
