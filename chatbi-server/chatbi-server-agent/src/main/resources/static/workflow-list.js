// 智能体编排列表管理
let allWorkflows = [];
let filteredWorkflows = [];

// DOM元素
let workflowGrid, searchInput, statusFilter, refreshBtn;

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    console.log('智能体编排列表初始化...');

    try {
        initializeElements();
        setupEventListeners();
        loadWorkflowList();
    } catch (error) {
        console.error('页面初始化失败:', error);
    }
});

function initializeElements() {
    workflowGrid = document.getElementById('workflowGrid');
    searchInput = document.getElementById('searchInput');
    statusFilter = document.getElementById('statusFilter');
    refreshBtn = document.getElementById('refreshBtn');

    console.log('DOM元素初始化完成');
}

function setupEventListeners() {
    // 搜索功能
    searchInput.addEventListener('input', function() {
        const keyword = this.value.trim().toLowerCase();
        filterWorkflows(keyword, statusFilter.value);
    });

    // 状态筛选
    statusFilter.addEventListener('change', function() {
        const keyword = searchInput.value.trim().toLowerCase();
        filterWorkflows(keyword, this.value);
    });

    // 刷新按钮
    refreshBtn.addEventListener('click', loadWorkflowList);

    console.log('事件监听器设置完成');
}

/**
 * 处理R类统一响应格式
 * @param {Response} response - 响应对象
 * @returns {Promise<any>} - 处理后的数据
 */
async function handleRResponse(response) {
    if (!response.ok) {
        throw new Error(`API请求失败: ${response.status}`);
    }

    const result = await response.json();

    // 检查R类响应格式
    if (result.code && result.code !== 1) {
        throw new Error(result.message || 'API请求失败');
    }

    // 返回data字段，如果没有data字段则返回整个结果（兼容旧格式）
    return result.data !== undefined ? result.data : result;
}

async function loadWorkflowList() {
    try {
        showLoading();
        const response = await fetch('/api/plan-template/list');
        const data = await handleRResponse(response);

        allWorkflows = data.templates || [];

        console.log('编排列表加载完成:', { count: allWorkflows.length });

        // 处理数据，添加状态和统计信息
        allWorkflows = allWorkflows.map(workflow => ({
            ...workflow,
            status: 'active', // 默认状态，可以根据实际情况调整
            agentCount: extractAgentCount(workflow.description),
            stepCount: extractStepCount(workflow.description)
        }));

        filteredWorkflows = [...allWorkflows];
        renderWorkflowList();

    } catch (error) {
        console.error('加载编排列表失败:', error);
        showError('加载编排列表失败: ' + error.message);
    }
}

function filterWorkflows(keyword, status) {
    filteredWorkflows = allWorkflows.filter(workflow => {
        const matchesKeyword = !keyword ||
            workflow.title.toLowerCase().includes(keyword) ||
            (workflow.description && workflow.description.toLowerCase().includes(keyword));

        const matchesStatus = !status || workflow.status === status;

        return matchesKeyword && matchesStatus;
    });

    renderWorkflowList();
}

function renderWorkflowList() {
    if (filteredWorkflows.length === 0) {
        showEmptyState();
        return;
    }

    const workflowsHtml = filteredWorkflows.map(workflow => `
        <div class="workflow-card" onclick="openWorkflow('${workflow.id}')">
            <div class="card-header">
                <h3 class="card-title">${escapeHtml(workflow.title || '未命名编排')}</h3>
                <div class="card-meta">
                    <span><i class="bi bi-calendar"></i> ${formatDate(workflow.createTime)}</span>
                    <span><i class="bi bi-clock"></i> ${formatDate(workflow.updateTime)}</span>
                </div>
            </div>
            <div class="card-content">
                <p class="card-description">${escapeHtml(workflow.description || '暂无描述')}</p>
                <div class="card-stats">
                    <div class="stat-item">
                        <i class="bi bi-robot"></i>
                        <span>${workflow.agentCount || 0} 个智能体</span>
                    </div>
                    <div class="stat-item">
                        <i class="bi bi-list-ol"></i>
                        <span>${workflow.stepCount || 0} 个步骤</span>
                    </div>
                </div>
                <div class="card-actions">
                    <div class="action-buttons">
                        <button class="btn btn-ghost btn-sm" onclick="event.stopPropagation(); editWorkflow('${workflow.id}')" title="编辑">
                            <i class="bi bi-pencil"></i>
                        </button>
                        <button class="btn btn-ghost btn-sm" onclick="event.stopPropagation(); duplicateWorkflow('${workflow.id}')" title="复制">
                            <i class="bi bi-copy"></i>
                        </button>
                        <button class="btn btn-ghost btn-sm" onclick="event.stopPropagation(); deleteWorkflow('${workflow.id}')" title="删除">
                            <i class="bi bi-trash"></i>
                        </button>
                    </div>
                    <div class="status-badge ${workflow.status === 'active' ? 'status-active' : 'status-draft'}">
                        <i class="bi bi-${workflow.status === 'active' ? 'check-circle' : 'clock'}"></i>
                        <span>${workflow.status === 'active' ? '已激活' : '草稿'}</span>
                    </div>
                </div>
            </div>
        </div>
    `).join('');

    workflowGrid.innerHTML = workflowsHtml;
}

function showLoading() {
    workflowGrid.innerHTML = `
        <div class="loading-state">
            <div class="loading-spinner"></div>
            <p>加载编排列表中...</p>
        </div>
    `;
}

function showEmptyState() {
    const hasFilters = searchInput.value.trim() || statusFilter.value;

    workflowGrid.innerHTML = `
        <div class="empty-state">
            <div class="empty-icon">
                <i class="bi bi-${hasFilters ? 'search' : 'diagram-3'}"></i>
            </div>
            <h3 class="empty-title">${hasFilters ? '未找到匹配的编排' : '还没有创建编排'}</h3>
            <p class="empty-description">
                ${hasFilters ?
                    '尝试调整搜索条件或筛选器' :
                    '开始创建您的第一个智能体编排，构建强大的自动化工作流'
                }
            </p>
            ${!hasFilters ? `
                <a href="workflow-designer.html" class="btn btn-primary">
                    <i class="bi bi-plus"></i>
                    <span>创建第一个编排</span>
                </a>
            ` : ''}
        </div>
    `;
}

function showError(message) {
    workflowGrid.innerHTML = `
        <div class="empty-state">
            <div class="empty-icon">
                <i class="bi bi-exclamation-triangle"></i>
            </div>
            <h3 class="empty-title">加载失败</h3>
            <p class="empty-description">${escapeHtml(message)}</p>
            <button class="btn btn-primary" onclick="loadWorkflowList()">
                <i class="bi bi-arrow-clockwise"></i>
                <span>重试</span>
            </button>
        </div>
    `;
}

function openWorkflow(workflowId) {
    // 跳转到编排执行页面
    window.location.href = `hierarchical-chat.html?planId=${workflowId}`;
}

function editWorkflow(workflowId) {
    // 跳转到编排编辑页面
    window.location.href = `workflow-designer.html?id=${workflowId}&mode=edit`;
}

function duplicateWorkflow(workflowId) {
    // 跳转到编排复制页面
    window.location.href = `workflow-designer.html?id=${workflowId}&mode=duplicate`;
}

async function deleteWorkflow(workflowId) {
    if (!confirm('确定要删除这个智能体编排吗？此操作不可撤销。')) {
        return;
    }

    try {
        const response = await fetch(`/api/plan-template/delete`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({ planId: workflowId })
        });

        await handleRResponse(response);

        showSuccessMessage('编排删除成功');
        loadWorkflowList(); // 重新加载列表

    } catch (error) {
        console.error('删除编排失败:', error);
        alert('删除失败: ' + error.message);
    }
}

// 工具函数
function formatDate(dateString) {
    if (!dateString) return '未知';

    try {
        const date = new Date(dateString);
        const now = new Date();
        const diffMs = now - date;
        const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));

        if (diffDays === 0) {
            return '今天';
        } else if (diffDays === 1) {
            return '昨天';
        } else if (diffDays < 7) {
            return `${diffDays} 天前`;
        } else {
            return date.toLocaleDateString('zh-CN');
        }
    } catch (error) {
        return '未知';
    }
}

function extractAgentCount(description) {
    // 从描述中提取智能体数量，这里是简单的估算
    if (!description) return 0;
    const matches = description.match(/\[([^\]]+)\]/g);
    return matches ? new Set(matches).size : 0;
}

function extractStepCount(description) {
    // 从描述中提取步骤数量，这里是简单的估算
    if (!description) return 0;
    const matches = description.match(/\[([^\]]+)\]/g);
    return matches ? matches.length : 0;
}

function escapeHtml(text) {
    if (!text) return '';
    const div = document.createElement('div');
    div.textContent = text;
    return div.innerHTML;
}

function showSuccessMessage(message) {
    const toast = document.createElement('div');
    toast.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        z-index: 9999;
        background: var(--success);
        color: white;
        padding: 16px 20px;
        border-radius: var(--radius);
        box-shadow: var(--shadow-lg);
        display: flex;
        align-items: center;
        gap: 8px;
        font-weight: 500;
    `;
    toast.innerHTML = `<i class="bi bi-check-circle"></i> ${escapeHtml(message)}`;
    document.body.appendChild(toast);

    setTimeout(() => {
        toast.remove();
    }, 3000);
}
