<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ChatBI - 三栏布局演示</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
            background: #f9fafb;
            color: #1f2937;
            height: 100vh;
            overflow: hidden;
        }

        .app-container {
            display: flex;
            flex-direction: column;
            height: 100vh;
            background: white;
        }

        .main-panels {
            display: flex;
            flex: 1;
            overflow: hidden;
        }

        /* 左侧历史对话面板 */
        .conversation-panel {
            width: 320px;
            background: white;
            border-right: 1px solid #e5e7eb;
            display: flex;
            flex-direction: column;
        }

        /* 中间Agent执行面板 */
        .agent-panel {
            flex: 1;
            background: white;
            border-right: 1px solid #e5e7eb;
            display: flex;
            flex-direction: column;
        }

        /* 右侧结果展示面板 */
        .content-panel {
            width: 400px;
            background: white;
            display: flex;
            flex-direction: column;
        }

        .panel-header {
            padding: 16px 20px;
            border-bottom: 1px solid #e5e7eb;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .panel-title {
            font-size: 16px;
            font-weight: 600;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .panel-title i {
            color: #2563eb;
            font-size: 18px;
        }

        .action-button {
            background: transparent;
            border: 1px solid #d1d5db;
            color: #6b7280;
            padding: 6px 12px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 12px;
            display: flex;
            align-items: center;
            gap: 4px;
            transition: all 0.2s ease;
        }

        .action-button:hover {
            background: #f9fafb;
            border-color: #2563eb;
            color: #2563eb;
        }

        .action-button.primary {
            background: #2563eb;
            color: white;
            border-color: #2563eb;
        }

        .panel-content {
            flex: 1;
            overflow-y: auto;
            padding: 0;
            background: #f9fafb;
        }

        /* 历史对话样式 */
        .history-item {
            padding: 8px 12px;
            margin: 4px 8px;
            border-radius: 6px;
            cursor: pointer;
            transition: all 0.2s ease;
            border: 1px solid transparent;
            font-size: 13px;
        }

        .history-item:hover {
            background: #f3f4f6;
            border-color: #e5e7eb;
        }

        .history-item.active {
            background: #2563eb;
            color: white;
            border-color: #2563eb;
        }

        .history-item-title {
            font-weight: 500;
            margin-bottom: 4px;
            line-height: 1.3;
        }

        .history-item-time {
            font-size: 11px;
            opacity: 0.8;
        }

        /* Agent步骤样式 */
        .agent-step {
            background: white;
            border: 1px solid #e5e7eb;
            border-radius: 8px;
            margin: 12px 20px;
            overflow: hidden;
            transition: box-shadow 0.2s;
        }

        .agent-step:hover {
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
        }

        .agent-step-header {
            padding: 12px 16px;
            background: #f9fafb;
            border-bottom: 1px solid #e5e7eb;
            display: flex;
            align-items: center;
            gap: 12px;
            cursor: pointer;
        }

        .step-number {
            width: 24px;
            height: 24px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            font-weight: 600;
            color: white;
            background: #10b981;
        }

        .step-title {
            flex: 1;
            font-size: 14px;
            font-weight: 500;
        }

        .step-status {
            font-size: 12px;
            color: #10b981;
        }

        .agent-step-content {
            padding: 16px;
            background: white;
        }

        .tool-execution {
            background: #f9fafb;
            border: 1px solid #e5e7eb;
            border-radius: 6px;
            margin-top: 12px;
            overflow: hidden;
        }

        .tool-header {
            padding: 8px 12px;
            background: #f3f4f6;
            border-bottom: 1px solid #e5e7eb;
            display: flex;
            align-items: center;
            gap: 8px;
            font-size: 12px;
            font-weight: 500;
        }

        .tool-icon {
            width: 16px;
            height: 16px;
            border-radius: 3px;
            background: #2563eb;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 10px;
        }

        .tool-content {
            padding: 12px;
            font-family: 'SF Mono', Monaco, monospace;
            font-size: 11px;
            line-height: 1.4;
            color: #374151;
        }

        /* 结果展示样式 */
        .result-container {
            background: white;
            border: 1px solid #e5e7eb;
            border-radius: 8px;
            margin: 12px 20px;
            overflow: hidden;
        }

        .result-header {
            padding: 12px 16px;
            background: #f9fafb;
            border-bottom: 1px solid #e5e7eb;
            font-size: 14px;
            font-weight: 500;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .result-icon {
            width: 20px;
            height: 20px;
            border-radius: 4px;
            background: #2563eb;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
        }

        .result-content {
            padding: 16px;
        }

        .data-table {
            width: 100%;
            border-collapse: collapse;
            font-size: 13px;
        }

        .data-table th {
            background: #f9fafb;
            padding: 8px 12px;
            text-align: left;
            font-weight: 500;
            border-bottom: 1px solid #e5e7eb;
        }

        .data-table td {
            padding: 8px 12px;
            border-bottom: 1px solid #f3f4f6;
        }

        .data-table tbody tr:hover {
            background: #f9fafb;
        }

        /* 输入面板 */
        .input-panel {
            background: white;
            border-top: 1px solid #e5e7eb;
            padding: 16px 20px;
        }

        .input-container {
            display: flex;
            gap: 12px;
            align-items: flex-end;
        }

        .message-input-wrapper {
            flex: 1;
        }

        .message-input {
            width: 100%;
            min-height: 40px;
            padding: 10px 16px;
            border: 1px solid #d1d5db;
            border-radius: 8px;
            resize: none;
            font-family: inherit;
            font-size: 14px;
            transition: border-color 0.2s;
        }

        .message-input:focus {
            outline: none;
            border-color: #2563eb;
            box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
        }

        .send-button {
            background: #2563eb;
            color: white;
            border: none;
            padding: 10px 16px;
            border-radius: 8px;
            cursor: pointer;
            display: flex;
            align-items: center;
            gap: 6px;
            font-weight: 500;
            font-size: 14px;
            transition: background 0.2s;
        }

        .send-button:hover {
            background: #1d4ed8;
        }
    </style>
</head>
<body>
    <div class="app-container">
        <div class="main-panels">
            <!-- 左侧历史对话面板 -->
            <div class="conversation-panel">
                <div class="panel-header">
                    <div class="panel-title">
                        <i class="bi bi-clock-history"></i>
                        <span>历史对话</span>
                    </div>
                    <button class="action-button primary">
                        <i class="bi bi-plus"></i>
                        新对话
                    </button>
                </div>
                <div class="panel-content">
                    <div class="history-item active">
                        <div class="history-item-title">查询销售数据分析</div>
                        <div class="history-item-time">2024-01-15 14:30</div>
                    </div>
                    <div class="history-item">
                        <div class="history-item-title">用户行为统计报告</div>
                        <div class="history-item-time">2024-01-15 10:15</div>
                    </div>
                    <div class="history-item">
                        <div class="history-item-title">产品销量对比分析</div>
                        <div class="history-item-time">2024-01-14 16:45</div>
                    </div>
                </div>
            </div>

            <!-- 中间Agent执行面板 -->
            <div class="agent-panel">
                <div class="panel-header">
                    <div class="panel-title">
                        <i class="bi bi-robot"></i>
                        <span>Agent 执行</span>
                    </div>
                    <button class="action-button">
                        <i class="bi bi-trash"></i>
                        清空
                    </button>
                </div>
                <div class="panel-content">
                    <div class="agent-step">
                        <div class="agent-step-header">
                            <div class="step-number">1</div>
                            <div class="step-title">SQL 查询生成</div>
                            <div class="step-status">已完成</div>
                            <i class="bi bi-chevron-right"></i>
                        </div>
                        <div class="agent-step-content">
                            <div style="font-size: 13px; color: #6b7280; margin-bottom: 12px;">
                                分析用户需求并生成相应的 SQL 查询语句
                            </div>
                            <div class="tool-execution">
                                <div class="tool-header">
                                    <div class="tool-icon">
                                        <i class="bi bi-gear"></i>
                                    </div>
                                    <div>sql_query</div>
                                    <div style="margin-left: auto; color: #10b981;">完成</div>
                                </div>
                                <div class="tool-content">
                                    参数: {"sql": "SELECT region, SUM(sales) FROM sales_data GROUP BY region"}
                                    <div style="background: #f0f9ff; border-left: 3px solid #2563eb; padding: 8px; margin-top: 8px; border-radius: 0 4px 4px 0;">
                                        执行成功，返回 3 行数据
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="agent-step">
                        <div class="agent-step-header">
                            <div class="step-number">2</div>
                            <div class="step-title">数据分析处理</div>
                            <div class="step-status">已完成</div>
                            <i class="bi bi-chevron-right"></i>
                        </div>
                        <div class="agent-step-content">
                            <div style="font-size: 13px; color: #6b7280;">
                                对查询结果进行分析和格式化处理
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 右侧结果展示面板 -->
            <div class="content-panel">
                <div class="panel-header">
                    <div class="panel-title">
                        <i class="bi bi-display"></i>
                        <span>执行结果</span>
                    </div>
                    <button class="action-button">
                        <i class="bi bi-bug"></i>
                        调试
                    </button>
                </div>
                <div class="panel-content">
                    <div class="result-container">
                        <div class="result-header">
                            <div class="result-icon">
                                <i class="bi bi-table"></i>
                            </div>
                            查询结果
                        </div>
                        <div class="result-content">
                            <table class="data-table">
                                <thead>
                                    <tr>
                                        <th>地区</th>
                                        <th>销售额</th>
                                        <th>增长率</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td>华东</td>
                                        <td>¥1,250,000</td>
                                        <td>+12.5%</td>
                                    </tr>
                                    <tr>
                                        <td>华南</td>
                                        <td>¥980,000</td>
                                        <td>+8.3%</td>
                                    </tr>
                                    <tr>
                                        <td>华北</td>
                                        <td>¥750,000</td>
                                        <td>+5.7%</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 底部输入面板 -->
        <div class="input-panel">
            <div class="input-container">
                <div class="message-input-wrapper">
                    <textarea class="message-input" placeholder="输入您的数据分析需求..." rows="1"></textarea>
                </div>
                <button class="send-button">
                    <i class="bi bi-send"></i>
                    发送
                </button>
            </div>
        </div>
    </div>
</body>
</html>
