<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>系统概览 - JTaskPilot 多智能体框架</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.0/font/bootstrap-icons.css" rel="stylesheet">
    <style>
        :root {
            --primary: #667eea;
            --primary-hover: #5a6fd8;
            --secondary: #764ba2;
            --accent: #f093fb;
            --success: #4ade80;
            --warning: #fbbf24;
            --danger: #f87171;
            --gray-50: #f9fafb;
            --gray-100: #f3f4f6;
            --gray-200: #e5e7eb;
            --gray-300: #d1d5db;
            --gray-400: #9ca3af;
            --gray-500: #6b7280;
            --gray-600: #4b5563;
            --gray-700: #374151;
            --gray-800: #1f2937;
            --gray-900: #111827;
            --white: #ffffff;
        }

        body {
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            min-height: 100vh;
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        }

        .hero-section {
            background: linear-gradient(135deg, var(--primary) 0%, var(--secondary) 100%);
            color: white;
            padding: 4rem 0;
            position: relative;
            overflow: hidden;
        }

        .hero-section::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="20" height="20" patternUnits="userSpaceOnUse"><path d="M 20 0 L 0 0 0 20" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="1"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
            pointer-events: none;
        }

        .hero-content {
            position: relative;
            z-index: 1;
        }

        .hero-title {
            font-size: 3.5rem;
            font-weight: 800;
            margin-bottom: 1.5rem;
            text-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
        }

        .hero-subtitle {
            font-size: 1.25rem;
            opacity: 0.9;
            margin-bottom: 2rem;
            line-height: 1.6;
        }

        .feature-card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 24px;
            padding: 2rem;
            margin-bottom: 2rem;
            transition: all 0.3s ease;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        }

        .feature-card:hover {
            transform: translateY(-8px);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
        }

        .feature-icon {
            width: 64px;
            height: 64px;
            background: linear-gradient(135deg, var(--primary) 0%, var(--secondary) 100%);
            border-radius: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.5rem;
            color: white;
            margin-bottom: 1.5rem;
            box-shadow: 0 8px 16px rgba(102, 126, 234, 0.3);
        }

        .feature-title {
            font-size: 1.25rem;
            font-weight: 700;
            color: var(--gray-900);
            margin-bottom: 1rem;
        }

        .feature-description {
            color: var(--gray-600);
            line-height: 1.6;
            margin-bottom: 1.5rem;
        }

        .feature-link {
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            color: var(--primary);
            text-decoration: none;
            font-weight: 600;
            transition: all 0.3s ease;
        }

        .feature-link:hover {
            color: var(--primary-hover);
            transform: translateX(4px);
        }

        .stats-section {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-radius: 24px;
            padding: 3rem;
            margin: 3rem 0;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
        }

        .stat-item {
            text-align: center;
        }

        .stat-number {
            font-size: 2.5rem;
            font-weight: 800;
            background: linear-gradient(135deg, var(--primary) 0%, var(--secondary) 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            margin-bottom: 0.5rem;
        }

        .stat-label {
            color: var(--gray-600);
            font-weight: 500;
            text-transform: uppercase;
            letter-spacing: 0.05em;
            font-size: 0.875rem;
        }

        .quick-actions {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-radius: 24px;
            padding: 2rem;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
        }

        .action-btn {
            background: linear-gradient(135deg, var(--primary) 0%, var(--secondary) 100%);
            color: white;
            border: none;
            border-radius: 16px;
            padding: 1rem 2rem;
            font-weight: 600;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 0.75rem;
            transition: all 0.3s ease;
            box-shadow: 0 8px 16px rgba(102, 126, 234, 0.3);
            margin: 0.5rem;
        }

        .action-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 12px 24px rgba(102, 126, 234, 0.4);
            color: white;
        }

        .section-title {
            font-size: 2rem;
            font-weight: 700;
            color: var(--gray-900);
            margin-bottom: 3rem;
            text-align: center;
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .animate-fade-in {
            animation: fadeInUp 0.6s ease-out;
        }

        .animate-delay-1 { animation-delay: 0.1s; }
        .animate-delay-2 { animation-delay: 0.2s; }
        .animate-delay-3 { animation-delay: 0.3s; }
        .animate-delay-4 { animation-delay: 0.4s; }
    </style>
</head>
<body>
    <!-- 英雄区域 -->
    <section class="hero-section">
        <div class="container">
            <div class="hero-content text-center">
                <h1 class="hero-title animate-fade-in">
                    <i class="bi bi-robot me-3"></i>
                    JTaskPilot
                </h1>
                <p class="hero-subtitle animate-fade-in animate-delay-1">
                    基于 Spring AI 的多智能体推理执行框架<br>
                    支持复杂工作流编排、智能对话和自动化任务执行
                </p>
                <div class="animate-fade-in animate-delay-2">
                    <a href="main-dashboard.html" class="action-btn me-3">
                        <i class="bi bi-play-circle"></i>
                        开始使用
                    </a>
                    <a href="/swagger-ui/index.html" target="_blank" class="action-btn" style="background: rgba(255,255,255,0.2); backdrop-filter: blur(10px);">
                        <i class="bi bi-code-square"></i>
                        API 文档
                    </a>
                </div>
            </div>
        </div>
    </section>

    <!-- 系统统计 -->
    <div class="container">
        <div class="stats-section animate-fade-in animate-delay-3">
            <div class="row" id="systemStats">
                <div class="col-md-3">
                    <div class="stat-item">
                        <div class="stat-number" id="agentCount">-</div>
                        <div class="stat-label">智能体数量</div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="stat-item">
                        <div class="stat-number" id="workflowCount">-</div>
                        <div class="stat-label">编排模板</div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="stat-item">
                        <div class="stat-number" id="toolCount">-</div>
                        <div class="stat-label">可用工具</div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="stat-item">
                        <div class="stat-number" id="executionCount">-</div>
                        <div class="stat-label">执行记录</div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 核心功能 -->
    <div class="container">
        <h2 class="section-title animate-fade-in animate-delay-4">核心功能模块</h2>

        <div class="row">
            <div class="col-lg-4 col-md-6">
                <div class="feature-card animate-fade-in">
                    <div class="feature-icon">
                        <i class="bi bi-chat-dots"></i>
                    </div>
                    <h3 class="feature-title">智能对话</h3>
                    <p class="feature-description">
                        支持多轮对话、上下文记忆和流式响应的智能对话系统，提供自然的人机交互体验。
                    </p>
                    <a href="main-dashboard.html" class="feature-link">
                        开始对话 <i class="bi bi-arrow-right"></i>
                    </a>
                </div>
            </div>

            <div class="col-lg-4 col-md-6">
                <div class="feature-card animate-fade-in animate-delay-1">
                    <div class="feature-icon">
                        <i class="bi bi-diagram-3"></i>
                    </div>
                    <h3 class="feature-title">智能体编排</h3>
                    <p class="feature-description">
                        可视化的智能体工作流编排器，支持拖拽式设计、步骤排序和复杂业务逻辑编排。
                    </p>
                    <a href="main-dashboard.html" class="feature-link">
                        创建编排 <i class="bi bi-arrow-right"></i>
                    </a>
                </div>
            </div>

            <div class="col-lg-4 col-md-6">
                <div class="feature-card animate-fade-in animate-delay-2">
                    <div class="feature-icon">
                        <i class="bi bi-gear"></i>
                    </div>
                    <h3 class="feature-title">智能体配置</h3>
                    <p class="feature-description">
                        灵活的智能体配置管理，支持角色定义、工具绑定和参数调优，满足不同场景需求。
                    </p>
                    <a href="main-dashboard.html" class="feature-link">
                        配置智能体 <i class="bi bi-arrow-right"></i>
                    </a>
                </div>
            </div>

            <div class="col-lg-4 col-md-6">
                <div class="feature-card animate-fade-in animate-delay-3">
                    <div class="feature-icon">
                        <i class="bi bi-tools"></i>
                    </div>
                    <h3 class="feature-title">工具管理</h3>
                    <p class="feature-description">
                        支持 MCP 协议的工具管理系统，提供丰富的内置工具和自定义工具扩展能力。
                    </p>
                    <a href="main-dashboard.html" class="feature-link">
                        管理工具 <i class="bi bi-arrow-right"></i>
                    </a>
                </div>
            </div>

            <div class="col-lg-4 col-md-6">
                <div class="feature-card animate-fade-in animate-delay-4">
                    <div class="feature-icon">
                        <i class="bi bi-chat-square-text"></i>
                    </div>
                    <h3 class="feature-title">对话历史</h3>
                    <p class="feature-description">
                        完整的对话历史记录和检索系统，支持按时间、主题和智能体类型进行分类查看。
                    </p>
                    <a href="main-dashboard.html" class="feature-link">
                        查看历史 <i class="bi bi-arrow-right"></i>
                    </a>
                </div>
            </div>

            <div class="col-lg-4 col-md-6">
                <div class="feature-card animate-fade-in animate-delay-5">
                    <div class="feature-icon">
                        <i class="bi bi-sliders"></i>
                    </div>
                    <h3 class="feature-title">系统配置</h3>
                    <p class="feature-description">
                        全面的系统配置管理，包括模型参数、API 配置和系统行为定制选项。
                    </p>
                    <a href="main-dashboard.html" class="feature-link">
                        系统设置 <i class="bi bi-arrow-right"></i>
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- 快速操作 -->
    <div class="container mb-5">
        <div class="quick-actions text-center">
            <h3 class="mb-4">快速开始</h3>
            <a href="main-dashboard.html" class="action-btn">
                <i class="bi bi-rocket"></i>
                进入主控制台
            </a>
            <a href="workflow-designer.html" class="action-btn">
                <i class="bi bi-plus-circle"></i>
                创建新编排
            </a>
            <a href="agent-config.html" class="action-btn">
                <i class="bi bi-person-plus"></i>
                添加智能体
            </a>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // 加载系统统计数据
        async function loadSystemStats() {
            try {
                // 加载智能体数量
                const agentsResponse = await fetch('/api/agents');
                if (agentsResponse.ok) {
                    const agentsData = await agentsResponse.json();
                    document.getElementById('agentCount').textContent = agentsData.length || 0;
                }

                // 加载编排模板数量
                const workflowsResponse = await fetch('/api/plan-template/list');
                if (workflowsResponse.ok) {
                    const workflowsData = await workflowsResponse.json();
                    document.getElementById('workflowCount').textContent = workflowsData.count || 0;
                }

                // 加载工具数量
                const toolsResponse = await fetch('/api/mcp/tools');
                if (toolsResponse.ok) {
                    const result = await toolsResponse.json();
                    // 处理R类响应格式
                    const toolsData = result.code === 1 ? result.data : result;
                    document.getElementById('toolCount').textContent = (toolsData && toolsData.length) || 0;
                }

                // 模拟执行记录数量（可以根据实际API调整）
                document.getElementById('executionCount').textContent = Math.floor(Math.random() * 100) + 50;

            } catch (error) {
                console.error('加载系统统计失败:', error);
                // 设置默认值
                document.getElementById('agentCount').textContent = '0';
                document.getElementById('workflowCount').textContent = '0';
                document.getElementById('toolCount').textContent = '0';
                document.getElementById('executionCount').textContent = '0';
            }
        }

        // 页面加载完成后执行
        document.addEventListener('DOMContentLoaded', function() {
            console.log('JTaskPilot 系统概览页面已加载');
            loadSystemStats();
        });
    </script>
</body>
</html>
