#server:
#  port: 18080
#
## Swagger 配置
#springdoc:
#  swagger-ui:
#    path: /swagger-ui.html
#    tags-sorter: alpha
#    operations-sorter: alpha
#  api-docs:
#    path: /v3/api-docs
#  group-configs:
#    - group: 'default'
#      paths-to-match: '/**'
#      packages-to-scan: com.cpit.chatbi.server.agent
#
#spring:
#  application:
#    name: agent
#  # 启用流式响应支持
#  mvc:
#    async:
#      request-timeout: 600000  # 10分钟超时
#  # 国际化配置
#  messages:
#    basename: i18n/messages
#    encoding: UTF-8
#    fallbackToSystemLocale: true
#  ai:
#    openai:
#      base-url: https://dashscope.aliyuncs.com/compatible-mode
#      api-key: sk-adf45b9e1376495a894ed242650af233
#      chat:
#        options:
#          model: qwen-plus-latest  # 使用更快的模型
##          model: qwen3-32b  # 使用更快的模型
##          model: qwen-turbo-latest  # 较大的模型，响应较慢
#          temperature: 0.1  # 降低温度以获得更快的响应
#          max-tokens: 4000  # 限制最大token数量
#      embedding:
#        enabled: true
#        options:
#          model: text-embedding-v2
#    # 禁用 Ollama 自动配置
#    ollama:
#      chat:
#        enabled: false
#      embedding:
#        enabled: false
#
#    vectorstore:
#      pgvector:
#        index-type: HNSW
#        distance-type: COSINE_DISTANCE
#        dimensions: 1536
#        table-name: vector_store
#        initialize-schema: false
#        batching-strategy: TOKEN_COUNT
#        max-document-batch-size: 10000
#    mcp:
#      client:
#        enabled: false
#
#
#  datasource:
#    driver-class-name: org.postgresql.Driver
#    #    url: ****************************************************************** # 数据库URL
#    url: *************************************************************** # 数据库URL
#    username: postgres
#    password: 123456
#    druid:
#      initial-size: 5
#      min-idle: 5
#      max-active: 20
#      max-wait: 60000
#      time-between-eviction-runs-millis: 60000
#      min-evictable-idle-time-millis: 300000
#      validation-query: SELECT 1
#      test-while-idle: true
#      test-on-borrow: false
#      test-on-return: false
#      pool-prepared-statements: true
#      max-pool-prepared-statement-per-connection-size: 20
#      filters: stat,wall
#      connection-properties: druid.stat.mergeSql=true;druid.stat.slowSqlMillis=5000
#
#  jpa:
#    database-platform: org.hibernate.dialect.PostgreSQLDialect
#    hibernate:
#      ddl-auto: validate  # 改为update，让JPA自动创建表
#    show-sql: true  # 开启SQL日志，便于调试
#    properties:
#      hibernate:
#        format_sql: true
#
#  # Flyway数据库迁移配置
#  flyway:
#    enabled: false  # 禁用Flyway（如果不需要数据库版本管理）
#    # 如果需要启用Flyway，请使用以下配置：
#    # enabled: true
#    # baseline-on-migrate: true  # 在现有数据库上启用baseline
#    # baseline-version: 1.0.0    # baseline版本号
#    # baseline-description: "Initial baseline"
#    # locations: classpath:db/migration  # 迁移脚本位置
#    # validate-on-migrate: false  # 跳过迁移验证（开发环境）
#
#logging:
#  file:
#    name: ./logs/info.log
#  level:
#    root: INFO
#    # 减少网络相关的调试日志以提高性能
#    "reactor.netty.http.client": WARN
#    "org.springframework.web.reactive.function.client": WARN
#    # 保留应用相关的调试日志
#    "com.alibaba.cloud.ai.example.manus": DEBUG
#    # 抑制客户端断开连接的错误日志
#    "org.apache.catalina.core.ContainerBase.[Catalina].[localhost].[/].[dispatcherServlet]": WARN
#    "org.springframework.web.servlet.mvc.method.annotation.ExceptionHandlerExceptionResolver": WARN
#    # 抑制Tomcat的连接重置错误
#    "org.apache.coyote.http11.Http11Processor": WARN
#    "org.apache.catalina.connector.CoyoteAdapter": WARN
#    "org.apache.catalina.connector.ClientAbortException": OFF
#    "org.apache.coyote": WARN
#    "org.apache.tomcat.util.net": WARN