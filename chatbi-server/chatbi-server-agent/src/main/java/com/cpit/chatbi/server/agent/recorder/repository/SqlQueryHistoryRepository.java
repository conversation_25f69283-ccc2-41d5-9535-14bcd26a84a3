package com.cpit.chatbi.server.agent.recorder.repository;

import com.cpit.chatbi.server.agent.recorder.entity.SqlQueryHistory;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

/**
 * SQL查询历史记录数据访问接口
 */
@Repository
public interface SqlQueryHistoryRepository extends JpaRepository<SqlQueryHistory, Long> {

    Page<SqlQueryHistory> findByChatIdOrderByCreatedAtDesc(String chatId, Pageable pageable);

    Page<SqlQueryHistory> findByUserIdOrderByCreatedAtDesc(String userId, Pageable pageable);

}
