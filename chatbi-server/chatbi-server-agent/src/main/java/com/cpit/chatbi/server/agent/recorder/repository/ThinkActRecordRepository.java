package com.cpit.chatbi.server.agent.recorder.repository;

import com.cpit.chatbi.server.agent.recorder.entity.ThinkActRecordEntity;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 思考行动记录数据访问接口
 */
@Repository
public interface ThinkActRecordRepository extends JpaRepository<ThinkActRecordEntity, Long> {

	/**
	 * 根据Agent执行ID查找思考行动记录
	 * @param agentExecutionId Agent执行ID
	 * @return 思考行动记录列表
	 */
	List<ThinkActRecordEntity> findByAgentExecutionRecordIdOrderByCreatedAt(Long agentExecutionId);

	/**
	 * 根据工具名称查找记录
	 * @param toolName 工具名称
	 * @param pageable 分页参数
	 * @return 分页结果
	 */
	Page<ThinkActRecordEntity> findByToolName(String toolName, Pageable pageable);

	/**
	 * 根据状态查找记录
	 * @param status 状态
	 * @param pageable 分页参数
	 * @return 分页结果
	 */
	Page<ThinkActRecordEntity> findByStatus(String status, Pageable pageable);

	/**
	 * 查找需要执行动作的记录
	 * @param pageable 分页参数
	 * @return 分页结果
	 */
	Page<ThinkActRecordEntity> findByActionNeededTrue(Pageable pageable);

	/**
	 * 查找有错误的记录
	 * @param pageable 分页参数
	 * @return 分页结果
	 */
	Page<ThinkActRecordEntity> findByErrorMessageIsNotNull(Pageable pageable);

	/**
	 * 查找指定时间范围内的记录
	 * @param startTime 开始时间
	 * @param endTime 结束时间
	 * @return 记录列表
	 */
	List<ThinkActRecordEntity> findByCreatedAtBetween(LocalDateTime startTime, LocalDateTime endTime);

	/**
	 * 统计工具使用情况
	 * @param days 天数
	 * @return 工具使用统计
	 */
	@Query(value = """
			SELECT
			    tool_name,
			    COUNT(*) as usage_count,
			    COUNT(CASE WHEN status = 'SUCCESS' THEN 1 END) as success_count,
			    COUNT(CASE WHEN status = 'ERROR' THEN 1 END) as error_count,
			    AVG(CASE WHEN act_end_time IS NOT NULL AND act_start_time IS NOT NULL
			        THEN EXTRACT(EPOCH FROM (act_end_time - act_start_time)) END) as avg_execution_time
			FROM think_act_records
			WHERE tool_name IS NOT NULL
			AND created_at >= :since
			GROUP BY tool_name
			ORDER BY usage_count DESC
			""", nativeQuery = true)
	List<Object[]> getToolUsageStats(@Param("since") LocalDateTime since);

	/**
	 * 获取思考时间统计
	 * @param days 天数
	 * @return 思考时间统计
	 */
	@Query(value = """
			SELECT
			    AVG(CASE WHEN think_end_time IS NOT NULL AND think_start_time IS NOT NULL
			        THEN EXTRACT(EPOCH FROM (think_end_time - think_start_time)) END) as avg_think_time,
			    MAX(CASE WHEN think_end_time IS NOT NULL AND think_start_time IS NOT NULL
			        THEN EXTRACT(EPOCH FROM (think_end_time - think_start_time)) END) as max_think_time,
			    MIN(CASE WHEN think_end_time IS NOT NULL AND think_start_time IS NOT NULL
			        THEN EXTRACT(EPOCH FROM (think_end_time - think_start_time)) END) as min_think_time
			FROM think_act_records
			WHERE think_start_time IS NOT NULL
			AND think_end_time IS NOT NULL
			AND created_at >= :since
			""", nativeQuery = true)
	Object[] getThinkTimeStats(@Param("since") LocalDateTime since);

	/**
	 * 获取动作执行时间统计
	 * @param days 天数
	 * @return 动作执行时间统计
	 */
	@Query(value = """
			SELECT
			    AVG(CASE WHEN act_end_time IS NOT NULL AND act_start_time IS NOT NULL
			        THEN EXTRACT(EPOCH FROM (act_end_time - act_start_time)) END) as avg_action_time,
			    MAX(CASE WHEN act_end_time IS NOT NULL AND act_start_time IS NOT NULL
			        THEN EXTRACT(EPOCH FROM (act_end_time - act_start_time)) END) as max_action_time,
			    MIN(CASE WHEN act_end_time IS NOT NULL AND act_start_time IS NOT NULL
			        THEN EXTRACT(EPOCH FROM (act_end_time - act_start_time)) END) as min_action_time
			FROM think_act_records
			WHERE act_start_time IS NOT NULL
			AND act_end_time IS NOT NULL
			AND action_needed = true
			AND created_at >= :since
			""", nativeQuery = true)
	Object[] getActionTimeStats(@Param("since") LocalDateTime since);

	/**
	 * 查找执行时间最长的思考记录
	 * @param pageable 分页参数
	 * @return 记录列表
	 */
	@Query(value = """
			SELECT t FROM ThinkActRecordEntity t
			WHERE t.thinkEndTime IS NOT NULL AND t.thinkStartTime IS NOT NULL
			ORDER BY (EXTRACT(EPOCH FROM t.thinkEndTime) - EXTRACT(EPOCH FROM t.thinkStartTime)) DESC
			""")
	List<ThinkActRecordEntity> findLongestThinkingRecords(Pageable pageable);

	/**
	 * 查找执行时间最长的动作记录
	 * @param pageable 分页参数
	 * @return 记录列表
	 */
	@Query(value = """
			SELECT t FROM ThinkActRecordEntity t
			WHERE t.actEndTime IS NOT NULL AND t.actStartTime IS NOT NULL
			ORDER BY (EXTRACT(EPOCH FROM t.actEndTime) - EXTRACT(EPOCH FROM t.actStartTime)) DESC
			""")
	List<ThinkActRecordEntity> findLongestActionRecords(Pageable pageable);

	/**
	 * 统计特定工具的成功率
	 * @param toolName 工具名称
	 * @param days 天数
	 * @return 成功率统计 [总数, 成功数]
	 */
	@Query(value = """
			SELECT
			    COUNT(*) as total_count,
			    COUNT(CASE WHEN status = 'SUCCESS' THEN 1 END) as success_count
			FROM think_act_records
			WHERE tool_name = :toolName
			AND created_at >= :since
			""", nativeQuery = true)
	Object[] getToolSuccessStats(@Param("toolName") String toolName, @Param("since") LocalDateTime since);

	/**
	 * 查找最常见的错误
	 * @param days 天数
	 * @return 错误统计
	 */
	@Query(value = """
			SELECT
			    error_message,
			    COUNT(*) as error_count
			FROM think_act_records
			WHERE error_message IS NOT NULL
			AND created_at >= :since
			GROUP BY error_message
			ORDER BY error_count DESC
			LIMIT 10
			""", nativeQuery = true)
	List<Object[]> getMostCommonErrors(@Param("since") LocalDateTime since);

	/**
	 * 统计动作需求比例
	 * @param days 天数
	 * @return 动作需求统计 [总数, 需要动作的数量]
	 */
	@Query(value = """
			SELECT
			    COUNT(*) as total_count,
			    COUNT(CASE WHEN action_needed = true THEN 1 END) as action_needed_count
			FROM think_act_records
			WHERE created_at >= :since
			""", nativeQuery = true)
	Object[] getActionNeedStats(@Param("since") LocalDateTime since);

}
