package com.cpit.chatbi.server.agent.recorder.service;

import cn.hutool.core.util.ObjUtil;
import com.cpit.chatbi.server.agent.recorder.entity.ChatSession;
import com.cpit.chatbi.server.agent.recorder.repository.ChatSessionRepository;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 聊天会话服务（简化版） 负责管理聊天会话的创建、查询等操作
 */
@Service
public class ChatSessionService {

	private static final Logger log = LoggerFactory.getLogger(ChatSessionService.class);

	@Autowired
	private ChatSessionRepository chatSessionRepository;

	/**
	 * 创建或获取聊天会话
	 * @param chatId 会话ID
	 * @param userId 用户ID
	 * @param chatName 会话名称
	 * @param planTemplateId 计划模板ID（仅在创建新会话时使用）
	 * @return 聊天会话
	 */
	@Transactional
	public ChatSession createOrGetChatSession(String chatId, String userId, String chatName, String planTemplateId) {
		return createOrGetChatSession(chatId, userId, chatName, planTemplateId, null, null);
	}

	/**
	 * 创建或获取聊天会话（支持模型ID和工具上下文）
	 * @param chatId 会话ID
	 * @param userId 用户ID
	 * @param chatName 会话名称
	 * @param planTemplateId 计划模板ID（仅在创建新会话时使用）
	 * @param modelId 模型ID（仅在创建新会话时使用）
	 * @param toolContext 工具上下文（仅在创建新会话时使用）
	 * @return 聊天会话
	 */
	@Transactional
	public ChatSession createOrGetChatSession(String chatId, String userId, String chatName, String planTemplateId,
			String modelId, String toolContext) {
		try {
			log.debug("创建或获取聊天会话: chatId={}, userId={}, modelId={}, toolContext={}",
					chatId, userId, modelId, toolContext != null ? "有工具上下文" : "无工具上下文");

			Optional<ChatSession> existingSession = chatSessionRepository.findById(chatId);
			if (existingSession.isPresent()) {
				// 会话已存在，更新模型ID和工具上下文（如果提供了新值）
				ChatSession session = existingSession.get();
				boolean updated = false;

				// 更新模型ID（如果提供了新值）
				if (modelId != null && !modelId.trim().isEmpty() && !modelId.equals(session.getModelId())) {
					session.setModelId(modelId);
					updated = true;
					log.debug("更新会话模型ID: chatId={}, oldModelId={}, newModelId={}",
							chatId, session.getModelId(), modelId);
				}

				// 更新工具上下文（如果提供了新值）
				if (toolContext != null && !toolContext.trim().isEmpty() && !toolContext.equals(session.getToolContext())) {
					session.setToolContext(toolContext);
					updated = true;
					log.debug("更新会话工具上下文: chatId={}", chatId);
				}

				// 如果有更新，保存到数据库
				if (updated) {
					session = chatSessionRepository.save(session);
					log.info("更新现有会话: chatId={}, planTemplateId={}, modelId={}",
							chatId, session.getPlanTemplateId(), session.getModelId());
				} else {
					log.debug("会话已存在，无需更新: chatId={}, planTemplateId={}", chatId, session.getPlanTemplateId());
				}

				return session;
			}
			else {
				// 创建新会话
				ChatSession newSession = new ChatSession(chatId, userId, chatName);
				newSession.setPlanTemplateId(planTemplateId);
				newSession.setModelId(modelId);
				newSession.setToolContext(toolContext);

				ChatSession savedSession = chatSessionRepository.save(newSession);
				log.info("创建新会话: chatId={}, userId={}, planTemplateId={}, modelId={}, hasToolContext={}",
						chatId, userId, planTemplateId, modelId, toolContext != null && !toolContext.trim().isEmpty());
				return savedSession;
			}
		}
		catch (Exception e) {
			log.error("创建或获取聊天会话失败: chatId={}, userId={}", chatId, userId, e);
			throw new RuntimeException("创建或获取聊天会话失败", e);
		}
	}

	/**
	 * 获取用户的会话列表（按时间分组）
	 * @param userId 用户ID
	 * @param planTemplateId 模板ID
	 * @param page 页码
	 * @param size 每页大小
	 * @return 分组的会话列表
	 */
	public Map<String, List<ChatSession>> getUserSessionsGroupedByDate(String userId, String keyword, String planTemplateId, int page, int size) {
		try {
			log.debug("获取用户会话列表: userId={}, page={}, size={}", userId, page, size);

			Pageable pageable = PageRequest.of(page, size);
			Page<ChatSession> sessionsPage = chatSessionRepository.searchSessions(userId, keyword, planTemplateId, pageable);

			// 按日期分组
			Map<String, List<ChatSession>> groupedSessions = sessionsPage.getContent()
				.stream()
				.collect(Collectors.groupingBy(session -> {
					LocalDateTime createdTime = session.getCreatedAt();

					LocalDateTime now = LocalDateTime.now();
					LocalDateTime today = now.toLocalDate().atStartOfDay();
					LocalDateTime yesterday = today.minusDays(1);
					LocalDateTime weekAgo = today.minusDays(7);

					if (createdTime.isAfter(today)) {
						return "今天";
					}
					else if (createdTime.isAfter(yesterday)) {
						return "昨天";
					}
					else if (createdTime.isAfter(weekAgo)) {
						return "本周";
					}
					else {
						return createdTime.toLocalDate().toString();
					}
				}, LinkedHashMap::new, Collectors.toList()));

			log.debug("用户会话分组完成: userId={}, 分组数={}", userId, groupedSessions.size());
			return groupedSessions;
		}
		catch (Exception e) {
			log.error("获取用户会话列表失败: userId={}", userId, e);
			return new LinkedHashMap<>();
		}
	}

	/**
	 * 获取会话信息
	 * @param chatId 会话ID
	 * @return 会话信息
	 */
	public Optional<ChatSession> getChatSession(String chatId) {
		try {
			return chatSessionRepository.findById(chatId);
		}
		catch (Exception e) {
			log.error("获取会话信息失败: chatId={}", chatId, e);
			return Optional.empty();
		}
	}

	/**
	 * 检查会话是否存在
	 * @param chatId 会话ID
	 * @return 是否存在
	 */
	public boolean existsChatSession(String chatId) {
		try {
			return chatSessionRepository.existsById(chatId);
		}
		catch (Exception e) {
			log.error("检查会话存在性失败: chatId={}", chatId, e);
			return false;
		}
	}

	/**
	 * 删除会话（软删除）
	 * @param chatId 会话ID
	 * @return 是否删除成功
	 */
	@Transactional
	public boolean deleteChatSession(String chatId) {
		try {
			log.debug("删除会话: chatId={}", chatId);
			chatSessionRepository.deleteById(chatSessionRepository.findByChatId(chatId).get().getId());
			return true;
		}
		catch (Exception e) {
			log.error("删除会话失败: chatId={}", chatId, e);
			return false;
		}
	}

	/**
	 * 搜索用户的会话
	 * @param userId 用户ID
	 * @param keyword 关键词
	 * @param page 页码
	 * @param size 每页大小
	 * @return 搜索结果
	 */
	public Page<ChatSession> searchUserSessions(String userId, String keyword, int page, int size) {
		try {
			log.debug("搜索用户会话: userId={}, keyword={}", userId, keyword);

			Pageable pageable = PageRequest.of(page, size);
			return chatSessionRepository.searchSessions(userId, keyword, pageable);
		}
		catch (Exception e) {
			log.error("搜索用户会话失败: userId={}, keyword={}", userId, keyword, e);
			return Page.empty();
		}
	}

	/**
	 * 获取用户统计信息
	 * @param userId 用户ID
	 * @return 统计信息
	 */
	public Map<String, Object> getUserStats(String userId) {
		try {
			log.debug("获取用户统计信息: userId={}", userId);

			Map<String, Object> stats = new HashMap<>();
			stats.put("totalSessions", chatSessionRepository.countByUserId(userId));

			return stats;
		}
		catch (Exception e) {
			log.error("获取用户统计信息失败: userId={}", userId, e);
			return new HashMap<>();
		}
	}

}
