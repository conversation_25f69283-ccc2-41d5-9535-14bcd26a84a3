package com.cpit.chatbi.server.agent.recorder.service;

import com.cpit.chatbi.server.agent.recorder.entity.SqlQueryHistory;
import org.springframework.data.domain.Page;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * SQL查询历史记录服务接口
 * <p>
 * 用于存储和管理SQL查询的历史记录，包括chatId、SQL语句和执行结果
 * </p>
 *
 * <AUTHOR> Agent
 * @since 1.0.0
 */
public interface SqlQueryHistoryService {

    Long save(SqlQueryHistory history);

    /**
     * 保存SQL查询历史记录
     *
     * @param chatId 对话ID，从ToolContext中获取
     * @param sql SQL查询语句
     * @param executeResult 执行结果（JSON格式）
     * @param dataSourceId 数据源ID
     * @param databaseName 数据库名称
     * @param schemaName Schema名称
     * @param executeStatus 执行状态（success/error）
     * @param errorMessage 错误信息（如果有）
     * @param useTime 执行耗时（毫秒）
     * @param operationRows 操作行数
     * @return 保存的记录ID
     */
    Long saveSqlQueryHistory(String chatId, String sql, String executeResult,
                           Long dataSourceId, String databaseName, String schemaName,
                           String executeStatus, String errorMessage,
                           Long useTime, Long operationRows);

    /**
     * 保存SQL查询历史记录（简化版本）
     *
     * @param chatId 对话ID
     * @param sql SQL查询语句
     * @param executeResult 执行结果
     * @param dataSourceId 数据源ID
     * @param databaseName 数据库名称
     * @param executeStatus 执行状态
     * @return 保存的记录ID
     */
    Long saveSqlQueryHistory(String chatId, String sql, String executeResult,
                           Long dataSourceId, String databaseName, String executeStatus);


    /**
     * 根据chatId查询SQL查询历史记录
     *
     * @param chatId 对话ID
     * @param pageNo 页码（从0开始）
     * @param pageSize 每页大小
     * @return 分页查询结果
     */
    Page<SqlQueryHistory> findByChatId(String chatId, int pageNo, int pageSize);

    /**
     * 根据用户ID查询SQL查询历史记录
     *
     * @param userId 用户ID
     * @param pageNo 页码（从0开始）
     * @param pageSize 每页大小
     * @return 分页查询结果
     */
    Page<SqlQueryHistory> findByUserId(String userId, int pageNo, int pageSize);

    /**
     * 删除指定记录
     *
     * @param id 记录ID
     * @return 是否删除成功
     */
    boolean deleteById(Long id);
}
