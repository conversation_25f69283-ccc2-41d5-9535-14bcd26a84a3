/*
 * Copyright 2025 the original author or authors.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package com.cpit.chatbi.server.agent.recorder;

// import org.springframework.stereotype.Component; // 已被StreamingPlanExecutionRecorder替代

import com.cpit.chatbi.server.agent.recorder.entity.AgentExecutionRecord;
import com.cpit.chatbi.server.agent.recorder.entity.PlanExecutionRecord;
import com.cpit.chatbi.server.agent.recorder.entity.ThinkActRecord;
import com.cpit.chatbi.server.agent.recorder.service.ExecutionRecordPersistenceService;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicLong;

/**
 * 默认的计划执行记录器实现 提供基本的内存存储和记录功能
 */
@Component
public class DefaultPlanExecutionRecorder implements PlanExecutionRecorder {

	private static final Logger log = LoggerFactory.getLogger(DefaultPlanExecutionRecorder.class);

	private final Map<String, PlanExecutionRecord> planRecords = new ConcurrentHashMap<>();

	private final AtomicLong agentExecutionIdGenerator = new AtomicLong(0);

	// 执行记录持久化服务
	@Autowired
	private ExecutionRecordPersistenceService persistenceService;

	@Override
	public String recordPlanExecution(PlanExecutionRecord stepRecord) {
		String planId = stepRecord.getPlanId();
		planRecords.put(planId, stepRecord);
		return planId;
	}

	@Override
	public Long recordAgentExecution(String planId, AgentExecutionRecord agentRecord) {
		Long agentExecutionId = agentExecutionIdGenerator.incrementAndGet();
		agentRecord.setId(agentExecutionId);
		PlanExecutionRecord planRecord = planRecords.get(planId);
		if (planRecord != null) {
			planRecord.addAgentExecutionRecord(agentRecord);
		}
		return agentExecutionId;
	}

	@Override
	public void recordThinkActExecution(String planId, Long agentExecutionId, ThinkActRecord thinkActRecord) {
		PlanExecutionRecord planRecord = planRecords.get(planId);
		if (planRecord != null) {
			for (AgentExecutionRecord agentRecord : planRecord.getAgentExecutionSequence()) {
				if (agentExecutionId.equals(agentRecord.getId())) {
					agentRecord.addThinkActStep(thinkActRecord);
					break;
				}
			}
		}
	}

	@Override
	public void recordPlanCompletion(String planId, String summary) {
		PlanExecutionRecord record = planRecords.get(planId);
		if (record != null) {
			record.complete(summary);
		}
	}

	@Override
	public PlanExecutionRecord getExecutionRecord(String planId) {
		return planRecords.get(planId);
	}

	/**
	 * 将指定计划ID的执行记录保存到持久化存储
	 * @param planId 要保存的计划ID
	 * @return 如果找到并保存了记录则返回 true，否则返回 false
	 */
	@Override
	public boolean savePlanExecutionRecords(String planId) {
		PlanExecutionRecord record = planRecords.get(planId);
		if (record == null) {
			log.warn("未找到要保存的计划执行记录: planId={}", planId);
			return false;
		}

		try {
			Long savedId = persistenceService.savePlanExecutionRecord(record);
			record.setId(savedId); // 设置数据库生成的ID
			log.info("✅ 计划执行记录已保存到数据库: planId={}, dbId={}", planId, savedId);
			return true;
		} catch (Exception e) {
			log.error("❌ 保存计划执行记录失败: planId={}", planId, e);
			return false;
		}
	}

	/**
	 * 将所有执行记录保存到持久化存储
	 */
	@Override
	public void saveAllExecutionRecords() {
		log.info("开始批量保存所有计划执行记录，总数: {}", planRecords.size());
		int successCount = 0;
		int failCount = 0;

		for (Map.Entry<String, PlanExecutionRecord> entry : planRecords.entrySet()) {
			String planId = entry.getKey();
			PlanExecutionRecord record = entry.getValue();

			try {
				Long savedId = persistenceService.savePlanExecutionRecord(record);
				record.setId(savedId); // 设置数据库生成的ID
				successCount++;
				log.debug("✅ 批量保存成功: planId={}, dbId={}", planId, savedId);
			} catch (Exception e) {
				failCount++;
				log.error("❌ 批量保存失败: planId={}", planId, e);
			}
		}

		log.info("批量保存计划执行记录完成: 成功={}, 失败={}", successCount, failCount);
	}

	@Override
	public AgentExecutionRecord getCurrentAgentExecutionRecord(String planId) {
		// 自动清理超过30分钟的计划记录
		cleanOutdatedPlans(30);

		PlanExecutionRecord planRecord = planRecords.get(planId);
		if (planRecord != null) {
			List<AgentExecutionRecord> agentExecutionSequence = planRecord.getAgentExecutionSequence();
			int currentIndex = planRecord.getCurrentStepIndex();
			if (!agentExecutionSequence.isEmpty()) {
				return agentExecutionSequence.get(currentIndex);
			}
		}
		return null;
	}

	/**
	 * 清理超过指定分钟数的过期计划记录
	 * @param expirationMinutes 过期时间（分钟）
	 */
	private void cleanOutdatedPlans(int expirationMinutes) {
		LocalDateTime currentTime = LocalDateTime.now();

		planRecords.entrySet().removeIf(entry -> {
			PlanExecutionRecord record = entry.getValue();
			// 检查记录创建时间是否超过了指定的过期时间
			if (record != null && record.getStartTime() != null) {
				LocalDateTime expirationTime = record.getStartTime().plusMinutes(expirationMinutes);
				return currentTime.isAfter(expirationTime);
			}
			return false;
		});
	}

	/**
	 * 删除指定计划ID的执行记录
	 * @param planId 要删除的计划ID
	 */
	@Override
	public void removeExecutionRecord(String planId) {
		planRecords.remove(planId);
	}

	@Override
	public void updateAgentStatus(String planId, Long agentExecutionId, String status, boolean completed) {
		// 默认实现：仅更新内存中的状态，不发布流式响应
		PlanExecutionRecord planRecord = planRecords.get(planId);
		if (planRecord != null) {
			AgentExecutionRecord agentRecord = planRecord.getAgentExecutionSequence().stream()
					.filter(agent -> agentExecutionId.equals(agent.getId()))
					.findFirst()
					.orElse(null);
			if (agentRecord != null) {
				agentRecord.setStatus(status);
				agentRecord.setCompleted(completed);
				if (completed) {
					agentRecord.setEndTime(LocalDateTime.now());
				}
				log.debug("更新Agent状态: planId={}, agentId={}, status={}, completed={}",
						planId, agentExecutionId, status, completed);
			}
		}
	}

}
