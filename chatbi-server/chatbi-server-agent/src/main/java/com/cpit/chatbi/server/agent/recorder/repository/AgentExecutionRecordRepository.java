package com.cpit.chatbi.server.agent.recorder.repository;

import com.cpit.chatbi.server.agent.recorder.entity.AgentExecutionRecordEntity;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;

/**
 * Agent执行记录数据访问接口
 */
@Repository
public interface AgentExecutionRecordRepository extends JpaRepository<AgentExecutionRecordEntity, Long> {

	/**
	 * 根据计划执行ID查找Agent记录
	 * @param planExecutionId 计划执行ID
	 * @return Agent记录列表
	 */
	List<AgentExecutionRecordEntity> findByPlanExecutionRecordIdOrderByCreatedAt(Long planExecutionId);

	/**
	 * 根据对话ID查找Agent记录
	 * @param conversationId 对话ID
	 * @param pageable 分页参数
	 * @return 分页结果
	 */
	Page<AgentExecutionRecordEntity> findByConversationId(String conversationId, Pageable pageable);

	/**
	 * 根据Agent名称查找记录
	 * @param agentName Agent名称
	 * @param pageable 分页参数
	 * @return 分页结果
	 */
	Page<AgentExecutionRecordEntity> findByAgentName(String agentName, Pageable pageable);

	/**
	 * 根据状态查找记录
	 * @param status 状态
	 * @param pageable 分页参数
	 * @return 分页结果
	 */
	Page<AgentExecutionRecordEntity> findByStatus(String status, Pageable pageable);

	/**
	 * 查找指定时间范围内的记录
	 * @param startTime 开始时间
	 * @param endTime 结束时间
	 * @return 记录列表
	 */
	List<AgentExecutionRecordEntity> findByCreatedAtBetween(LocalDateTime startTime, LocalDateTime endTime);

	/**
	 * 统计Agent执行成功率
	 * @param agentName Agent名称
	 * @param days 天数
	 * @return 统计结果 [总数, 成功数]
	 */
	@Query(value = """
			SELECT
			    COUNT(*) as total_count,
			    COUNT(CASE WHEN is_completed = true THEN 1 END) as success_count
			FROM agent_execution_records
			WHERE agent_name = :agentName
			AND created_at >= :since
			""", nativeQuery = true)
	Object[] getAgentSuccessStats(@Param("agentName") String agentName, @Param("since") LocalDateTime since);

	/**
	 * 获取Agent平均执行时间
	 * @param agentName Agent名称
	 * @param days 天数
	 * @return 平均执行时间（秒）
	 */
	@Query(value = """
			SELECT AVG(EXTRACT(EPOCH FROM (end_time - start_time)))
			FROM agent_execution_records
			WHERE agent_name = :agentName
			AND end_time IS NOT NULL
			AND created_at >= :since
			""", nativeQuery = true)
	Double getAgentAverageExecutionTime(@Param("agentName") String agentName, @Param("since") LocalDateTime since);

	/**
	 * 查找执行时间最长的Agent记录
	 * @param pageable 分页参数
	 * @return 记录列表
	 */
	@Query(value = """
			SELECT a FROM AgentExecutionRecordEntity a
			WHERE a.endTime IS NOT NULL
			ORDER BY (EXTRACT(EPOCH FROM a.endTime) - EXTRACT(EPOCH FROM a.startTime)) DESC
			""")
	List<AgentExecutionRecordEntity> findLongestExecutions(Pageable pageable);

	/**
	 * 获取Agent性能排行榜
	 * @param days 天数
	 * @return 性能统计
	 */
	@Query(value = """
			SELECT
			    agent_name,
			    COUNT(*) as execution_count,
			    COUNT(CASE WHEN is_completed = true THEN 1 END) as success_count,
			    AVG(CASE WHEN end_time IS NOT NULL THEN EXTRACT(EPOCH FROM (end_time - start_time)) END) as avg_duration_seconds,
			    COUNT(CASE WHEN is_stuck = true THEN 1 END) as stuck_count
			FROM agent_execution_records
			WHERE created_at >= :since
			GROUP BY agent_name
			ORDER BY success_count DESC, avg_duration_seconds ASC
			""",
			nativeQuery = true)
	List<Object[]> getAgentPerformanceRanking(@Param("since") LocalDateTime since);

	/**
	 * 查找卡住的Agent执行记录
	 * @param pageable 分页参数
	 * @return 分页结果
	 */
	Page<AgentExecutionRecordEntity> findByIsStuckTrue(Pageable pageable);

	/**
	 * 查找有错误的Agent执行记录
	 * @param pageable 分页参数
	 * @return 分页结果
	 */
	Page<AgentExecutionRecordEntity> findByErrorMessageIsNotNull(Pageable pageable);

	/**
	 * 统计指定Agent的执行次数
	 * @param agentName Agent名称
	 * @param startTime 开始时间
	 * @param endTime 结束时间
	 * @return 执行次数
	 */
	long countByAgentNameAndCreatedAtBetween(String agentName, LocalDateTime startTime, LocalDateTime endTime);

	/**
	 * 查找最活跃的对话
	 * @param days 天数
	 * @return 对话活跃度统计
	 */
	@Query(value = """
			SELECT
			    conversation_id,
			    COUNT(*) as execution_count,
			    MAX(created_at) as last_activity
			FROM agent_execution_records
			WHERE conversation_id IS NOT NULL
			AND created_at >= :since
			GROUP BY conversation_id
			ORDER BY execution_count DESC
			LIMIT 20
			""", nativeQuery = true)
	List<Object[]> getMostActiveConversations(@Param("since") LocalDateTime since);

}
