package com.cpit.chatbi.server.agent.recorder.repository;

import com.cpit.chatbi.server.agent.recorder.entity.PlanExecutionRecordEntity;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * 计划执行记录数据访问接口
 */
@Repository
public interface PlanExecutionRecordRepository extends JpaRepository<PlanExecutionRecordEntity, Long> {

	/**
	 * 根据计划ID查找执行记录（场景级查询，可能返回多条记录）
	 * @param planId 计划ID（场景ID）
	 * @return 执行记录列表
	 */
	List<PlanExecutionRecordEntity> findByPlanId(String planId);

	/**
	 * 根据对话ID查找执行记录（可能有多条记录，按时间排序）
	 * @param chatId 对话ID
	 * @return 执行记录列表，按创建时间倒序排列
	 */
	List<PlanExecutionRecordEntity> findByChatIdOrderByCreatedAtDesc(String chatId);

	/**
	 * 根据对话ID查找执行记录（分页查询，按时间排序）
	 * @param chatId 对话ID
	 * @param pageable 分页参数
	 * @return 分页的执行记录，按创建时间倒序排列
	 */
	Page<PlanExecutionRecordEntity> findByChatIdOrderByCreatedAtDesc(String chatId, Pageable pageable);

	/**
	 * 根据对话ID查找执行记录（分页查询，按时间升序排列）
	 * 用于对话历史显示，最早的记录在前，最新的记录在后（接近对话框）
	 * @param chatId 对话ID
	 * @param pageable 分页参数
	 * @return 分页的执行记录，按创建时间升序排列
	 */
	Page<PlanExecutionRecordEntity> findByChatIdOrderByCreatedAtAsc(String chatId, Pageable pageable);

	/**
	 * 根据对话ID查找最新的执行记录
	 * @param chatId 对话ID
	 * @return 最新的执行记录
	 */
	Optional<PlanExecutionRecordEntity> findFirstByChatIdOrderByCreatedAtDesc(String chatId);

	/**
	 * 检查计划ID是否存在
	 * @param planId 计划ID
	 * @return 是否存在
	 */
	boolean existsByPlanId(String planId);

	/**
	 * 检查对话ID是否存在
	 * @param chatId 对话ID
	 * @return 是否存在
	 */
	boolean existsByChatId(String chatId);

	/**
	 * 根据完成状态查找记录
	 * @param completed 是否完成
	 * @param pageable 分页参数
	 * @return 分页结果
	 */
	Page<PlanExecutionRecordEntity> findByCompleted(Boolean completed, Pageable pageable);

	/**
	 * 查找指定时间范围内的记录
	 * @param startTime 开始时间
	 * @param endTime 结束时间
	 * @return 记录列表
	 */
	List<PlanExecutionRecordEntity> findByCreatedAtBetween(LocalDateTime startTime, LocalDateTime endTime);

	/**
	 * 查找最近的记录
	 * @param pageable 分页参数
	 * @return 分页结果
	 */
	@Query("SELECT p FROM PlanExecutionRecordEntity p ORDER BY p.createdAt DESC")
	Page<PlanExecutionRecordEntity> findRecentRecords(Pageable pageable);

	/**
	 * 根据用户请求关键词搜索
	 * @param keyword 关键词
	 * @param pageable 分页参数
	 * @return 分页结果
	 */
	@Query("SELECT p FROM PlanExecutionRecordEntity p WHERE p.userRequest LIKE %:keyword% OR p.title LIKE %:keyword%")
	Page<PlanExecutionRecordEntity> searchByKeyword(@Param("keyword") String keyword, Pageable pageable);

	/**
	 * 统计指定时间范围内的记录数量
	 * @param startTime 开始时间
	 * @param endTime 结束时间
	 * @return 记录数量
	 */
	@Query("SELECT COUNT(p) FROM PlanExecutionRecordEntity p WHERE p.createdAt BETWEEN :startTime AND :endTime")
	long countByCreatedAtBetween(@Param("startTime") LocalDateTime startTime, @Param("endTime") LocalDateTime endTime);

	/**
	 * 统计完成率
	 * @param startTime 开始时间
	 * @param endTime 结束时间
	 * @return 完成的记录数量
	 */
	@Query("SELECT COUNT(p) FROM PlanExecutionRecordEntity p WHERE p.completed = true AND p.createdAt BETWEEN :startTime AND :endTime")
	long countCompletedByCreatedAtBetween(@Param("startTime") LocalDateTime startTime,
			@Param("endTime") LocalDateTime endTime);

	/**
	 * 删除指定时间之前的记录
	 * @param cutoffTime 截止时间
	 * @return 删除的记录数量
	 */
	@Modifying
	@Query("DELETE FROM PlanExecutionRecordEntity p WHERE p.createdAt < :cutoffTime")
	int deleteByCreatedAtBefore(@Param("cutoffTime") LocalDateTime cutoffTime);

	/**
	 * 查找执行时间最长的记录
	 * @param limit 限制数量
	 * @return 记录列表
	 */
	@Query(value = """
			SELECT p FROM PlanExecutionRecordEntity p
			WHERE p.endTime IS NOT NULL
			ORDER BY (EXTRACT(EPOCH FROM p.endTime) - EXTRACT(EPOCH FROM p.startTime)) DESC
			""")
	List<PlanExecutionRecordEntity> findLongestExecutions(Pageable pageable);

	/**
	 * 获取执行统计信息
	 * @param days 天数
	 * @return 统计结果
	 */
	@Query(value = """
			SELECT
			    COUNT(*) as total_count,
			    COUNT(CASE WHEN completed = true THEN 1 END) as completed_count,
			    AVG(CASE WHEN end_time IS NOT NULL THEN EXTRACT(EPOCH FROM (end_time - start_time)) END) as avg_duration_seconds
			FROM plan_execution_records
			WHERE created_at >= :since
			""",
			nativeQuery = true)
	Object[] getExecutionStats(@Param("since") LocalDateTime since);

	/**
	 * 查找包含特定Agent的执行记录
	 * @param agentName Agent名称
	 * @param pageable 分页参数
	 * @return 分页结果
	 */
	@Query("""
			SELECT DISTINCT p FROM PlanExecutionRecordEntity p
			JOIN p.agentExecutionRecords a
			WHERE a.agentName = :agentName
			ORDER BY p.createdAt DESC
			""")
	Page<PlanExecutionRecordEntity> findByAgentName(@Param("agentName") String agentName, Pageable pageable);

	/**
	 * 查找使用特定工具的执行记录
	 * @param toolName 工具名称
	 * @param pageable 分页参数
	 * @return 分页结果
	 */
	@Query("""
			SELECT DISTINCT p FROM PlanExecutionRecordEntity p
			JOIN p.agentExecutionRecords a
			JOIN a.thinkActRecords t
			WHERE t.toolName = :toolName
			ORDER BY p.createdAt DESC
			""")
	Page<PlanExecutionRecordEntity> findByToolName(@Param("toolName") String toolName, Pageable pageable);

	/**
	 * 获取热门工具使用统计
	 * @param days 天数
	 * @return 工具使用统计
	 */
	@Query(value = """
			SELECT
			    t.tool_name,
			    COUNT(*) as usage_count,
			    COUNT(CASE WHEN t.status = 'SUCCESS' THEN 1 END) as success_count
			FROM plan_execution_records p
			JOIN agent_execution_records a ON p.id = a.plan_execution_id
			JOIN think_act_records t ON a.id = t.agent_execution_id
			WHERE p.created_at >= :since AND t.tool_name IS NOT NULL
			GROUP BY t.tool_name
			ORDER BY usage_count DESC
			LIMIT 20
			""", nativeQuery = true)
	List<Object[]> getToolUsageStats(@Param("since") LocalDateTime since);

}
