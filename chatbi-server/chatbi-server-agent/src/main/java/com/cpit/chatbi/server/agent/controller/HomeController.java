package com.cpit.chatbi.server.agent.controller;

import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.GetMapping;

/**
 * 首页控制器 负责处理根路径访问和页面重定向
 *
 * <AUTHOR> Team
 */
@Controller
public class HomeController {

	/**
	 * 根路径重定向到系统概览页面
	 * @return 重定向到系统概览页面
	 */
	@GetMapping("/")
	public String home() {
		return "redirect:/system-overview.html";
	}

	/**
	 * 主页重定向到系统概览页面
	 * @return 重定向到系统概览页面
	 */
	@GetMapping("/home")
	public String homePage() {
		return "redirect:/system-overview.html";
	}

	/**
	 * 控制台重定向到主控制台页面
	 * @return 重定向到主控制台页面
	 */
	@GetMapping("/dashboard")
	public String dashboard() {
		return "redirect:/main-dashboard.html";
	}

	/**
	 * 编排重定向到智能体编排列表页面
	 * @return 重定向到智能体编排列表页面
	 */
	@GetMapping("/workflow")
	public String workflow() {
		return "redirect:/workflow-list.html";
	}

	/**
	 * 智能体配置重定向
	 * @return 重定向到智能体配置页面
	 */
	@GetMapping("/agents")
	public String agents() {
		return "redirect:/agent-config.html";
	}

	/**
	 * 工具管理重定向
	 * @return 重定向到工具管理页面
	 */
	@GetMapping("/tools")
	public String tools() {
		return "redirect:/mcp-config.html";
	}

	/**
	 * 对话历史重定向
	 * @return 重定向到对话历史页面
	 */
	@GetMapping("/history")
	public String history() {
		return "redirect:/hierarchical-chat.html";
	}

	/**
	 * 系统配置重定向
	 * @return 重定向到系统配置页面
	 */
	@GetMapping("/config")
	public String config() {
		return "redirect:/admin/index.html";
	}

}
