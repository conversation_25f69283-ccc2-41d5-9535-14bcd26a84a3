/*
 * Copyright 2025 the original author or authors.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package com.cpit.chatbi.server.agent.controller;

import com.cpit.chatbi.server.agent.planning.PlanningFactory;
import com.cpit.chatbi.server.agent.planning.coordinator.PlanIdDispatcher;
import com.cpit.chatbi.server.agent.planning.coordinator.PlanningCoordinator;
import com.cpit.chatbi.server.agent.planning.model.vo.ExecutionContext;
import com.cpit.chatbi.server.agent.recorder.PlanExecutionRecorder;
import com.cpit.chatbi.server.agent.recorder.entity.PlanExecutionRecord;
import com.cpit.chatbi.server.agent.util.AsyncExecutionUtils;
import com.cpit.chatbi.server.tools.base.vo.R;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.CompletableFuture;

@RestController
@RequestMapping("/api/executor")
public class ManusController {

	private static final Logger logger = LoggerFactory.getLogger(ManusController.class);

	@Autowired
	@Lazy
	private PlanningFactory planningFactory;

	@Autowired
	private PlanExecutionRecorder planExecutionRecorder;

	@Autowired
	private PlanIdDispatcher planIdDispatcher;

	@Autowired
	private AsyncExecutionUtils asyncExecutionUtils;

	/**
	 * 异步执行 Manus 请求
	 * @param request 包含用户查询的请求
	 * @return 任务ID及状态
	 */
	@PostMapping("/execute")
	public R<Map<String, Object>> executeQuery(@RequestBody Map<String, String> request) {
		String query = request.get("query");
		String modelId = request.get("modelId");
		if (query == null || query.trim().isEmpty()) {
			return R.error("查询内容不能为空", null);
		}
		if (modelId == null || modelId.trim().isEmpty()) {
			return R.error("模型不能为空", null);
		}
		ExecutionContext context = new ExecutionContext();
		context.setUserRequest(query);
		// 使用 PlanIdDispatcher 生成唯一的计划ID
		String planId = planIdDispatcher.generatePlanId();
		context.setPlanId(planId);
		context.setModelId(modelId);
		context.setNeedSummary(true);
		// 获取或创建规划流程
		PlanningCoordinator planningFlow = planningFactory.createPlanningCoordinator(planId, false);

		// 异步执行任务 - 使用统一的异步执行工具类，自动管理 SqlSession
		asyncExecutionUtils.supplyAsyncWithDb(() -> {
			try {
				return planningFlow.executePlan(context);
			}
			catch (Exception e) {
				logger.error("执行计划失败", e);
				throw new RuntimeException("执行计划失败: " + e.getMessage(), e);
			}
		});

		// 返回任务ID及初始状态
		Map<String, Object> response = new HashMap<>();
		response.put("planId", planId);
		response.put("status", "processing");
		response.put("message", "任务已提交，正在处理中");

		return R.ok(response);
	}

	/**
	 * 获取详细的执行记录
	 * @param planId 计划ID
	 * @return 执行记录的 JSON 表示
	 */
	@GetMapping("/details/{planId}")
	public synchronized R<String> getExecutionDetails(@PathVariable("planId") String planId) {
		PlanExecutionRecord planRecord = planExecutionRecorder.getExecutionRecord(planId);

		if (planRecord == null) {
			return R.error("执行记录不存在", null);
		}

		return R.ok("获取成功", planRecord.toJson());
	}

	/**
	 * 删除指定计划ID的执行记录
	 * @param planId 计划ID
	 * @return 删除操作的结果
	 */
	@DeleteMapping("/details/{planId}")
	public R<Map<String, String>> removeExecutionDetails(@PathVariable("planId") String planId) {
		PlanExecutionRecord planRecord = planExecutionRecorder.getExecutionRecord(planId);
		if (planRecord == null) {
			return R.error("执行记录不存在", null);
		}

		try {
			planExecutionRecorder.removeExecutionRecord(planId);
			return R.ok(Map.of("message", "执行记录已成功删除", "planId", planId));
		}
		catch (Exception e) {
			return R.error("删除记录失败: " + e.getMessage(), null);
		}
	}

}
