(function (global, factory) {
  typeof exports === 'object' && typeof module !== 'undefined' ? factory(exports) :
  typeof define === 'function' && define.amd ? define(['exports'], factory) :
  (global = typeof globalThis !== 'undefined' ? globalThis : global || self, factory(global.loupe = {}));
}(this, (function (exports) { 'use strict';

  function _typeof(obj) {
    "@babel/helpers - typeof";

    if (typeof Symbol === "function" && typeof Symbol.iterator === "symbol") {
      _typeof = function (obj) {
        return typeof obj;
      };
    } else {
      _typeof = function (obj) {
        return obj && typeof Symbol === "function" && obj.constructor === Symbol && obj !== Symbol.prototype ? "symbol" : typeof obj;
      };
    }

    return _typeof(obj);
  }

  function _slicedToArray(arr, i) {
    return _arrayWithHoles(arr) || _iterableToArrayLimit(arr, i) || _unsupportedIterableToArray(arr, i) || _nonIterableRest();
  }

  function _arrayWithHoles(arr) {
    if (Array.isArray(arr)) return arr;
  }

  function _iterableToArrayLimit(arr, i) {
    if (typeof Symbol === "undefined" || !(Symbol.iterator in Object(arr))) return;
    var _arr = [];
    var _n = true;
    var _d = false;
    var _e = undefined;

    try {
      for (var _i = arr[Symbol.iterator](), _s; !(_n = (_s = _i.next()).done); _n = true) {
        _arr.push(_s.value);

        if (i && _arr.length === i) break;
      }
    } catch (err) {
      _d = true;
      _e = err;
    } finally {
      try {
        if (!_n && _i["return"] != null) _i["return"]();
      } finally {
        if (_d) throw _e;
      }
    }

    return _arr;
  }

  function _unsupportedIterableToArray(o, minLen) {
    if (!o) return;
    if (typeof o === "string") return _arrayLikeToArray(o, minLen);
    var n = Object.prototype.toString.call(o).slice(8, -1);
    if (n === "Object" && o.constructor) n = o.constructor.name;
    if (n === "Map" || n === "Set") return Array.from(o);
    if (n === "Arguments" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return _arrayLikeToArray(o, minLen);
  }

  function _arrayLikeToArray(arr, len) {
    if (len == null || len > arr.length) len = arr.length;

    for (var i = 0, arr2 = new Array(len); i < len; i++) arr2[i] = arr[i];

    return arr2;
  }

  function _nonIterableRest() {
    throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.");
  }

  var ansiColors = {
    bold: ['1', '22'],
    dim: ['2', '22'],
    italic: ['3', '23'],
    underline: ['4', '24'],
    // 5 & 6 are blinking
    inverse: ['7', '27'],
    hidden: ['8', '28'],
    strike: ['9', '29'],
    // 10-20 are fonts
    // 21-29 are resets for 1-9
    black: ['30', '39'],
    red: ['31', '39'],
    green: ['32', '39'],
    yellow: ['33', '39'],
    blue: ['34', '39'],
    magenta: ['35', '39'],
    cyan: ['36', '39'],
    white: ['37', '39'],
    brightblack: ['30;1', '39'],
    brightred: ['31;1', '39'],
    brightgreen: ['32;1', '39'],
    brightyellow: ['33;1', '39'],
    brightblue: ['34;1', '39'],
    brightmagenta: ['35;1', '39'],
    brightcyan: ['36;1', '39'],
    brightwhite: ['37;1', '39'],
    grey: ['90', '39']
  };
  var styles = {
    special: 'cyan',
    number: 'yellow',
    bigint: 'yellow',
    boolean: 'yellow',
    undefined: 'grey',
    null: 'bold',
    string: 'green',
    symbol: 'green',
    date: 'magenta',
    regexp: 'red'
  };
  var truncator = '…';

  function colorise(value, styleType) {
    var color = ansiColors[styles[styleType]] || ansiColors[styleType];

    if (!color) {
      return String(value);
    }

    return "\x1B[".concat(color[0], "m").concat(String(value), "\x1B[").concat(color[1], "m");
  }

  function normaliseOptions() {
    var _ref = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {},
        _ref$showHidden = _ref.showHidden,
        showHidden = _ref$showHidden === void 0 ? false : _ref$showHidden,
        _ref$depth = _ref.depth,
        depth = _ref$depth === void 0 ? 2 : _ref$depth,
        _ref$colors = _ref.colors,
        colors = _ref$colors === void 0 ? false : _ref$colors,
        _ref$customInspect = _ref.customInspect,
        customInspect = _ref$customInspect === void 0 ? true : _ref$customInspect,
        _ref$showProxy = _ref.showProxy,
        showProxy = _ref$showProxy === void 0 ? false : _ref$showProxy,
        _ref$maxArrayLength = _ref.maxArrayLength,
        maxArrayLength = _ref$maxArrayLength === void 0 ? Infinity : _ref$maxArrayLength,
        _ref$breakLength = _ref.breakLength,
        breakLength = _ref$breakLength === void 0 ? Infinity : _ref$breakLength,
        _ref$seen = _ref.seen,
        seen = _ref$seen === void 0 ? [] : _ref$seen,
        _ref$truncate = _ref.truncate,
        truncate = _ref$truncate === void 0 ? Infinity : _ref$truncate,
        _ref$stylize = _ref.stylize,
        stylize = _ref$stylize === void 0 ? String : _ref$stylize;

    var options = {
      showHidden: Boolean(showHidden),
      depth: Number(depth),
      colors: Boolean(colors),
      customInspect: Boolean(customInspect),
      showProxy: Boolean(showProxy),
      maxArrayLength: Number(maxArrayLength),
      breakLength: Number(breakLength),
      truncate: Number(truncate),
      seen: seen,
      stylize: stylize
    };

    if (options.colors) {
      options.stylize = colorise;
    }

    return options;
  }
  function truncate(string, length) {
    var tail = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : truncator;
    string = String(string);
    var tailLength = tail.length;
    var stringLength = string.length;

    if (tailLength > length && stringLength > tailLength) {
      return tail;
    }

    if (stringLength > length && stringLength > tailLength) {
      return "".concat(string.slice(0, length - tailLength)).concat(tail);
    }

    return string;
  } // eslint-disable-next-line complexity

  function inspectList(list, options, inspectItem) {
    var separator = arguments.length > 3 && arguments[3] !== undefined ? arguments[3] : ', ';
    inspectItem = inspectItem || options.inspect;
    var size = list.length;
    if (size === 0) return '';
    var originalLength = options.truncate;
    var output = '';
    var peek = '';
    var truncated = '';

    for (var i = 0; i < size; i += 1) {
      var last = i + 1 === list.length;
      var secondToLast = i + 2 === list.length;
      truncated = "".concat(truncator, "(").concat(list.length - i, ")");
      var value = list[i]; // If there is more than one remaining we need to account for a separator of `, `

      options.truncate = originalLength - output.length - (last ? 0 : separator.length);
      var string = peek || inspectItem(value, options) + (last ? '' : separator);
      var nextLength = output.length + string.length;
      var truncatedLength = nextLength + truncated.length; // If this is the last element, and adding it would
      // take us over length, but adding the truncator wouldn't - then break now

      if (last && nextLength > originalLength && output.length + truncated.length <= originalLength) {
        break;
      } // If this isn't the last or second to last element to scan,
      // but the string is already over length then break here


      if (!last && !secondToLast && truncatedLength > originalLength) {
        break;
      } // Peek at the next string to determine if we should
      // break early before adding this item to the output


      peek = last ? '' : inspectItem(list[i + 1], options) + (secondToLast ? '' : separator); // If we have one element left, but this element and
      // the next takes over length, the break early

      if (!last && secondToLast && truncatedLength > originalLength && nextLength + peek.length > originalLength) {
        break;
      }

      output += string; // If the next element takes us to length -
      // but there are more after that, then we should truncate now

      if (!last && !secondToLast && nextLength + peek.length >= originalLength) {
        truncated = "".concat(truncator, "(").concat(list.length - i - 1, ")");
        break;
      }

      truncated = '';
    }

    return "".concat(output).concat(truncated);
  }

  function quoteComplexKey(key) {
    if (key.match(/^[a-zA-Z_][a-zA-Z_0-9]*$/)) {
      return key;
    }

    return JSON.stringify(key).replace(/'/g, "\\'").replace(/\\"/g, '"').replace(/(^"|"$)/g, "'");
  }

  function inspectProperty(_ref2, options) {
    var _ref3 = _slicedToArray(_ref2, 2),
        key = _ref3[0],
        value = _ref3[1];

    options.truncate -= 2;

    if (typeof key === 'string') {
      key = quoteComplexKey(key);
    } else if (typeof key !== 'number') {
      key = "[".concat(options.inspect(key, options), "]");
    }

    options.truncate -= key.length;
    value = options.inspect(value, options);
    return "".concat(key, ": ").concat(value);
  }

  function inspectArray(array, options) {
    // Object.keys will always output the Array indices first, so we can slice by
    // `array.length` to get non-index properties
    var nonIndexProperties = Object.keys(array).slice(array.length);
    if (!array.length && !nonIndexProperties.length) return '[]';
    options.truncate -= 4;
    var listContents = inspectList(array, options);
    options.truncate -= listContents.length;
    var propertyContents = '';

    if (nonIndexProperties.length) {
      propertyContents = inspectList(nonIndexProperties.map(function (key) {
        return [key, array[key]];
      }), options, inspectProperty);
    }

    return "[ ".concat(listContents).concat(propertyContents ? ", ".concat(propertyContents) : '', " ]");
  }

  /* !
   * Chai - getFuncName utility
   * Copyright(c) 2012-2016 Jake Luer <<EMAIL>>
   * MIT Licensed
   */

  /**
   * ### .getFuncName(constructorFn)
   *
   * Returns the name of a function.
   * When a non-function instance is passed, returns `null`.
   * This also includes a polyfill function if `aFunc.name` is not defined.
   *
   * @name getFuncName
   * @param {Function} funct
   * @namespace Utils
   * @api public
   */

  var toString = Function.prototype.toString;
  var functionNameMatch = /\s*function(?:\s|\s*\/\*[^(?:*\/)]+\*\/\s*)*([^\s\(\/]+)/;
  var maxFunctionSourceLength = 512;
  function getFuncName(aFunc) {
    if (typeof aFunc !== 'function') {
      return null;
    }

    var name = '';
    if (typeof Function.prototype.name === 'undefined' && typeof aFunc.name === 'undefined') {
      // eslint-disable-next-line prefer-reflect
      var functionSource = toString.call(aFunc);
      // To avoid unconstrained resource consumption due to pathalogically large function names,
      // we limit the available return value to be less than 512 characters.
      if (functionSource.indexOf('(') > maxFunctionSourceLength) {
        return name;
      }
      // Here we run a polyfill if Function does not support the `name` property and if aFunc.name is not defined
      var match = functionSource.match(functionNameMatch);
      if (match) {
        name = match[1];
      }
    } else {
      // If we've got a `name` property we just use it
      name = aFunc.name;
    }

    return name;
  }

  var getFuncName_1 = getFuncName;

  var getArrayName = function getArrayName(array) {
    // We need to special case Node.js' Buffers, which report to be Uint8Array
    if (typeof Buffer === 'function' && array instanceof Buffer) {
      return 'Buffer';
    }

    if (array[Symbol.toStringTag]) {
      return array[Symbol.toStringTag];
    }

    return getFuncName_1(array.constructor);
  };

  function inspectTypedArray(array, options) {
    var name = getArrayName(array);
    options.truncate -= name.length + 4; // Object.keys will always output the Array indices first, so we can slice by
    // `array.length` to get non-index properties

    var nonIndexProperties = Object.keys(array).slice(array.length);
    if (!array.length && !nonIndexProperties.length) return "".concat(name, "[]"); // As we know TypedArrays only contain Unsigned Integers, we can skip inspecting each one and simply
    // stylise the toString() value of them

    var output = '';

    for (var i = 0; i < array.length; i++) {
      var string = "".concat(options.stylize(truncate(array[i], options.truncate), 'number')).concat(i === array.length - 1 ? '' : ', ');
      options.truncate -= string.length;

      if (array[i] !== array.length && options.truncate <= 3) {
        output += "".concat(truncator, "(").concat(array.length - array[i] + 1, ")");
        break;
      }

      output += string;
    }

    var propertyContents = '';

    if (nonIndexProperties.length) {
      propertyContents = inspectList(nonIndexProperties.map(function (key) {
        return [key, array[key]];
      }), options, inspectProperty);
    }

    return "".concat(name, "[ ").concat(output).concat(propertyContents ? ", ".concat(propertyContents) : '', " ]");
  }

  function inspectDate(dateObject, options) {
    var stringRepresentation = dateObject.toJSON();

    if (stringRepresentation === null) {
      return 'Invalid Date';
    }

    var split = stringRepresentation.split('T');
    var date = split[0]; // If we need to - truncate the time portion, but never the date

    return options.stylize("".concat(date, "T").concat(truncate(split[1], options.truncate - date.length - 1)), 'date');
  }

  function inspectFunction(func, options) {
    var name = getFuncName_1(func);

    if (!name) {
      return options.stylize('[Function]', 'special');
    }

    return options.stylize("[Function ".concat(truncate(name, options.truncate - 11), "]"), 'special');
  }

  function inspectMapEntry(_ref, options) {
    var _ref2 = _slicedToArray(_ref, 2),
        key = _ref2[0],
        value = _ref2[1];

    options.truncate -= 4;
    key = options.inspect(key, options);
    options.truncate -= key.length;
    value = options.inspect(value, options);
    return "".concat(key, " => ").concat(value);
  } // IE11 doesn't support `map.entries()`


  function mapToEntries(map) {
    var entries = [];
    map.forEach(function (value, key) {
      entries.push([key, value]);
    });
    return entries;
  }

  function inspectMap(map, options) {
    var size = map.size - 1;

    if (size <= 0) {
      return 'Map{}';
    }

    options.truncate -= 7;
    return "Map{ ".concat(inspectList(mapToEntries(map), options, inspectMapEntry), " }");
  }

  var isNaN = Number.isNaN || function (i) {
    return i !== i;
  }; // eslint-disable-line no-self-compare


  function inspectNumber(number, options) {
    if (isNaN(number)) {
      return options.stylize('NaN', 'number');
    }

    if (number === Infinity) {
      return options.stylize('Infinity', 'number');
    }

    if (number === -Infinity) {
      return options.stylize('-Infinity', 'number');
    }

    if (number === 0) {
      return options.stylize(1 / number === Infinity ? '+0' : '-0', 'number');
    }

    return options.stylize(truncate(number, options.truncate), 'number');
  }

  function inspectBigInt(number, options) {
    var nums = truncate(number.toString(), options.truncate - 1);
    if (nums !== truncator) nums += 'n';
    return options.stylize(nums, 'bigint');
  }

  function inspectRegExp(value, options) {
    var flags = value.toString().split('/')[2];
    var sourceLength = options.truncate - (2 + flags.length);
    var source = value.source;
    return options.stylize("/".concat(truncate(source, sourceLength), "/").concat(flags), 'regexp');
  }

  function arrayFromSet(set) {
    var values = [];
    set.forEach(function (value) {
      values.push(value);
    });
    return values;
  }

  function inspectSet(set, options) {
    if (set.size === 0) return 'Set{}';
    options.truncate -= 7;
    return "Set{ ".concat(inspectList(arrayFromSet(set), options), " }");
  }

  var stringEscapeChars = new RegExp("['\\u0000-\\u001f\\u007f-\\u009f\\u00ad\\u0600-\\u0604\\u070f\\u17b4\\u17b5" + "\\u200c-\\u200f\\u2028-\\u202f\\u2060-\\u206f\\ufeff\\ufff0-\\uffff]", 'g');
  var escapeCharacters = {
    '\b': '\\b',
    '\t': '\\t',
    '\n': '\\n',
    '\f': '\\f',
    '\r': '\\r',
    "'": "\\'",
    '\\': '\\\\'
  };
  var hex = 16;
  var unicodeLength = 4;

  function escape(char) {
    return escapeCharacters[char] || "\\u".concat("0000".concat(char.charCodeAt(0).toString(hex)).slice(-unicodeLength));
  }

  function inspectString(string, options) {
    if (stringEscapeChars.test(string)) {
      string = string.replace(stringEscapeChars, escape);
    }

    return options.stylize("'".concat(truncate(string, options.truncate - 2), "'"), 'string');
  }

  function inspectSymbol(value) {
    if ('description' in Symbol.prototype) {
      return value.description ? "Symbol(".concat(value.description, ")") : 'Symbol()';
    }

    return value.toString();
  }

  var getPromiseValue = function getPromiseValue() {
    return 'Promise{…}';
  };

  try {
    var _process$binding = process.binding('util'),
        getPromiseDetails = _process$binding.getPromiseDetails,
        kPending = _process$binding.kPending,
        kRejected = _process$binding.kRejected;

    if (Array.isArray(getPromiseDetails(Promise.resolve()))) {
      getPromiseValue = function getPromiseValue(value, options) {
        var _getPromiseDetails = getPromiseDetails(value),
            _getPromiseDetails2 = _slicedToArray(_getPromiseDetails, 2),
            state = _getPromiseDetails2[0],
            innerValue = _getPromiseDetails2[1];

        if (state === kPending) {
          return 'Promise{<pending>}';
        }

        return "Promise".concat(state === kRejected ? '!' : '', "{").concat(options.inspect(innerValue, options), "}");
      };
    }
  } catch (notNode) {
    /* ignore */
  }

  var inspectPromise = getPromiseValue;

  function inspectObject(object, options) {
    var properties = Object.getOwnPropertyNames(object);
    var symbols = Object.getOwnPropertySymbols ? Object.getOwnPropertySymbols(object) : [];

    if (properties.length === 0 && symbols.length === 0) {
      return '{}';
    }

    options.truncate -= 4;
    options.seen = options.seen || [];

    if (options.seen.indexOf(object) >= 0) {
      return '[Circular]';
    }

    options.seen.push(object);
    var propertyContents = inspectList(properties.map(function (key) {
      return [key, object[key]];
    }), options, inspectProperty);
    var symbolContents = inspectList(symbols.map(function (key) {
      return [key, object[key]];
    }), options, inspectProperty);
    options.seen.pop();
    var sep = '';

    if (propertyContents && symbolContents) {
      sep = ', ';
    }

    return "{ ".concat(propertyContents).concat(sep).concat(symbolContents, " }");
  }

  var toStringTag = typeof Symbol !== 'undefined' && Symbol.toStringTag ? Symbol.toStringTag : false;
  function inspectClass(value, options) {
    var name = '';

    if (toStringTag && toStringTag in value) {
      name = value[toStringTag];
    }

    name = name || getFuncName_1(value.constructor); // Babel transforms anonymous classes to the name `_class`

    if (!name || name === '_class') {
      name = '<Anonymous Class>';
    }

    options.truncate -= name.length;
    return "".concat(name).concat(inspectObject(value, options));
  }

  function inspectArguments(args, options) {
    if (args.length === 0) return 'Arguments[]';
    options.truncate -= 13;
    return "Arguments[ ".concat(inspectList(args, options), " ]");
  }

  var errorKeys = ['stack', 'line', 'column', 'name', 'message', 'fileName', 'lineNumber', 'columnNumber', 'number', 'description'];
  function inspectObject$1(error, options) {
    var properties = Object.getOwnPropertyNames(error).filter(function (key) {
      return errorKeys.indexOf(key) === -1;
    });
    var name = error.name;
    options.truncate -= name.length;
    var message = '';

    if (typeof error.message === 'string') {
      message = truncate(error.message, options.truncate);
    } else {
      properties.unshift('message');
    }

    message = message ? ": ".concat(message) : '';
    options.truncate -= message.length + 5;
    var propertyContents = inspectList(properties.map(function (key) {
      return [key, error[key]];
    }), options, inspectProperty);
    return "".concat(name).concat(message).concat(propertyContents ? " { ".concat(propertyContents, " }") : '');
  }

  function inspectAttribute(_ref, options) {
    var _ref2 = _slicedToArray(_ref, 2),
        key = _ref2[0],
        value = _ref2[1];

    options.truncate -= 3;

    if (!value) {
      return "".concat(options.stylize(key, 'yellow'));
    }

    return "".concat(options.stylize(key, 'yellow'), "=").concat(options.stylize("\"".concat(value, "\""), 'string'));
  }
  function inspectHTMLCollection(collection, options) {
    // eslint-disable-next-line no-use-before-define
    return inspectList(collection, options, inspectHTML, '\n');
  }
  function inspectHTML(element, options) {
    var properties = element.getAttributeNames();
    var name = element.tagName.toLowerCase();
    var head = options.stylize("<".concat(name), 'special');
    var headClose = options.stylize(">", 'special');
    var tail = options.stylize("</".concat(name, ">"), 'special');
    options.truncate -= name.length * 2 + 5;
    var propertyContents = '';

    if (properties.length > 0) {
      propertyContents += ' ';
      propertyContents += inspectList(properties.map(function (key) {
        return [key, element.getAttribute(key)];
      }), options, inspectAttribute, ' ');
    }

    options.truncate -= propertyContents.length;
    var truncate = options.truncate;
    var children = inspectHTMLCollection(element.children, options);

    if (children && children.length > truncate) {
      children = "".concat(truncator, "(").concat(element.children.length, ")");
    }

    return "".concat(head).concat(propertyContents).concat(headClose).concat(children).concat(tail);
  }

  var symbolsSupported = typeof Symbol === 'function' && typeof Symbol.for === 'function';
  var chaiInspect = symbolsSupported ? Symbol.for('chai/inspect') : '@@chai/inspect';
  var nodeInspect = false;

  try {
    // eslint-disable-next-line global-require
    var nodeUtil = require('util');

    nodeInspect = nodeUtil.inspect ? nodeUtil.inspect.custom : false;
  } catch (noNodeInspect) {
    nodeInspect = false;
  }

  function FakeMap() {
    // eslint-disable-next-line prefer-template
    this.key = 'chai/loupe__' + Math.random() + Date.now();
  }

  FakeMap.prototype = {
    // eslint-disable-next-line object-shorthand
    get: function get(key) {
      return key[this.key];
    },
    // eslint-disable-next-line object-shorthand
    has: function has(key) {
      return this.key in key;
    },
    // eslint-disable-next-line object-shorthand
    set: function set(key, value) {
      if (Object.isExtensible(key)) {
        Object.defineProperty(key, this.key, {
          // eslint-disable-next-line object-shorthand
          value: value,
          configurable: true
        });
      }
    }
  };
  var constructorMap = new (typeof WeakMap === 'function' ? WeakMap : FakeMap)();
  var stringTagMap = {};
  var baseTypesMap = {
    undefined: function undefined$1(value, options) {
      return options.stylize('undefined', 'undefined');
    },
    null: function _null(value, options) {
      return options.stylize(null, 'null');
    },
    boolean: function boolean(value, options) {
      return options.stylize(value, 'boolean');
    },
    Boolean: function Boolean(value, options) {
      return options.stylize(value, 'boolean');
    },
    number: inspectNumber,
    Number: inspectNumber,
    bigint: inspectBigInt,
    BigInt: inspectBigInt,
    string: inspectString,
    String: inspectString,
    function: inspectFunction,
    Function: inspectFunction,
    symbol: inspectSymbol,
    // A Symbol polyfill will return `Symbol` not `symbol` from typedetect
    Symbol: inspectSymbol,
    Array: inspectArray,
    Date: inspectDate,
    Map: inspectMap,
    Set: inspectSet,
    RegExp: inspectRegExp,
    Promise: inspectPromise,
    // WeakSet, WeakMap are totally opaque to us
    WeakSet: function WeakSet(value, options) {
      return options.stylize('WeakSet{…}', 'special');
    },
    WeakMap: function WeakMap(value, options) {
      return options.stylize('WeakMap{…}', 'special');
    },
    Arguments: inspectArguments,
    Int8Array: inspectTypedArray,
    Uint8Array: inspectTypedArray,
    Uint8ClampedArray: inspectTypedArray,
    Int16Array: inspectTypedArray,
    Uint16Array: inspectTypedArray,
    Int32Array: inspectTypedArray,
    Uint32Array: inspectTypedArray,
    Float32Array: inspectTypedArray,
    Float64Array: inspectTypedArray,
    Generator: function Generator() {
      return '';
    },
    DataView: function DataView() {
      return '';
    },
    ArrayBuffer: function ArrayBuffer() {
      return '';
    },
    Error: inspectObject$1,
    HTMLCollection: inspectHTMLCollection,
    NodeList: inspectHTMLCollection
  }; // eslint-disable-next-line complexity

  var inspectCustom = function inspectCustom(value, options, type) {
    if (chaiInspect in value && typeof value[chaiInspect] === 'function') {
      return value[chaiInspect](options);
    }

    if (nodeInspect && nodeInspect in value && typeof value[nodeInspect] === 'function') {
      return value[nodeInspect](options.depth, options);
    }

    if ('inspect' in value && typeof value.inspect === 'function') {
      return value.inspect(options.depth, options);
    }

    if ('constructor' in value && constructorMap.has(value.constructor)) {
      return constructorMap.get(value.constructor)(value, options);
    }

    if (stringTagMap[type]) {
      return stringTagMap[type](value, options);
    }

    return '';
  };

  var toString$1 = Object.prototype.toString; // eslint-disable-next-line complexity

  function inspect(value, options) {
    options = normaliseOptions(options);
    options.inspect = inspect;
    var _options = options,
        customInspect = _options.customInspect;
    var type = value === null ? 'null' : _typeof(value);

    if (type === 'object') {
      type = toString$1.call(value).slice(8, -1);
    } // If it is a base value that we already support, then use Loupe's inspector


    if (baseTypesMap[type]) {
      return baseTypesMap[type](value, options);
    } // If `options.customInspect` is set to true then try to use the custom inspector


    if (customInspect && value) {
      var output = inspectCustom(value, options, type);

      if (output) {
        if (typeof output === 'string') return output;
        return inspect(output, options);
      }
    }

    var proto = value ? Object.getPrototypeOf(value) : false; // If it's a plain Object then use Loupe's inspector

    if (proto === Object.prototype || proto === null) {
      return inspectObject(value, options);
    } // Specifically account for HTMLElements
    // eslint-disable-next-line no-undef


    if (value && typeof HTMLElement === 'function' && value instanceof HTMLElement) {
      return inspectHTML(value, options);
    }

    if ('constructor' in value) {
      // If it is a class, inspect it like an object but add the constructor name
      if (value.constructor !== Object) {
        return inspectClass(value, options);
      } // If it is an object with an anonymous prototype, display it as an object.


      return inspectObject(value, options);
    } // last chance to check if it's an object


    if (value === Object(value)) {
      return inspectObject(value, options);
    } // We have run out of options! Just stringify the value


    return options.stylize(String(value), type);
  }
  function registerConstructor(constructor, inspector) {
    if (constructorMap.has(constructor)) {
      return false;
    }

    constructorMap.set(constructor, inspector);
    return true;
  }
  function registerStringTag(stringTag, inspector) {
    if (stringTag in stringTagMap) {
      return false;
    }

    stringTagMap[stringTag] = inspector;
    return true;
  }
  var custom = chaiInspect;

  exports.custom = custom;
  exports.default = inspect;
  exports.inspect = inspect;
  exports.registerConstructor = registerConstructor;
  exports.registerStringTag = registerStringTag;

  Object.defineProperty(exports, '__esModule', { value: true });

})));
