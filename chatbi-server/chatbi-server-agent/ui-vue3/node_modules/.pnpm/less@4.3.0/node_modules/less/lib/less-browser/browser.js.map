{"version": 3, "file": "browser.js", "sourceRoot": "", "sources": ["../../src/less-browser/browser.js"], "names": [], "mappings": ";;;AAAA,qDAAiC;AAEjC,kBAAe;IACX,SAAS,EAAE,UAAU,QAAQ,EAAE,MAAM,EAAE,KAAK;QACxC,yBAAyB;QACzB,IAAM,IAAI,GAAG,KAAK,CAAC,IAAI,IAAI,EAAE,CAAC;QAE9B,kEAAkE;QAClE,IAAM,EAAE,GAAG,eAAQ,KAAK,CAAC,KAAK,IAAI,KAAK,CAAC,SAAS,CAAC,IAAI,CAAC,CAAE,CAAC;QAE1D,4EAA4E;QAC5E,IAAM,YAAY,GAAG,QAAQ,CAAC,cAAc,CAAC,EAAE,CAAC,CAAC;QACjD,IAAI,gBAAgB,GAAG,KAAK,CAAC;QAE7B,2EAA2E;QAC3E,IAAM,SAAS,GAAG,QAAQ,CAAC,aAAa,CAAC,OAAO,CAAC,CAAC;QAClD,SAAS,CAAC,YAAY,CAAC,MAAM,EAAE,UAAU,CAAC,CAAC;QAC3C,IAAI,KAAK,CAAC,KAAK,EAAE;YACb,SAAS,CAAC,YAAY,CAAC,OAAO,EAAE,KAAK,CAAC,KAAK,CAAC,CAAC;SAChD;QACD,SAAS,CAAC,EAAE,GAAG,EAAE,CAAC;QAElB,IAAI,CAAC,SAAS,CAAC,UAAU,EAAE;YACvB,SAAS,CAAC,WAAW,CAAC,QAAQ,CAAC,cAAc,CAAC,MAAM,CAAC,CAAC,CAAC;YAEvD,6EAA6E;YAC7E,gBAAgB,GAAG,CAAC,YAAY,KAAK,IAAI,IAAI,YAAY,CAAC,UAAU,CAAC,MAAM,GAAG,CAAC,IAAI,SAAS,CAAC,UAAU,CAAC,MAAM,GAAG,CAAC;gBAC9G,YAAY,CAAC,UAAU,CAAC,SAAS,KAAK,SAAS,CAAC,UAAU,CAAC,SAAS,CAAC,CAAC;SAC7E;QAED,IAAM,IAAI,GAAG,QAAQ,CAAC,oBAAoB,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;QAEtD,8EAA8E;QAC9E,qDAAqD;QACrD,IAAI,YAAY,KAAK,IAAI,IAAI,gBAAgB,KAAK,KAAK,EAAE;YACrD,IAAM,MAAM,GAAG,KAAK,IAAI,KAAK,CAAC,WAAW,IAAI,IAAI,CAAC;YAClD,IAAI,MAAM,EAAE;gBACR,MAAM,CAAC,UAAU,CAAC,YAAY,CAAC,SAAS,EAAE,MAAM,CAAC,CAAC;aACrD;iBAAM;gBACH,IAAI,CAAC,WAAW,CAAC,SAAS,CAAC,CAAC;aAC/B;SACJ;QACD,IAAI,YAAY,IAAI,gBAAgB,KAAK,KAAK,EAAE;YAC5C,YAAY,CAAC,UAAU,CAAC,WAAW,CAAC,YAAY,CAAC,CAAC;SACrD;QAED,UAAU;QACV,sGAAsG;QACtG,mJAAmJ;QACnJ,IAAI,SAAS,CAAC,UAAU,EAAE;YACtB,IAAI;gBACA,SAAS,CAAC,UAAU,CAAC,OAAO,GAAG,MAAM,CAAC;aACzC;YAAC,OAAO,CAAC,EAAE;gBACR,MAAM,IAAI,KAAK,CAAC,wCAAwC,CAAC,CAAC;aAC7D;SACJ;IACL,CAAC;IACD,aAAa,EAAE,UAAS,MAAM;QAC1B,IAAM,QAAQ,GAAG,MAAM,CAAC,QAAQ,CAAC;QACjC,OAAO,QAAQ,CAAC,aAAa,IAAI,CAAC;YAC9B,IAAM,OAAO,GAAG,QAAQ,CAAC,oBAAoB,CAAC,QAAQ,CAAC,CAAC;YACxD,OAAO,OAAO,CAAC,OAAO,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;QACvC,CAAC,CAAC,EAAE,CAAC;IACT,CAAC;CACJ,CAAC", "sourcesContent": ["import * as utils from './utils';\n\nexport default {\n    createCSS: function (document, styles, sheet) {\n        // Strip the query-string\n        const href = sheet.href || '';\n\n        // If there is no title set, use the filename, minus the extension\n        const id = `less:${sheet.title || utils.extractId(href)}`;\n\n        // If this has already been inserted into the DOM, we may need to replace it\n        const oldStyleNode = document.getElementById(id);\n        let keepOldStyleNode = false;\n\n        // Create a new stylesheet node for insertion or (if necessary) replacement\n        const styleNode = document.createElement('style');\n        styleNode.setAttribute('type', 'text/css');\n        if (sheet.media) {\n            styleNode.setAttribute('media', sheet.media);\n        }\n        styleNode.id = id;\n\n        if (!styleNode.styleSheet) {\n            styleNode.appendChild(document.createTextNode(styles));\n\n            // If new contents match contents of oldStyleNode, don't replace oldStyleNode\n            keepOldStyleNode = (oldStyleNode !== null && oldStyleNode.childNodes.length > 0 && styleNode.childNodes.length > 0 &&\n                oldStyleNode.firstChild.nodeValue === styleNode.firstChild.nodeValue);\n        }\n\n        const head = document.getElementsByTagName('head')[0];\n\n        // If there is no oldStyleNode, just append; otherwise, only append if we need\n        // to replace oldStyleNode with an updated stylesheet\n        if (oldStyleNode === null || keepOldStyleNode === false) {\n            const nextEl = sheet && sheet.nextSibling || null;\n            if (nextEl) {\n                nextEl.parentNode.insertBefore(styleNode, nextEl);\n            } else {\n                head.appendChild(styleNode);\n            }\n        }\n        if (oldStyleNode && keepOldStyleNode === false) {\n            oldStyleNode.parentNode.removeChild(oldStyleNode);\n        }\n\n        // For IE.\n        // This needs to happen *after* the style element is added to the DOM, otherwise IE 7 and 8 may crash.\n        // See http://social.msdn.microsoft.com/Forums/en-US/7e081b65-878a-4c22-8e68-c10d39c2ed32/internet-explorer-crashes-appending-style-element-to-head\n        if (styleNode.styleSheet) {\n            try {\n                styleNode.styleSheet.cssText = styles;\n            } catch (e) {\n                throw new Error('Couldn\\'t reassign styleSheet.cssText.');\n            }\n        }\n    },\n    currentScript: function(window) {\n        const document = window.document;\n        return document.currentScript || (() => {\n            const scripts = document.getElementsByTagName('script');\n            return scripts[scripts.length - 1];\n        })();\n    }\n};\n"]}