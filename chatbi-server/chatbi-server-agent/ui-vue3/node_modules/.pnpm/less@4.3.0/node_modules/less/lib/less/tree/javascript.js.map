{"version": 3, "file": "javascript.js", "sourceRoot": "", "sources": ["../../../src/less/tree/javascript.js"], "names": [], "mappings": ";;;AAAA,wEAAwC;AACxC,kEAAoC;AACpC,4DAA8B;AAC9B,kEAAoC;AAEpC,IAAM,UAAU,GAAG,UAAS,MAAM,EAAE,OAAO,EAAE,KAAK,EAAE,eAAe;IAC/D,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC;IACvB,IAAI,CAAC,UAAU,GAAG,MAAM,CAAC;IACzB,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC;IACpB,IAAI,CAAC,SAAS,GAAG,eAAe,CAAC;AACrC,CAAC,CAAA;AAED,UAAU,CAAC,SAAS,GAAG,MAAM,CAAC,MAAM,CAAC,IAAI,sBAAU,EAAE,EAAE;IACnD,IAAI,EAAE,YAAY;IAElB,IAAI,YAAC,OAAO;QACR,IAAM,MAAM,GAAG,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,UAAU,EAAE,OAAO,CAAC,CAAC;QACjE,IAAM,IAAI,GAAG,OAAO,MAAM,CAAC;QAE3B,IAAI,IAAI,KAAK,QAAQ,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,EAAE;YACrC,OAAO,IAAI,mBAAS,CAAC,MAAM,CAAC,CAAC;SAChC;aAAM,IAAI,IAAI,KAAK,QAAQ,EAAE;YAC1B,OAAO,IAAI,gBAAM,CAAC,YAAI,MAAM,OAAG,EAAE,MAAM,EAAE,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC;SACvE;aAAM,IAAI,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE;YAC9B,OAAO,IAAI,mBAAS,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;SAC3C;aAAM;YACH,OAAO,IAAI,mBAAS,CAAC,MAAM,CAAC,CAAC;SAChC;IACL,CAAC;CACJ,CAAC,CAAC;AAEH,kBAAe,UAAU,CAAC", "sourcesContent": ["import JsEvalNode from './js-eval-node';\nimport Dimension from './dimension';\nimport Quoted from './quoted';\nimport Anonymous from './anonymous';\n\nconst JavaScript = function(string, escaped, index, currentFileInfo) {\n    this.escaped = escaped;\n    this.expression = string;\n    this._index = index;\n    this._fileInfo = currentFileInfo;\n}\n\nJavaScript.prototype = Object.assign(new JsEvalNode(), {\n    type: 'JavaScript',\n\n    eval(context) {\n        const result = this.evaluateJavaScript(this.expression, context);\n        const type = typeof result;\n\n        if (type === 'number' && !isNaN(result)) {\n            return new Dimension(result);\n        } else if (type === 'string') {\n            return new Quoted(`\"${result}\"`, result, this.escaped, this._index);\n        } else if (Array.isArray(result)) {\n            return new Anonymous(result.join(', '));\n        } else {\n            return new Anonymous(result);\n        }\n    }\n});\n\nexport default JavaScript;\n"]}