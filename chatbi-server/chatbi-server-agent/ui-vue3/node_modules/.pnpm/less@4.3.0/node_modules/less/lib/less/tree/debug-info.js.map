{"version": 3, "file": "debug-info.js", "sourceRoot": "", "sources": ["../../../src/less/tree/debug-info.js"], "names": [], "mappings": ";;AAAA,SAAS,SAAS,CAAC,GAAG;IAClB,OAAO,kBAAW,GAAG,CAAC,SAAS,CAAC,UAAU,eAAK,GAAG,CAAC,SAAS,CAAC,QAAQ,UAAO,CAAC;AACjF,CAAC;AAED,SAAS,YAAY,CAAC,GAAG;IACrB,IAAI,oBAAoB,GAAG,GAAG,CAAC,SAAS,CAAC,QAAQ,CAAC;IAClD,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,oBAAoB,CAAC,EAAE;QAC7C,oBAAoB,GAAG,iBAAU,oBAAoB,CAAE,CAAC;KAC3D;IACD,OAAO,uDAAgD,oBAAoB,CAAC,OAAO,CAAC,YAAY,EAAE,UAAU,CAAC;QACzG,IAAI,CAAC,IAAI,IAAI,EAAE;YACX,CAAC,GAAG,GAAG,CAAC;SACX;QACD,OAAO,YAAK,CAAC,CAAE,CAAC;IACpB,CAAC,CAAC,sCAA4B,GAAG,CAAC,SAAS,CAAC,UAAU,SAAM,CAAC;AACjE,CAAC;AAED,SAAS,SAAS,CAAC,OAAO,EAAE,GAAG,EAAE,aAAa;IAC1C,IAAI,MAAM,GAAG,EAAE,CAAC;IAChB,IAAI,OAAO,CAAC,eAAe,IAAI,CAAC,OAAO,CAAC,QAAQ,EAAE;QAC9C,QAAQ,OAAO,CAAC,eAAe,EAAE;YAC7B,KAAK,UAAU;gBACX,MAAM,GAAG,SAAS,CAAC,GAAG,CAAC,CAAC;gBACxB,MAAM;YACV,KAAK,YAAY;gBACb,MAAM,GAAG,YAAY,CAAC,GAAG,CAAC,CAAC;gBAC3B,MAAM;YACV,KAAK,KAAK;gBACN,MAAM,GAAG,SAAS,CAAC,GAAG,CAAC,GAAG,CAAC,aAAa,IAAI,EAAE,CAAC,GAAG,YAAY,CAAC,GAAG,CAAC,CAAC;gBACpE,MAAM;SACb;KACJ;IACD,OAAO,MAAM,CAAC;AAClB,CAAC;AAED,kBAAe,SAAS,CAAC", "sourcesContent": ["function asComment(ctx) {\n    return `/* line ${ctx.debugInfo.lineNumber}, ${ctx.debugInfo.fileName} */\\n`;\n}\n\nfunction asMediaQuery(ctx) {\n    let filenameWithProtocol = ctx.debugInfo.fileName;\n    if (!/^[a-z]+:\\/\\//i.test(filenameWithProtocol)) {\n        filenameWithProtocol = `file://${filenameWithProtocol}`;\n    }\n    return `@media -sass-debug-info{filename{font-family:${filenameWithProtocol.replace(/([.:/\\\\])/g, function (a) {\n        if (a == '\\\\') {\n            a = '/';\n        }\n        return `\\\\${a}`;\n    })}}line{font-family:\\\\00003${ctx.debugInfo.lineNumber}}}\\n`;\n}\n\nfunction debugInfo(context, ctx, lineSeparator) {\n    let result = '';\n    if (context.dumpLineNumbers && !context.compress) {\n        switch (context.dumpLineNumbers) {\n            case 'comments':\n                result = asComment(ctx);\n                break;\n            case 'mediaquery':\n                result = asMediaQuery(ctx);\n                break;\n            case 'all':\n                result = asComment(ctx) + (lineSeparator || '') + asMediaQuery(ctx);\n                break;\n        }\n    }\n    return result;\n}\n\nexport default debugInfo;\n\n"]}