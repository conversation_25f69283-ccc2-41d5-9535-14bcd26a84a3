{"version": 3, "file": "extend.js", "sourceRoot": "", "sources": ["../../../src/less/tree/extend.js"], "names": [], "mappings": ";;;AAAA,wDAA0B;AAC1B,gEAAkC;AAElC,IAAM,MAAM,GAAG,UAAS,QAAQ,EAAE,MAAM,EAAE,KAAK,EAAE,eAAe,EAAE,cAAc;IAC5E,IAAI,CAAC,QAAQ,GAAG,QAAQ,CAAC;IACzB,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;IACrB,IAAI,CAAC,SAAS,GAAG,MAAM,CAAC,OAAO,EAAE,CAAC;IAClC,IAAI,CAAC,UAAU,GAAG,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;IACnC,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC;IACpB,IAAI,CAAC,SAAS,GAAG,eAAe,CAAC;IACjC,IAAI,CAAC,kBAAkB,CAAC,cAAc,CAAC,CAAC;IACxC,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;IAEtB,QAAQ,MAAM,EAAE;QACZ,KAAK,MAAM,CAAC;QACZ,KAAK,KAAK;YACN,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC;YACxB,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC;YACvB,MAAM;QACV;YACI,IAAI,CAAC,WAAW,GAAG,KAAK,CAAC;YACzB,IAAI,CAAC,UAAU,GAAG,KAAK,CAAC;YACxB,MAAM;KACb;IACD,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC,CAAC;AACxC,CAAC,CAAC;AAEF,MAAM,CAAC,SAAS,GAAG,MAAM,CAAC,MAAM,CAAC,IAAI,cAAI,EAAE,EAAE;IACzC,IAAI,EAAE,QAAQ;IAEd,MAAM,YAAC,OAAO;QACV,IAAI,CAAC,QAAQ,GAAG,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;IACjD,CAAC;IAED,IAAI,YAAC,OAAO;QACR,OAAO,IAAI,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,EAAE,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,QAAQ,EAAE,EAAE,IAAI,CAAC,QAAQ,EAAE,EAAE,IAAI,CAAC,cAAc,EAAE,CAAC,CAAC;IACzH,CAAC;IAED,qCAAqC;IACrC,0CAA0C;IAC1C,KAAK,YAAC,OAAO;QACT,OAAO,IAAI,MAAM,CAAC,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,QAAQ,EAAE,EAAE,IAAI,CAAC,QAAQ,EAAE,EAAE,IAAI,CAAC,cAAc,EAAE,CAAC,CAAC;IAC3G,CAAC;IAED,0DAA0D;IAC1D,iBAAiB,YAAC,SAAS;QACvB,IAAI,YAAY,GAAG,EAAE,EAAE,CAAC,EAAE,gBAAgB,CAAC;QAE3C,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,SAAS,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YACnC,gBAAgB,GAAG,SAAS,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC;YACzC,mEAAmE;YACnE,kEAAkE;YAClE,IAAI,CAAC,GAAG,CAAC,IAAI,gBAAgB,CAAC,MAAM,IAAI,gBAAgB,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,KAAK,KAAK,EAAE,EAAE;gBACjF,gBAAgB,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,KAAK,GAAG,GAAG,CAAC;aAC9C;YACD,YAAY,GAAG,YAAY,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC;SAC7D;QAED,IAAI,CAAC,aAAa,GAAG,CAAC,IAAI,kBAAQ,CAAC,YAAY,CAAC,CAAC,CAAC;QAClD,IAAI,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC,kBAAkB,CAAC,IAAI,CAAC,cAAc,EAAE,CAAC,CAAC;IACpE,CAAC;CACJ,CAAC,CAAC;AAEH,MAAM,CAAC,OAAO,GAAG,CAAC,CAAC;AACnB,kBAAe,MAAM,CAAC", "sourcesContent": ["import Node from './node';\nimport Selector from './selector';\n\nconst Extend = function(selector, option, index, currentFileInfo, visibilityInfo) {\n    this.selector = selector;\n    this.option = option;\n    this.object_id = Extend.next_id++;\n    this.parent_ids = [this.object_id];\n    this._index = index;\n    this._fileInfo = currentFileInfo;\n    this.copyVisibilityInfo(visibilityInfo);\n    this.allowRoot = true;\n\n    switch (option) {\n        case '!all':\n        case 'all':\n            this.allowBefore = true;\n            this.allowAfter = true;\n            break;\n        default:\n            this.allowBefore = false;\n            this.allowAfter = false;\n            break;\n    }\n    this.setParent(this.selector, this);\n};\n\nExtend.prototype = Object.assign(new Node(), {\n    type: 'Extend',\n\n    accept(visitor) {\n        this.selector = visitor.visit(this.selector);\n    },\n\n    eval(context) {\n        return new Extend(this.selector.eval(context), this.option, this.getIndex(), this.fileInfo(), this.visibilityInfo());\n    },\n\n    // remove when Nodes have JSDoc types\n    // eslint-disable-next-line no-unused-vars\n    clone(context) {\n        return new Extend(this.selector, this.option, this.getIndex(), this.fileInfo(), this.visibilityInfo());\n    },\n\n    // it concatenates (joins) all selectors in selector array\n    findSelfSelectors(selectors) {\n        let selfElements = [], i, selectorElements;\n\n        for (i = 0; i < selectors.length; i++) {\n            selectorElements = selectors[i].elements;\n            // duplicate the logic in genCSS function inside the selector node.\n            // future TODO - move both logics into the selector joiner visitor\n            if (i > 0 && selectorElements.length && selectorElements[0].combinator.value === '') {\n                selectorElements[0].combinator.value = ' ';\n            }\n            selfElements = selfElements.concat(selectors[i].elements);\n        }\n\n        this.selfSelectors = [new Selector(selfElements)];\n        this.selfSelectors[0].copyVisibilityInfo(this.visibilityInfo());\n    }\n});\n\nExtend.next_id = 0;\nexport default Extend;\n"]}