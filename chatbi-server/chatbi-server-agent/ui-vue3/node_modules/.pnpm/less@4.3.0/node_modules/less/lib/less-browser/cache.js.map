{"version": 3, "file": "cache.js", "sourceRoot": "", "sources": ["../../src/less-browser/cache.js"], "names": [], "mappings": ";AAAA,wDAAwD;;AAExD,mBAAe,UAAC,MAAM,EAAE,OAAO,EAAE,MAAM;IACnC,IAAI,KAAK,GAAG,IAAI,CAAC;IACjB,IAAI,OAAO,CAAC,GAAG,KAAK,aAAa,EAAE;QAC/B,IAAI;YACA,KAAK,GAAG,CAAC,OAAO,MAAM,CAAC,YAAY,KAAK,WAAW,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,MAAM,CAAC,YAAY,CAAC;SACrF;QAAC,OAAO,CAAC,EAAE,GAAE;KACjB;IACD,OAAO;QACH,MAAM,EAAE,UAAS,IAAI,EAAE,YAAY,EAAE,UAAU,EAAE,MAAM;YACnD,IAAI,KAAK,EAAE;gBACP,MAAM,CAAC,IAAI,CAAC,iBAAU,IAAI,eAAY,CAAC,CAAC;gBACxC,IAAI;oBACA,KAAK,CAAC,OAAO,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC;oBAC5B,KAAK,CAAC,OAAO,CAAC,UAAG,IAAI,eAAY,EAAE,YAAY,CAAC,CAAC;oBACjD,IAAI,UAAU,EAAE;wBACZ,KAAK,CAAC,OAAO,CAAC,UAAG,IAAI,UAAO,EAAE,IAAI,CAAC,SAAS,CAAC,UAAU,CAAC,CAAC,CAAC;qBAC7D;iBACJ;gBAAC,OAAO,CAAC,EAAE;oBACR,yDAAyD;oBACzD,MAAM,CAAC,KAAK,CAAC,2BAAmB,IAAI,qCAAiC,CAAC,CAAC;iBAC1E;aACJ;QACL,CAAC;QACD,MAAM,EAAE,UAAS,IAAI,EAAE,OAAO,EAAE,UAAU;YACtC,IAAM,GAAG,GAAS,KAAK,IAAI,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;YAC/C,IAAM,SAAS,GAAG,KAAK,IAAI,KAAK,CAAC,OAAO,CAAC,UAAG,IAAI,eAAY,CAAC,CAAC;YAC9D,IAAI,IAAI,GAAQ,KAAK,IAAI,KAAK,CAAC,OAAO,CAAC,UAAG,IAAI,UAAO,CAAC,CAAC;YAEvD,UAAU,GAAG,UAAU,IAAI,EAAE,CAAC;YAC9B,IAAI,GAAG,IAAI,IAAI,IAAI,CAAC,CAAC,kEAAkE;YAEvF,IAAI,SAAS,IAAI,OAAO,CAAC,YAAY;gBACjC,CAAC,IAAI,IAAI,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC,OAAO,EAAE;oBACrC,IAAI,IAAI,CAAC,SAAS,CAAC,CAAC,OAAO,EAAE,CAAC;gBAClC,IAAI,CAAC,SAAS,CAAC,UAAU,CAAC,KAAK,IAAI,EAAE;gBACrC,iBAAiB;gBACjB,OAAO,GAAG,CAAC;aACd;QACL,CAAC;KACJ,CAAC;AACN,CAAC,EAAC", "sourcesContent": ["// Cache system is a bit outdated and could do with work\n\nexport default (window, options, logger) => {\n    let cache = null;\n    if (options.env !== 'development') {\n        try {\n            cache = (typeof window.localStorage === 'undefined') ? null : window.localStorage;\n        } catch (_) {}\n    }\n    return {\n        setCSS: function(path, lastModified, modifyVars, styles) {\n            if (cache) {\n                logger.info(`saving ${path} to cache.`);\n                try {\n                    cache.setItem(path, styles);\n                    cache.setItem(`${path}:timestamp`, lastModified);\n                    if (modifyVars) {\n                        cache.setItem(`${path}:vars`, JSON.stringify(modifyVars));\n                    }\n                } catch (e) {\n                    // TODO - could do with adding more robust error handling\n                    logger.error(`failed to save \"${path}\" to local storage for caching.`);\n                }\n            }\n        },\n        getCSS: function(path, webInfo, modifyVars) {\n            const css       = cache && cache.getItem(path);\n            const timestamp = cache && cache.getItem(`${path}:timestamp`);\n            let vars      = cache && cache.getItem(`${path}:vars`);\n\n            modifyVars = modifyVars || {};\n            vars = vars || '{}'; // if not set, treat as the JSON representation of an empty object\n\n            if (timestamp && webInfo.lastModified &&\n                (new Date(webInfo.lastModified).valueOf() ===\n                    new Date(timestamp).valueOf()) &&\n                JSON.stringify(modifyVars) === vars) {\n                // Use local copy\n                return css;\n            }\n        }\n    };\n};\n"]}