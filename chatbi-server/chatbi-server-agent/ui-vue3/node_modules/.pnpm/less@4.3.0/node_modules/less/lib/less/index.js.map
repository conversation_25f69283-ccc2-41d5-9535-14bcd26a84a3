{"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["../../src/less/index.js"], "names": [], "mappings": ";;;AAAA,kFAAoD;AACpD,wDAA0B;AAC1B,wDAA0B;AAC1B,sGAAsE;AACtE,wGAAwE;AACxE,gEAAkC;AAClC,mEAAqC;AACrC,kEAAoC;AACpC,gEAAkC;AAClC,oEAAqC;AACrC,4EAA6C;AAC7C,qDAAiC;AACjC,4EAA6C;AAC7C,4DAA8B;AAC9B,kFAAkD;AAClD,oFAAoD;AACpD,oEAAqC;AACrC,4EAA6C;AAC7C,0DAA4B;AAC5B,4DAA8B;AAC9B,mDAA6C;AAC7C,kFAA8C;AAE9C,mBAAwB,WAAW,EAAE,YAAY;IAC7C,IAAI,eAAe,EAAE,gBAAgB,EAAE,SAAS,EAAE,aAAa,CAAC;IAEhE,WAAW,GAAG,IAAI,qBAAW,CAAC,WAAW,EAAE,YAAY,CAAC,CAAC;IACzD,eAAe,GAAG,IAAA,2BAAe,EAAC,WAAW,CAAC,CAAC;IAC/C,gBAAgB,GAAG,IAAA,4BAAgB,EAAC,eAAe,EAAE,WAAW,CAAC,CAAC;IAClE,SAAS,GAAG,IAAA,oBAAS,EAAC,gBAAgB,CAAC,CAAC;IACxC,aAAa,GAAG,IAAA,wBAAa,EAAC,WAAW,CAAC,CAAC;IAE3C,IAAM,MAAM,GAAG,IAAA,gBAAM,EAAC,WAAW,EAAE,SAAS,EAAE,aAAa,CAAC,CAAC;IAC7D,IAAM,KAAK,GAAG,IAAA,eAAK,EAAC,WAAW,EAAE,SAAS,EAAE,aAAa,CAAC,CAAC;IAE3D,IAAM,CAAC,GAAG,IAAA,4BAAY,EAAC,WAAI,sBAAO,CAAE,CAAC,CAAC;IACtC,IAAM,OAAO,GAAG;QACZ,OAAO,EAAE,CAAC,CAAC,CAAC,KAAK,EAAE,CAAC,CAAC,KAAK,EAAE,CAAC,CAAC,KAAK,CAAC;QACpC,IAAI,gBAAA;QACJ,IAAI,gBAAA;QACJ,WAAW,uBAAA;QACX,mBAAmB,iCAAA;QACnB,oBAAoB,kCAAA;QACpB,WAAW,aAAA;QACX,QAAQ,oBAAA;QACR,MAAM,kBAAA;QACN,SAAS,EAAE,IAAA,mBAAS,EAAC,WAAW,CAAC;QACjC,QAAQ,oBAAA;QACR,eAAe,EAAE,eAAe;QAChC,gBAAgB,EAAE,gBAAgB;QAClC,SAAS,EAAE,SAAS;QACpB,aAAa,EAAE,aAAa;QAC5B,MAAM,QAAA;QACN,KAAK,OAAA;QACL,SAAS,sBAAA;QACT,aAAa,0BAAA;QACb,KAAK,OAAA;QACL,aAAa,0BAAA;QACb,MAAM,kBAAA;KACT,CAAC;IAEF,sBAAsB;IAEtB,IAAM,IAAI,GAAG,UAAS,CAAC;QACnB,OAAO;YACH,IAAM,GAAG,GAAG,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC;YACvC,CAAC,CAAC,KAAK,CAAC,GAAG,EAAE,KAAK,CAAC,SAAS,CAAC,KAAK,CAAC,IAAI,CAAC,SAAS,EAAE,CAAC,CAAC,CAAC,CAAC;YACvD,OAAO,GAAG,CAAC;QACf,CAAC,CAAC;IACN,CAAC,CAAC;IACF,IAAI,CAAC,CAAC;IACN,IAAM,GAAG,GAAG,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;IACnC,KAAK,IAAM,CAAC,IAAI,OAAO,CAAC,IAAI,EAAE;QAC1B,4BAA4B;QAC5B,CAAC,GAAG,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QACpB,IAAI,OAAO,CAAC,KAAK,UAAU,EAAE;YACzB,GAAG,CAAC,CAAC,CAAC,WAAW,EAAE,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC;SAClC;aACI;YACD,GAAG,CAAC,CAAC,CAAC,GAAG,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;YAC7B,KAAK,IAAM,CAAC,IAAI,CAAC,EAAE;gBACf,4BAA4B;gBAC5B,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,WAAW,EAAE,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;aACxC;SACJ;KACJ;IAED;;;;;OAKG;IACH,OAAO,CAAC,KAAK,GAAG,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;IACxC,OAAO,CAAC,MAAM,GAAG,OAAO,CAAC,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;IAE1C,OAAO,GAAG,CAAC;AACf,CAAC;AA1ED,4BA0EC", "sourcesContent": ["import Environment from './environment/environment';\nimport data from './data';\nimport tree from './tree';\nimport AbstractFileManager from './environment/abstract-file-manager';\nimport AbstractPluginLoader from './environment/abstract-plugin-loader';\nimport visitors from './visitors';\nimport Parser from './parser/parser';\nimport functions from './functions';\nimport contexts from './contexts';\nimport LessError from './less-error';\nimport transformTree from './transform-tree';\nimport * as utils from './utils';\nimport PluginManager from './plugin-manager';\nimport logger from './logger';\nimport SourceMapOutput from './source-map-output';\nimport SourceMapBuilder from './source-map-builder';\nimport ParseTree from './parse-tree';\nimport ImportManager from './import-manager';\nimport Parse from './parse';\nimport Render from './render';\nimport { version } from '../../package.json';\nimport parseVersion from 'parse-node-version';\n\nexport default function(environment, fileManagers) {\n    let sourceMapOutput, sourceMapBuilder, parseTree, importManager;\n\n    environment = new Environment(environment, fileManagers);\n    sourceMapOutput = SourceMapOutput(environment);\n    sourceMapBuilder = SourceMapBuilder(sourceMapOutput, environment);\n    parseTree = ParseTree(sourceMapBuilder);\n    importManager = ImportManager(environment);\n\n    const render = Render(environment, parseTree, importManager);\n    const parse = Parse(environment, parseTree, importManager);\n\n    const v = parseVersion(`v${version}`);\n    const initial = {\n        version: [v.major, v.minor, v.patch],\n        data,\n        tree,\n        Environment,\n        AbstractFileManager,\n        AbstractPluginLoader,\n        environment,\n        visitors,\n        Parser,\n        functions: functions(environment),\n        contexts,\n        SourceMapOutput: sourceMapOutput,\n        SourceMapBuilder: sourceMapBuilder,\n        ParseTree: parseTree,\n        ImportManager: importManager,\n        render,\n        parse,\n        LessError,\n        transformTree,\n        utils,\n        PluginManager,\n        logger\n    };\n\n    // Create a public API\n\n    const ctor = function(t) {\n        return function() {\n            const obj = Object.create(t.prototype);\n            t.apply(obj, Array.prototype.slice.call(arguments, 0));\n            return obj;\n        };\n    };\n    let t;\n    const api = Object.create(initial);\n    for (const n in initial.tree) {\n        /* eslint guard-for-in: 0 */\n        t = initial.tree[n];\n        if (typeof t === 'function') {\n            api[n.toLowerCase()] = ctor(t);\n        }\n        else {\n            api[n] = Object.create(null);\n            for (const o in t) {\n                /* eslint guard-for-in: 0 */\n                api[n][o.toLowerCase()] = ctor(t[o]);\n            }\n        }\n    }\n\n    /**\n     * Some of the functions assume a `this` context of the API object,\n     * which causes it to fail when wrapped for ES6 imports.\n     * \n     * An assumed `this` should be removed in the future.\n     */\n    initial.parse = initial.parse.bind(api);\n    initial.render = initial.render.bind(api);\n\n    return api;\n}\n"]}