{"version": 3, "file": "namespace-value.js", "sourceRoot": "", "sources": ["../../../src/less/tree/namespace-value.js"], "names": [], "mappings": ";;;AAAA,wDAA0B;AAC1B,gEAAkC;AAClC,8DAAgC;AAChC,gEAAkC;AAElC,IAAM,cAAc,GAAG,UAAS,QAAQ,EAAE,OAAO,EAAE,KAAK,EAAE,QAAQ;IAC9D,IAAI,CAAC,KAAK,GAAG,QAAQ,CAAC;IACtB,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC;IACvB,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC;IACpB,IAAI,CAAC,SAAS,GAAG,QAAQ,CAAC;AAC9B,CAAC,CAAC;AAEF,cAAc,CAAC,SAAS,GAAG,MAAM,CAAC,MAAM,CAAC,IAAI,cAAI,EAAE,EAAE;IACjD,IAAI,EAAE,gBAAgB;IAEtB,IAAI,YAAC,OAAO;QACR,IAAI,CAAC,EAAE,IAAI,EAAE,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QAE9C,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YACtC,IAAI,GAAG,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;YAEvB;;;;eAIG;YACH,IAAI,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE;gBACtB,KAAK,GAAG,IAAI,iBAAO,CAAC,CAAC,IAAI,kBAAQ,EAAE,CAAC,EAAE,KAAK,CAAC,CAAC;aAChD;YAED,IAAI,IAAI,KAAK,EAAE,EAAE;gBACb,KAAK,GAAG,KAAK,CAAC,eAAe,EAAE,CAAC;aACnC;iBACI,IAAI,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,KAAK,GAAG,EAAE;gBAC7B,IAAI,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,KAAK,GAAG,EAAE;oBACxB,IAAI,GAAG,WAAI,IAAI,kBAAQ,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,KAAK,CAAE,CAAC;iBACjE;gBACD,IAAI,KAAK,CAAC,SAAS,EAAE;oBACjB,KAAK,GAAG,KAAK,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;iBAChC;gBAED,IAAI,CAAC,KAAK,EAAE;oBACR,MAAM,EAAE,IAAI,EAAE,MAAM;wBAChB,OAAO,EAAE,mBAAY,IAAI,eAAY;wBACrC,QAAQ,EAAE,IAAI,CAAC,QAAQ,EAAE,CAAC,QAAQ;wBAClC,KAAK,EAAE,IAAI,CAAC,QAAQ,EAAE,EAAE,CAAC;iBAChC;aACJ;iBACI;gBACD,IAAI,IAAI,CAAC,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC,KAAK,IAAI,EAAE;oBAC/B,IAAI,GAAG,WAAI,IAAI,kBAAQ,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,KAAK,CAAE,CAAC;iBACjE;qBACI;oBACD,IAAI,GAAG,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,WAAI,IAAI,CAAE,CAAC;iBACrD;gBACD,IAAI,KAAK,CAAC,UAAU,EAAE;oBAClB,KAAK,GAAG,KAAK,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;iBAChC;gBAED,IAAI,CAAC,KAAK,EAAE;oBACR,MAAM,EAAE,IAAI,EAAE,MAAM;wBAChB,OAAO,EAAE,qBAAa,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,iBAAa;wBACjD,QAAQ,EAAE,IAAI,CAAC,QAAQ,EAAE,CAAC,QAAQ;wBAClC,KAAK,EAAE,IAAI,CAAC,QAAQ,EAAE,EAAE,CAAC;iBAChC;gBACD,8EAA8E;gBAC9E,8CAA8C;gBAC9C,KAAK,GAAG,KAAK,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;aACnC;YAED,IAAI,KAAK,CAAC,KAAK,EAAE;gBACb,KAAK,GAAG,KAAK,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,KAAK,CAAC;aACrC;YACD,IAAI,KAAK,CAAC,OAAO,EAAE;gBACf,KAAK,GAAG,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;aACvC;SACJ;QACD,OAAO,KAAK,CAAC;IACjB,CAAC;CACJ,CAAC,CAAC;AAEH,kBAAe,cAAc,CAAC", "sourcesContent": ["import Node from './node';\nimport Variable from './variable';\nimport Ruleset from './ruleset';\nimport Selector from './selector';\n\nconst NamespaceValue = function(ruleCall, lookups, index, fileInfo) {\n    this.value = ruleCall;\n    this.lookups = lookups;\n    this._index = index;\n    this._fileInfo = fileInfo;\n};\n\nNamespaceValue.prototype = Object.assign(new Node(), {\n    type: 'NamespaceValue',\n\n    eval(context) {\n        let i, name, rules = this.value.eval(context);\n        \n        for (i = 0; i < this.lookups.length; i++) {\n            name = this.lookups[i];\n\n            /**\n             * Eval'd DRs return rulesets.\n             * Eval'd mixins return rules, so let's make a ruleset if we need it.\n             * We need to do this because of late parsing of values\n             */\n            if (Array.isArray(rules)) {\n                rules = new Ruleset([new Selector()], rules);\n            }\n\n            if (name === '') {\n                rules = rules.lastDeclaration();\n            }\n            else if (name.charAt(0) === '@') {\n                if (name.charAt(1) === '@') {\n                    name = `@${new Variable(name.substr(1)).eval(context).value}`;\n                }\n                if (rules.variables) {\n                    rules = rules.variable(name);\n                }\n                \n                if (!rules) {\n                    throw { type: 'Name',\n                        message: `variable ${name} not found`,\n                        filename: this.fileInfo().filename,\n                        index: this.getIndex() };\n                }\n            }\n            else {\n                if (name.substring(0, 2) === '$@') {\n                    name = `$${new Variable(name.substr(1)).eval(context).value}`;\n                }\n                else {\n                    name = name.charAt(0) === '$' ? name : `$${name}`;\n                }\n                if (rules.properties) {\n                    rules = rules.property(name);\n                }\n            \n                if (!rules) {\n                    throw { type: 'Name',\n                        message: `property \"${name.substr(1)}\" not found`,\n                        filename: this.fileInfo().filename,\n                        index: this.getIndex() };\n                }\n                // Properties are an array of values, since a ruleset can have multiple props.\n                // We pick the last one (the \"cascaded\" value)\n                rules = rules[rules.length - 1];\n            }\n\n            if (rules.value) {\n                rules = rules.eval(context).value;\n            }\n            if (rules.ruleset) {\n                rules = rules.ruleset.eval(context);\n            }\n        }\n        return rules;\n    }\n});\n\nexport default NamespaceValue;\n"]}