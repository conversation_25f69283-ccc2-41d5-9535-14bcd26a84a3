{"version": 3, "file": "mixin-call.js", "sourceRoot": "", "sources": ["../../../src/less/tree/mixin-call.js"], "names": [], "mappings": ";;;AAAA,wDAA0B;AAC1B,gEAAkC;AAClC,gFAAiD;AACjD,yEAA+C;AAE/C,IAAM,SAAS,GAAG,UAAS,QAAQ,EAAE,IAAI,EAAE,KAAK,EAAE,eAAe,EAAE,SAAS;IACxE,IAAI,CAAC,QAAQ,GAAG,IAAI,kBAAQ,CAAC,QAAQ,CAAC,CAAC;IACvC,IAAI,CAAC,SAAS,GAAG,IAAI,IAAI,EAAE,CAAC;IAC5B,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC;IACpB,IAAI,CAAC,SAAS,GAAG,eAAe,CAAC;IACjC,IAAI,CAAC,SAAS,GAAG,SAAS,CAAC;IAC3B,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;IACtB,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC,CAAC;AACxC,CAAC,CAAC;AAEF,SAAS,CAAC,SAAS,GAAG,MAAM,CAAC,MAAM,CAAC,IAAI,cAAI,EAAE,EAAE;IAC5C,IAAI,EAAE,WAAW;IAEjB,MAAM,YAAC,OAAO;QACV,IAAI,IAAI,CAAC,QAAQ,EAAE;YACf,IAAI,CAAC,QAAQ,GAAG,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;SAChD;QACD,IAAI,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE;YACvB,IAAI,CAAC,SAAS,GAAG,OAAO,CAAC,UAAU,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;SACvD;IACL,CAAC;IAED,IAAI,YAAC,OAAO;QACR,IAAI,MAAM,CAAC;QACX,IAAI,KAAK,CAAC;QACV,IAAI,SAAS,CAAC;QACd,IAAM,IAAI,GAAG,EAAE,CAAC;QAChB,IAAI,GAAG,CAAC;QACR,IAAI,QAAQ,CAAC;QACb,IAAM,KAAK,GAAG,EAAE,CAAC;QACjB,IAAI,KAAK,GAAG,KAAK,CAAC;QAClB,IAAI,CAAC,CAAC;QACN,IAAI,CAAC,CAAC;QACN,IAAI,CAAC,CAAC;QACN,IAAI,WAAW,CAAC;QAChB,IAAI,UAAU,CAAC;QACf,IAAM,UAAU,GAAG,EAAE,CAAC;QACtB,IAAI,SAAS,CAAC;QACd,IAAM,eAAe,GAAG,EAAE,CAAC;QAC3B,IAAI,aAAa,CAAC;QAClB,IAAM,kBAAkB,GAAG,CAAC,CAAC,CAAC;QAC9B,IAAM,OAAO,GAAG,CAAC,CAAC;QAClB,IAAM,OAAO,GAAG,CAAC,CAAC;QAClB,IAAM,QAAQ,GAAG,CAAC,CAAC;QACnB,IAAI,KAAK,CAAC;QACV,IAAI,eAAe,CAAC;QACpB,IAAI,iBAAiB,CAAC;QAEtB,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QAE5C,SAAS,YAAY,CAAC,KAAK,EAAE,SAAS;YAClC,IAAI,CAAC,EAAE,CAAC,EAAE,SAAS,CAAC;YAEpB,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE;gBACpB,eAAe,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC;gBAC1B,iBAAW,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;gBACrB,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,SAAS,CAAC,MAAM,IAAI,eAAe,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE;oBACzD,SAAS,GAAG,SAAS,CAAC,CAAC,CAAC,CAAC;oBACzB,IAAI,SAAS,CAAC,cAAc,EAAE;wBAC1B,eAAe,CAAC,CAAC,CAAC,GAAG,eAAe,CAAC,CAAC,CAAC,IAAI,SAAS,CAAC,cAAc,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;qBACtF;iBACJ;gBACD,IAAI,KAAK,CAAC,cAAc,EAAE;oBACtB,eAAe,CAAC,CAAC,CAAC,GAAG,eAAe,CAAC,CAAC,CAAC,IAAI,KAAK,CAAC,cAAc,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;iBAClF;aACJ;YACD,IAAI,eAAe,CAAC,CAAC,CAAC,IAAI,eAAe,CAAC,CAAC,CAAC,EAAE;gBAC1C,IAAI,eAAe,CAAC,CAAC,CAAC,IAAI,eAAe,CAAC,CAAC,CAAC,EAAE;oBAC1C,OAAO,eAAe,CAAC,CAAC,CAAC,CAAC,CAAC;wBACvB,OAAO,CAAC,CAAC,CAAC,QAAQ,CAAC;iBAC1B;gBAED,OAAO,OAAO,CAAC;aAClB;YACD,OAAO,kBAAkB,CAAC;QAC9B,CAAC;QAED,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YACxC,GAAG,GAAG,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC;YACxB,QAAQ,GAAG,GAAG,CAAC,KAAK,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;YACnC,IAAI,GAAG,CAAC,MAAM,IAAI,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,KAAK,CAAC,EAAE;gBAC7C,QAAQ,GAAG,QAAQ,CAAC,KAAK,CAAC;gBAC1B,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,QAAQ,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;oBAClC,IAAI,CAAC,IAAI,CAAC,EAAC,KAAK,EAAE,QAAQ,CAAC,CAAC,CAAC,EAAC,CAAC,CAAC;iBACnC;aACJ;iBAAM;gBACH,IAAI,CAAC,IAAI,CAAC,EAAC,IAAI,EAAE,GAAG,CAAC,IAAI,EAAE,KAAK,EAAE,QAAQ,EAAC,CAAC,CAAC;aAChD;SACJ;QAED,iBAAiB,GAAG,UAAS,IAAI,IAAG,OAAO,IAAI,CAAC,SAAS,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC,CAAA,CAAC,CAAC;QAE3E,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,OAAO,CAAC,MAAM,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YACxC,IAAI,CAAC,MAAM,GAAG,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,IAAI,EAAE,iBAAiB,CAAC,CAAC,CAAC,MAAM,GAAG,CAAC,EAAE;gBACtF,UAAU,GAAG,IAAI,CAAC;gBAElB,6FAA6F;gBAC7F,+FAA+F;gBAC/F,8FAA8F;gBAC9F,4BAA4B;gBAE5B,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,MAAM,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;oBAChC,KAAK,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC;oBACvB,SAAS,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC;oBAC3B,WAAW,GAAG,KAAK,CAAC;oBACpB,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,OAAO,CAAC,MAAM,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;wBACxC,IAAI,CAAC,CAAC,CAAC,KAAK,YAAY,0BAAe,CAAC,CAAC,IAAI,KAAK,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,eAAe,IAAI,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,EAAE;4BAC7G,WAAW,GAAG,IAAI,CAAC;4BACnB,MAAM;yBACT;qBACJ;oBACD,IAAI,WAAW,EAAE;wBACb,SAAS;qBACZ;oBAED,IAAI,KAAK,CAAC,SAAS,CAAC,IAAI,EAAE,OAAO,CAAC,EAAE;wBAChC,SAAS,GAAG,EAAC,KAAK,OAAA,EAAE,KAAK,EAAE,YAAY,CAAC,KAAK,EAAE,SAAS,CAAC,EAAC,CAAC;wBAE3D,IAAI,SAAS,CAAC,KAAK,KAAK,kBAAkB,EAAE;4BACxC,UAAU,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;yBAC9B;wBAED,KAAK,GAAG,IAAI,CAAC;qBAChB;iBACJ;gBAED,iBAAW,CAAC,KAAK,EAAE,CAAC;gBAEpB,KAAK,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;gBAClB,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,UAAU,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;oBACpC,KAAK,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,EAAE,CAAC;iBAChC;gBAED,IAAI,KAAK,CAAC,OAAO,CAAC,GAAG,CAAC,EAAE;oBACpB,aAAa,GAAG,QAAQ,CAAC;iBAC5B;qBAAM;oBACH,aAAa,GAAG,OAAO,CAAC;oBACxB,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,GAAG,KAAK,CAAC,QAAQ,CAAC,CAAC,GAAG,CAAC,EAAE;wBACxC,MAAM,EAAE,IAAI,EAAE,SAAS;4BACnB,OAAO,EAAE,gEAA4D,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,MAAI;4BAC1F,KAAK,EAAE,IAAI,CAAC,QAAQ,EAAE,EAAE,QAAQ,EAAE,IAAI,CAAC,QAAQ,EAAE,CAAC,QAAQ,EAAE,CAAC;qBACpE;iBACJ;gBAED,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,UAAU,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;oBACpC,SAAS,GAAG,UAAU,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC;oBAChC,IAAI,CAAC,SAAS,KAAK,OAAO,CAAC,IAAI,CAAC,SAAS,KAAK,aAAa,CAAC,EAAE;wBAC1D,IAAI;4BACA,KAAK,GAAG,UAAU,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC;4BAC5B,IAAI,CAAC,CAAC,KAAK,YAAY,0BAAe,CAAC,EAAE;gCACrC,eAAe,GAAG,KAAK,CAAC,eAAe,IAAI,KAAK,CAAC;gCACjD,KAAK,GAAG,IAAI,0BAAe,CAAC,EAAE,EAAE,EAAE,EAAE,KAAK,CAAC,KAAK,EAAE,IAAI,EAAE,KAAK,EAAE,IAAI,EAAE,eAAe,CAAC,cAAc,EAAE,CAAC,CAAC;gCACtG,KAAK,CAAC,eAAe,GAAG,eAAe,CAAC;6BAC3C;4BACD,IAAM,QAAQ,GAAG,KAAK,CAAC,QAAQ,CAAC,OAAO,EAAE,IAAI,EAAE,IAAI,CAAC,SAAS,CAAC,CAAC,KAAK,CAAC;4BACrE,IAAI,CAAC,2BAA2B,CAAC,QAAQ,CAAC,CAAC;4BAC3C,KAAK,CAAC,SAAS,CAAC,IAAI,CAAC,KAAK,CAAC,KAAK,EAAE,QAAQ,CAAC,CAAC;yBAC/C;wBAAC,OAAO,CAAC,EAAE;4BACR,MAAM,EAAE,OAAO,EAAE,CAAC,CAAC,OAAO,EAAE,KAAK,EAAE,IAAI,CAAC,QAAQ,EAAE,EAAE,QAAQ,EAAE,IAAI,CAAC,QAAQ,EAAE,CAAC,QAAQ,EAAE,KAAK,EAAE,CAAC,CAAC,KAAK,EAAE,CAAC;yBAC5G;qBACJ;iBACJ;gBAED,IAAI,KAAK,EAAE;oBACP,OAAO,KAAK,CAAC;iBAChB;aACJ;SACJ;QACD,IAAI,UAAU,EAAE;YACZ,MAAM,EAAE,IAAI,EAAK,SAAS;gBACtB,OAAO,EAAE,gDAA0C,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,MAAI;gBACxE,KAAK,EAAI,IAAI,CAAC,QAAQ,EAAE,EAAE,QAAQ,EAAE,IAAI,CAAC,QAAQ,EAAE,CAAC,QAAQ,EAAE,CAAC;SACtE;aAAM;YACH,MAAM,EAAE,IAAI,EAAK,MAAM;gBACnB,OAAO,EAAE,UAAG,IAAI,CAAC,QAAQ,CAAC,KAAK,EAAE,CAAC,IAAI,EAAE,kBAAe;gBACvD,KAAK,EAAI,IAAI,CAAC,QAAQ,EAAE,EAAE,QAAQ,EAAE,IAAI,CAAC,QAAQ,EAAE,CAAC,QAAQ,EAAE,CAAC;SACtE;IACL,CAAC;IAED,2BAA2B,YAAC,WAAW;QACnC,IAAI,CAAC,EAAE,IAAI,CAAC;QACZ,IAAI,IAAI,CAAC,gBAAgB,EAAE,EAAE;YACzB,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,WAAW,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;gBACrC,IAAI,GAAG,WAAW,CAAC,CAAC,CAAC,CAAC;gBACtB,IAAI,CAAC,kBAAkB,EAAE,CAAC;aAC7B;SACJ;IACL,CAAC;IAED,MAAM,YAAC,IAAI;QACP,OAAO,UAAG,IAAI,CAAC,QAAQ,CAAC,KAAK,EAAE,CAAC,IAAI,EAAE,cAAI,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,UAAU,CAAC;YACjE,IAAI,QAAQ,GAAG,EAAE,CAAC;YAClB,IAAI,CAAC,CAAC,IAAI,EAAE;gBACR,QAAQ,IAAI,UAAG,CAAC,CAAC,IAAI,MAAG,CAAC;aAC5B;YACD,IAAI,CAAC,CAAC,KAAK,CAAC,KAAK,EAAE;gBACf,QAAQ,IAAI,CAAC,CAAC,KAAK,CAAC,KAAK,EAAE,CAAC;aAC/B;iBAAM;gBACH,QAAQ,IAAI,KAAK,CAAC;aACrB;YACD,OAAO,QAAQ,CAAC;QACpB,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,EAAE,MAAG,CAAC;IAC1B,CAAC;CACJ,CAAC,CAAC;AAEH,kBAAe,SAAS,CAAC", "sourcesContent": ["import Node from './node';\nimport Selector from './selector';\nimport MixinDefinition from './mixin-definition';\nimport defaultFunc from '../functions/default';\n\nconst MixinCall = function(elements, args, index, currentFileInfo, important) {\n    this.selector = new Selector(elements);\n    this.arguments = args || [];\n    this._index = index;\n    this._fileInfo = currentFileInfo;\n    this.important = important;\n    this.allowRoot = true;\n    this.setParent(this.selector, this);\n};\n\nMixinCall.prototype = Object.assign(new Node(), {\n    type: 'MixinCall',\n\n    accept(visitor) {\n        if (this.selector) {\n            this.selector = visitor.visit(this.selector);\n        }\n        if (this.arguments.length) {\n            this.arguments = visitor.visitArray(this.arguments);\n        }\n    },\n\n    eval(context) {\n        let mixins;\n        let mixin;\n        let mixinPath;\n        const args = [];\n        let arg;\n        let argValue;\n        const rules = [];\n        let match = false;\n        let i;\n        let m;\n        let f;\n        let isRecursive;\n        let isOneFound;\n        const candidates = [];\n        let candidate;\n        const conditionResult = [];\n        let defaultResult;\n        const defFalseEitherCase = -1;\n        const defNone = 0;\n        const defTrue = 1;\n        const defFalse = 2;\n        let count;\n        let originalRuleset;\n        let noArgumentsFilter;\n\n        this.selector = this.selector.eval(context);\n\n        function calcDefGroup(mixin, mixinPath) {\n            let f, p, namespace;\n\n            for (f = 0; f < 2; f++) {\n                conditionResult[f] = true;\n                defaultFunc.value(f);\n                for (p = 0; p < mixinPath.length && conditionResult[f]; p++) {\n                    namespace = mixinPath[p];\n                    if (namespace.matchCondition) {\n                        conditionResult[f] = conditionResult[f] && namespace.matchCondition(null, context);\n                    }\n                }\n                if (mixin.matchCondition) {\n                    conditionResult[f] = conditionResult[f] && mixin.matchCondition(args, context);\n                }\n            }\n            if (conditionResult[0] || conditionResult[1]) {\n                if (conditionResult[0] != conditionResult[1]) {\n                    return conditionResult[1] ?\n                        defTrue : defFalse;\n                }\n\n                return defNone;\n            }\n            return defFalseEitherCase;\n        }\n\n        for (i = 0; i < this.arguments.length; i++) {\n            arg = this.arguments[i];\n            argValue = arg.value.eval(context);\n            if (arg.expand && Array.isArray(argValue.value)) {\n                argValue = argValue.value;\n                for (m = 0; m < argValue.length; m++) {\n                    args.push({value: argValue[m]});\n                }\n            } else {\n                args.push({name: arg.name, value: argValue});\n            }\n        }\n\n        noArgumentsFilter = function(rule) {return rule.matchArgs(null, context);};\n\n        for (i = 0; i < context.frames.length; i++) {\n            if ((mixins = context.frames[i].find(this.selector, null, noArgumentsFilter)).length > 0) {\n                isOneFound = true;\n\n                // To make `default()` function independent of definition order we have two \"subpasses\" here.\n                // At first we evaluate each guard *twice* (with `default() == true` and `default() == false`),\n                // and build candidate list with corresponding flags. Then, when we know all possible matches,\n                // we make a final decision.\n\n                for (m = 0; m < mixins.length; m++) {\n                    mixin = mixins[m].rule;\n                    mixinPath = mixins[m].path;\n                    isRecursive = false;\n                    for (f = 0; f < context.frames.length; f++) {\n                        if ((!(mixin instanceof MixinDefinition)) && mixin === (context.frames[f].originalRuleset || context.frames[f])) {\n                            isRecursive = true;\n                            break;\n                        }\n                    }\n                    if (isRecursive) {\n                        continue;\n                    }\n\n                    if (mixin.matchArgs(args, context)) {\n                        candidate = {mixin, group: calcDefGroup(mixin, mixinPath)};\n\n                        if (candidate.group !== defFalseEitherCase) {\n                            candidates.push(candidate);\n                        }\n\n                        match = true;\n                    }\n                }\n\n                defaultFunc.reset();\n\n                count = [0, 0, 0];\n                for (m = 0; m < candidates.length; m++) {\n                    count[candidates[m].group]++;\n                }\n\n                if (count[defNone] > 0) {\n                    defaultResult = defFalse;\n                } else {\n                    defaultResult = defTrue;\n                    if ((count[defTrue] + count[defFalse]) > 1) {\n                        throw { type: 'Runtime',\n                            message: `Ambiguous use of \\`default()\\` found when matching for \\`${this.format(args)}\\``,\n                            index: this.getIndex(), filename: this.fileInfo().filename };\n                    }\n                }\n\n                for (m = 0; m < candidates.length; m++) {\n                    candidate = candidates[m].group;\n                    if ((candidate === defNone) || (candidate === defaultResult)) {\n                        try {\n                            mixin = candidates[m].mixin;\n                            if (!(mixin instanceof MixinDefinition)) {\n                                originalRuleset = mixin.originalRuleset || mixin;\n                                mixin = new MixinDefinition('', [], mixin.rules, null, false, null, originalRuleset.visibilityInfo());\n                                mixin.originalRuleset = originalRuleset;\n                            }\n                            const newRules = mixin.evalCall(context, args, this.important).rules;\n                            this._setVisibilityToReplacement(newRules);\n                            Array.prototype.push.apply(rules, newRules);\n                        } catch (e) {\n                            throw { message: e.message, index: this.getIndex(), filename: this.fileInfo().filename, stack: e.stack };\n                        }\n                    }\n                }\n\n                if (match) {\n                    return rules;\n                }\n            }\n        }\n        if (isOneFound) {\n            throw { type:    'Runtime',\n                message: `No matching definition was found for \\`${this.format(args)}\\``,\n                index:   this.getIndex(), filename: this.fileInfo().filename };\n        } else {\n            throw { type:    'Name',\n                message: `${this.selector.toCSS().trim()} is undefined`,\n                index:   this.getIndex(), filename: this.fileInfo().filename };\n        }\n    },\n\n    _setVisibilityToReplacement(replacement) {\n        let i, rule;\n        if (this.blocksVisibility()) {\n            for (i = 0; i < replacement.length; i++) {\n                rule = replacement[i];\n                rule.addVisibilityBlock();\n            }\n        }\n    },\n\n    format(args) {\n        return `${this.selector.toCSS().trim()}(${args ? args.map(function (a) {\n            let argValue = '';\n            if (a.name) {\n                argValue += `${a.name}:`;\n            }\n            if (a.value.toCSS) {\n                argValue += a.value.toCSS();\n            } else {\n                argValue += '???';\n            }\n            return argValue;\n        }).join(', ') : ''})`;\n    }\n});\n\nexport default MixinCall;\n"]}