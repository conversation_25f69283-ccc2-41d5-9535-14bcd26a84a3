{"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["../../src/less-node/index.js"], "names": [], "mappings": ";;;AAAA,sEAAwC;AACxC,wEAAyC;AACzC,gFAAgD;AAChD,yDAA4C;AAC5C,IAAM,IAAI,GAAG,IAAA,cAAqB,EAAC,qBAAW,EAAE,CAAC,IAAI,sBAAW,EAAE,EAAE,IAAI,0BAAc,EAAE,CAAC,CAAC,CAAC;AAC3F,wEAAyC;AAEzC,yDAAyD;AACzD,IAAI,CAAC,qBAAqB,GAAG,cAAqB,CAAC;AACnD,IAAI,CAAC,WAAW,GAAG,sBAAW,CAAC;AAC/B,IAAI,CAAC,YAAY,GAAG,OAAO,CAAC,iBAAiB,CAAC,CAAC,OAAO,CAAC;AACvD,IAAI,CAAC,EAAE,GAAG,OAAO,CAAC,MAAM,CAAC,CAAC,OAAO,CAAC;AAClC,IAAI,CAAC,WAAW,GAAG,sBAAW,CAAC;AAC/B,IAAI,CAAC,cAAc,GAAG,0BAAc,CAAC;AAErC,iBAAiB;AACjB,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC,yBAAyB,CAAC,CAAC,OAAO,EAAE,CAAC;AAE5D,mCAAmC;AACnC,OAAO,CAAC,cAAc,CAAC,CAAC,OAAO,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;AAElD,kBAAe,IAAI,CAAC", "sourcesContent": ["import environment from './environment';\nimport FileManager from './file-manager';\nimport UrlFileManager from './url-file-manager';\nimport createFromEnvironment from '../less';\nconst less = createFromEnvironment(environment, [new FileManager(), new UrlFileManager()]);\nimport lesscHelper from './lessc-helper';\n\n// allow people to create less with their own environment\nless.createFromEnvironment = createFromEnvironment;\nless.lesscHelper = lesscHelper;\nless.PluginLoader = require('./plugin-loader').default;\nless.fs = require('./fs').default;\nless.FileManager = FileManager;\nless.UrlFileManager = UrlFileManager;\n\n// Set up options\nless.options = require('../less/default-options').default();\n\n// provide image-size functionality\nrequire('./image-size').default(less.environment);\n\nexport default less;\n"]}