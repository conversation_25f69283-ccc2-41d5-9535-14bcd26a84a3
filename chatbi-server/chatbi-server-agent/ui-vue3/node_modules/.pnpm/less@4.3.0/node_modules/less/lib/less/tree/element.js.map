{"version": 3, "file": "element.js", "sourceRoot": "", "sources": ["../../../src/less/tree/element.js"], "names": [], "mappings": ";;;AAAA,wDAA0B;AAC1B,0DAA4B;AAC5B,oEAAsC;AAEtC,IAAM,OAAO,GAAG,UAAS,UAAU,EAAE,KAAK,EAAE,UAAU,EAAE,KAAK,EAAE,eAAe,EAAE,cAAc;IAC1F,IAAI,CAAC,UAAU,GAAG,UAAU,YAAY,oBAAU,CAAC,CAAC;QAChD,UAAU,CAAC,CAAC,CAAC,IAAI,oBAAU,CAAC,UAAU,CAAC,CAAC;IAE5C,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE;QAC3B,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC,IAAI,EAAE,CAAC;KAC7B;SAAM,IAAI,KAAK,EAAE;QACd,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;KACtB;SAAM;QACH,IAAI,CAAC,KAAK,GAAG,EAAE,CAAC;KACnB;IACD,IAAI,CAAC,UAAU,GAAG,UAAU,CAAC;IAC7B,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC;IACpB,IAAI,CAAC,SAAS,GAAG,eAAe,CAAC;IACjC,IAAI,CAAC,kBAAkB,CAAC,cAAc,CAAC,CAAC;IACxC,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,UAAU,EAAE,IAAI,CAAC,CAAC;AAC1C,CAAC,CAAA;AAED,OAAO,CAAC,SAAS,GAAG,MAAM,CAAC,MAAM,CAAC,IAAI,cAAI,EAAE,EAAE;IAC1C,IAAI,EAAE,SAAS;IAEf,MAAM,YAAC,OAAO;QACV,IAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC;QACzB,IAAI,CAAC,UAAU,GAAG,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;QACjD,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE;YAC3B,IAAI,CAAC,KAAK,GAAG,OAAO,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;SACrC;IACL,CAAC;IAED,IAAI,YAAC,OAAO;QACR,OAAO,IAAI,OAAO,CAAC,IAAI,CAAC,UAAU,EAC9B,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,EACvD,IAAI,CAAC,UAAU,EACf,IAAI,CAAC,QAAQ,EAAE,EACf,IAAI,CAAC,QAAQ,EAAE,EAAE,IAAI,CAAC,cAAc,EAAE,CAAC,CAAC;IAChD,CAAC;IAED,KAAK;QACD,OAAO,IAAI,OAAO,CAAC,IAAI,CAAC,UAAU,EAC9B,IAAI,CAAC,KAAK,EACV,IAAI,CAAC,UAAU,EACf,IAAI,CAAC,QAAQ,EAAE,EACf,IAAI,CAAC,QAAQ,EAAE,EAAE,IAAI,CAAC,cAAc,EAAE,CAAC,CAAC;IAChD,CAAC;IAED,MAAM,YAAC,OAAO,EAAE,MAAM;QAClB,MAAM,CAAC,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,EAAE,IAAI,CAAC,QAAQ,EAAE,EAAE,IAAI,CAAC,QAAQ,EAAE,CAAC,CAAC;IACtE,CAAC;IAED,KAAK,YAAC,OAAO;QACT,OAAO,GAAG,OAAO,IAAI,EAAE,CAAC;QACxB,IAAI,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC;QACvB,IAAM,aAAa,GAAG,OAAO,CAAC,aAAa,CAAC;QAC5C,IAAI,KAAK,YAAY,eAAK,EAAE;YACxB,8DAA8D;YAC9D,yDAAyD;YACzD,OAAO,CAAC,aAAa,GAAG,IAAI,CAAC;SAChC;QACD,KAAK,GAAG,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC;QACnD,OAAO,CAAC,aAAa,GAAG,aAAa,CAAC;QACtC,IAAI,KAAK,KAAK,EAAE,IAAI,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,KAAK,GAAG,EAAE;YACzD,OAAO,EAAE,CAAC;SACb;aAAM;YACH,OAAO,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,OAAO,CAAC,GAAG,KAAK,CAAC;SACjD;IACL,CAAC;CACJ,CAAC,CAAC;AAEH,kBAAe,OAAO,CAAC", "sourcesContent": ["import Node from './node';\nimport Paren from './paren';\nimport Combinator from './combinator';\n\nconst Element = function(combinator, value, isVariable, index, currentFileInfo, visibilityInfo) {\n    this.combinator = combinator instanceof Combinator ?\n        combinator : new Combinator(combinator);\n\n    if (typeof value === 'string') {\n        this.value = value.trim();\n    } else if (value) {\n        this.value = value;\n    } else {\n        this.value = '';\n    }\n    this.isVariable = isVariable;\n    this._index = index;\n    this._fileInfo = currentFileInfo;\n    this.copyVisibilityInfo(visibilityInfo);\n    this.setParent(this.combinator, this);\n}\n\nElement.prototype = Object.assign(new Node(), {\n    type: 'Element',\n\n    accept(visitor) {\n        const value = this.value;\n        this.combinator = visitor.visit(this.combinator);\n        if (typeof value === 'object') {\n            this.value = visitor.visit(value);\n        }\n    },\n\n    eval(context) {\n        return new Element(this.combinator,\n            this.value.eval ? this.value.eval(context) : this.value,\n            this.isVariable,\n            this.getIndex(),\n            this.fileInfo(), this.visibilityInfo());\n    },\n\n    clone() {\n        return new Element(this.combinator,\n            this.value,\n            this.isVariable,\n            this.getIndex(),\n            this.fileInfo(), this.visibilityInfo());\n    },\n\n    genCSS(context, output) {\n        output.add(this.toCSS(context), this.fileInfo(), this.getIndex());\n    },\n\n    toCSS(context) {\n        context = context || {};\n        let value = this.value;\n        const firstSelector = context.firstSelector;\n        if (value instanceof Paren) {\n            // selector in parens should not be affected by outer selector\n            // flags (breaks only interpolated selectors - see #1973)\n            context.firstSelector = true;\n        }\n        value = value.toCSS ? value.toCSS(context) : value;\n        context.firstSelector = firstSelector;\n        if (value === '' && this.combinator.value.charAt(0) === '&') {\n            return '';\n        } else {\n            return this.combinator.toCSS(context) + value;\n        }\n    }\n});\n\nexport default Element;\n"]}