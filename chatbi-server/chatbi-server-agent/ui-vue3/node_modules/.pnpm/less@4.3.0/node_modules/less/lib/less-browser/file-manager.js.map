{"version": 3, "file": "file-manager.js", "sourceRoot": "", "sources": ["../../src/less-browser/file-manager.js"], "names": [], "mappings": ";;;AAAA,kHAA+E;AAE/E,IAAI,OAAO,CAAC;AACZ,IAAI,MAAM,CAAC;AACX,IAAI,SAAS,GAAG,EAAE,CAAC;AAEnB,wIAAwI;AACxI,IAAM,WAAW,GAAG,cAAY,CAAC,CAAA;AACjC,WAAW,CAAC,SAAS,GAAG,MAAM,CAAC,MAAM,CAAC,IAAI,kCAAmB,EAAE,EAAE;IAC7D,uBAAuB;QACnB,OAAO,IAAI,CAAC;IAChB,CAAC;IAED,IAAI,YAAC,QAAQ,EAAE,SAAS;QACpB,IAAI,CAAC,QAAQ,EAAE;YACX,OAAO,SAAS,CAAC;SACpB;QACD,OAAO,IAAI,CAAC,eAAe,CAAC,SAAS,EAAE,QAAQ,CAAC,CAAC,IAAI,CAAC;IAC1D,CAAC;IAED,KAAK,YAAC,GAAG,EAAE,IAAI,EAAE,QAAQ,EAAE,OAAO;QAC9B,IAAM,GAAG,GAAG,IAAI,cAAc,EAAE,CAAC;QACjC,IAAM,KAAK,GAAG,OAAO,CAAC,cAAc,CAAC,CAAC,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC;QAEhE,IAAI,OAAO,GAAG,CAAC,gBAAgB,KAAK,UAAU,EAAE;YAC5C,GAAG,CAAC,gBAAgB,CAAC,UAAU,CAAC,CAAC;SACpC;QACD,MAAM,CAAC,KAAK,CAAC,wBAAiB,GAAG,MAAG,CAAC,CAAC;QACtC,GAAG,CAAC,IAAI,CAAC,KAAK,EAAE,GAAG,EAAE,KAAK,CAAC,CAAC;QAC5B,GAAG,CAAC,gBAAgB,CAAC,QAAQ,EAAE,IAAI,IAAI,0CAA0C,CAAC,CAAC;QACnF,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAEf,SAAS,cAAc,CAAC,GAAG,EAAE,QAAQ,EAAE,OAAO;YAC1C,IAAI,GAAG,CAAC,MAAM,IAAI,GAAG,IAAI,GAAG,CAAC,MAAM,GAAG,GAAG,EAAE;gBACvC,QAAQ,CAAC,GAAG,CAAC,YAAY,EACrB,GAAG,CAAC,iBAAiB,CAAC,eAAe,CAAC,CAAC,CAAC;aAC/C;iBAAM,IAAI,OAAO,OAAO,KAAK,UAAU,EAAE;gBACtC,OAAO,CAAC,GAAG,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC;aAC5B;QACL,CAAC;QAED,IAAI,OAAO,CAAC,cAAc,IAAI,CAAC,OAAO,CAAC,SAAS,EAAE;YAC9C,IAAI,GAAG,CAAC,MAAM,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,MAAM,IAAI,GAAG,IAAI,GAAG,CAAC,MAAM,GAAG,GAAG,CAAC,EAAE;gBAC7D,QAAQ,CAAC,GAAG,CAAC,YAAY,CAAC,CAAC;aAC9B;iBAAM;gBACH,OAAO,CAAC,GAAG,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC;aAC5B;SACJ;aAAM,IAAI,KAAK,EAAE;YACd,GAAG,CAAC,kBAAkB,GAAG;gBACrB,IAAI,GAAG,CAAC,UAAU,IAAI,CAAC,EAAE;oBACrB,cAAc,CAAC,GAAG,EAAE,QAAQ,EAAE,OAAO,CAAC,CAAC;iBAC1C;YACL,CAAC,CAAC;SACL;aAAM;YACH,cAAc,CAAC,GAAG,EAAE,QAAQ,EAAE,OAAO,CAAC,CAAC;SAC1C;IACL,CAAC;IAED,QAAQ;QACJ,OAAO,IAAI,CAAC;IAChB,CAAC;IAED,cAAc;QACV,SAAS,GAAG,EAAE,CAAC;IACnB,CAAC;IAED,QAAQ,YAAC,QAAQ,EAAE,gBAAgB,EAAE,OAAO;QACxC,2CAA2C;QAC3C,6BAA6B;QAE7B,IAAI,gBAAgB,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,QAAQ,CAAC,EAAE;YACpD,QAAQ,GAAG,gBAAgB,GAAG,QAAQ,CAAC;SAC1C;QAED,QAAQ,GAAG,OAAO,CAAC,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC,kBAAkB,CAAC,QAAQ,EAAE,OAAO,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC;QAEnF,OAAO,GAAG,OAAO,IAAI,EAAE,CAAC;QAExB,kGAAkG;QAClG,qCAAqC;QACrC,IAAM,SAAS,GAAG,IAAI,CAAC,eAAe,CAAC,QAAQ,EAAE,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;QACvE,IAAM,IAAI,GAAQ,SAAS,CAAC,GAAG,CAAC;QAChC,IAAM,IAAI,GAAQ,IAAI,CAAC;QAEvB,OAAO,IAAI,OAAO,CAAC,UAAC,OAAO,EAAE,MAAM;YAC/B,IAAI,OAAO,CAAC,YAAY,IAAI,SAAS,CAAC,IAAI,CAAC,EAAE;gBACzC,IAAI;oBACA,IAAM,QAAQ,GAAG,SAAS,CAAC,IAAI,CAAC,CAAC;oBACjC,OAAO,OAAO,CAAC,EAAE,QAAQ,EAAE,QAAQ,EAAE,QAAQ,EAAE,IAAI,EAAE,OAAO,EAAE,EAAE,YAAY,EAAE,IAAI,IAAI,EAAE,EAAE,EAAC,CAAC,CAAC;iBAChG;gBAAC,OAAO,CAAC,EAAE;oBACR,OAAO,MAAM,CAAC,EAAE,QAAQ,EAAE,IAAI,EAAE,OAAO,EAAE,6BAAsB,IAAI,wBAAc,CAAC,CAAC,OAAO,CAAE,EAAE,CAAC,CAAC;iBACnG;aACJ;YAED,IAAI,CAAC,KAAK,CAAC,IAAI,EAAE,OAAO,CAAC,IAAI,EAAE,SAAS,aAAa,CAAC,IAAI,EAAE,YAAY;gBACpE,iBAAiB;gBACjB,SAAS,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC;gBAEvB,6BAA6B;gBAC7B,OAAO,CAAC,EAAE,QAAQ,EAAE,IAAI,EAAE,QAAQ,EAAE,IAAI,EAAE,OAAO,EAAE,EAAE,YAAY,cAAA,EAAE,EAAC,CAAC,CAAC;YAC1E,CAAC,EAAE,SAAS,UAAU,CAAC,MAAM,EAAE,GAAG;gBAC9B,MAAM,CAAC,EAAE,IAAI,EAAE,MAAM,EAAE,OAAO,EAAE,WAAI,GAAG,6BAAmB,MAAM,MAAG,EAAE,IAAI,MAAA,EAAE,CAAC,CAAC;YACjF,CAAC,CAAC,CAAC;QACP,CAAC,CAAC,CAAC;IACP,CAAC;CACJ,CAAC,CAAC;AAEH,mBAAe,UAAC,IAAI,EAAE,GAAG;IACrB,OAAO,GAAG,IAAI,CAAC;IACf,MAAM,GAAG,GAAG,CAAC;IACb,OAAO,WAAW,CAAC;AACvB,CAAC,EAAA", "sourcesContent": ["import AbstractFileManager from '../less/environment/abstract-file-manager.js';\n\nlet options;\nlet logger;\nlet fileCache = {};\n\n// TODOS - move log somewhere. pathDiff and doing something similar in node. use pathDiff in the other browser file for the initial load\nconst FileManager = function() {}\nFileManager.prototype = Object.assign(new AbstractFileManager(), {\n    alwaysMakePathsAbsolute() {\n        return true;\n    },\n\n    join(basePath, laterPath) {\n        if (!basePath) {\n            return laterPath;\n        }\n        return this.extractUrlParts(laterPath, basePath).path;\n    },\n\n    doXHR(url, type, callback, errback) {\n        const xhr = new XMLHttpRequest();\n        const async = options.isFileProtocol ? options.fileAsync : true;\n\n        if (typeof xhr.overrideMimeType === 'function') {\n            xhr.overrideMimeType('text/css');\n        }\n        logger.debug(`XHR: Getting '${url}'`);\n        xhr.open('GET', url, async);\n        xhr.setRequestHeader('Accept', type || 'text/x-less, text/css; q=0.9, */*; q=0.5');\n        xhr.send(null);\n\n        function handleResponse(xhr, callback, errback) {\n            if (xhr.status >= 200 && xhr.status < 300) {\n                callback(xhr.responseText,\n                    xhr.getResponseHeader('Last-Modified'));\n            } else if (typeof errback === 'function') {\n                errback(xhr.status, url);\n            }\n        }\n\n        if (options.isFileProtocol && !options.fileAsync) {\n            if (xhr.status === 0 || (xhr.status >= 200 && xhr.status < 300)) {\n                callback(xhr.responseText);\n            } else {\n                errback(xhr.status, url);\n            }\n        } else if (async) {\n            xhr.onreadystatechange = () => {\n                if (xhr.readyState == 4) {\n                    handleResponse(xhr, callback, errback);\n                }\n            };\n        } else {\n            handleResponse(xhr, callback, errback);\n        }\n    },\n\n    supports() {\n        return true;\n    },\n\n    clearFileCache() {\n        fileCache = {};\n    },\n\n    loadFile(filename, currentDirectory, options) {\n        // TODO: Add prefix support like less-node?\n        // What about multiple paths?\n\n        if (currentDirectory && !this.isPathAbsolute(filename)) {\n            filename = currentDirectory + filename;\n        }\n\n        filename = options.ext ? this.tryAppendExtension(filename, options.ext) : filename;\n\n        options = options || {};\n\n        // sheet may be set to the stylesheet for the initial load or a collection of properties including\n        // some context variables for imports\n        const hrefParts = this.extractUrlParts(filename, window.location.href);\n        const href      = hrefParts.url;\n        const self      = this;\n        \n        return new Promise((resolve, reject) => {\n            if (options.useFileCache && fileCache[href]) {\n                try {\n                    const lessText = fileCache[href];\n                    return resolve({ contents: lessText, filename: href, webInfo: { lastModified: new Date() }});\n                } catch (e) {\n                    return reject({ filename: href, message: `Error loading file ${href} error was ${e.message}` });\n                }\n            }\n\n            self.doXHR(href, options.mime, function doXHRCallback(data, lastModified) {\n                // per file cache\n                fileCache[href] = data;\n\n                // Use remote copy (re-parse)\n                resolve({ contents: data, filename: href, webInfo: { lastModified }});\n            }, function doXHRError(status, url) {\n                reject({ type: 'File', message: `'${url}' wasn't found (${status})`, href });\n            });\n        });\n    }\n});\n\nexport default (opts, log) => {\n    options = opts;\n    logger = log;\n    return FileManager;\n}\n"]}