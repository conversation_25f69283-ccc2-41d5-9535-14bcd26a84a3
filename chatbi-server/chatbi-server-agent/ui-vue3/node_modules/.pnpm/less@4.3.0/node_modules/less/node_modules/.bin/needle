#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*) basedir=`cygpath -w "$basedir"`;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/Users/<USER>/projects/chatbi/chatbi-server/chatbi-server-agent/ui-vue3/node_modules/.pnpm/needle@3.3.1/node_modules/needle/bin/node_modules:/Users/<USER>/projects/chatbi/chatbi-server/chatbi-server-agent/ui-vue3/node_modules/.pnpm/needle@3.3.1/node_modules/needle/node_modules:/Users/<USER>/projects/chatbi/chatbi-server/chatbi-server-agent/ui-vue3/node_modules/.pnpm/needle@3.3.1/node_modules:/Users/<USER>/projects/chatbi/chatbi-server/chatbi-server-agent/ui-vue3/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/Users/<USER>/projects/chatbi/chatbi-server/chatbi-server-agent/ui-vue3/node_modules/.pnpm/needle@3.3.1/node_modules/needle/bin/node_modules:/Users/<USER>/projects/chatbi/chatbi-server/chatbi-server-agent/ui-vue3/node_modules/.pnpm/needle@3.3.1/node_modules/needle/node_modules:/Users/<USER>/projects/chatbi/chatbi-server/chatbi-server-agent/ui-vue3/node_modules/.pnpm/needle@3.3.1/node_modules:/Users/<USER>/projects/chatbi/chatbi-server/chatbi-server-agent/ui-vue3/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../../../../../needle@3.3.1/node_modules/needle/bin/needle" "$@"
else
  exec node  "$basedir/../../../../../needle@3.3.1/node_modules/needle/bin/needle" "$@"
fi
