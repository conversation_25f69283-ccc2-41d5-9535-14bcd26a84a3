{"version": 3, "file": "image-size.js", "sourceRoot": "", "sources": ["../../src/less-node/image-size.js"], "names": [], "mappings": ";;;AAAA,6EAA+C;AAC/C,+EAAiD;AACjD,oGAAqE;AAErE,mBAAe,UAAA,WAAW;IAEtB,SAAS,SAAS,CAAC,eAAe,EAAE,YAAY;QAC5C,IAAI,QAAQ,GAAG,YAAY,CAAC,KAAK,CAAC;QAClC,IAAM,eAAe,GAAG,eAAe,CAAC,eAAe,CAAC;QACxD,IAAM,gBAAgB,GAAG,eAAe,CAAC,WAAW,CAAC,CAAC;YAClD,eAAe,CAAC,gBAAgB,CAAC,CAAC,CAAC,eAAe,CAAC,SAAS,CAAC;QAEjE,IAAM,aAAa,GAAG,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC;QAC5C,IAAI,aAAa,KAAK,CAAC,CAAC,EAAE;YACtB,QAAQ,GAAG,QAAQ,CAAC,KAAK,CAAC,CAAC,EAAE,aAAa,CAAC,CAAC;SAC/C;QAED,IAAM,WAAW,GAAG,WAAW,CAAC,cAAc,CAAC,QAAQ,EAAE,gBAAgB,EAAE,eAAe,CAAC,OAAO,EAAE,WAAW,EAAE,IAAI,CAAC,CAAC;QAEvH,IAAI,CAAC,WAAW,EAAE;YACd,MAAM;gBACF,IAAI,EAAE,MAAM;gBACZ,OAAO,EAAE,yCAAkC,YAAY,CAAE;aAC5D,CAAC;SACL;QAED,IAAM,QAAQ,GAAG,WAAW,CAAC,YAAY,CAAC,QAAQ,EAAE,gBAAgB,EAAE,eAAe,CAAC,OAAO,EAAE,WAAW,CAAC,CAAC;QAE5G,IAAI,QAAQ,CAAC,KAAK,EAAE;YAChB,MAAM,QAAQ,CAAC,KAAK,CAAC;SACxB;QAED,IAAM,MAAM,GAAG,OAAO,CAAC,YAAY,CAAC,CAAC;QACrC,OAAO,MAAM,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;IACrC,CAAC;IAED,IAAM,cAAc,GAAG;QACnB,YAAY,EAAE,UAAS,YAAY;YAC/B,IAAM,IAAI,GAAG,SAAS,CAAC,IAAI,EAAE,YAAY,CAAC,CAAC;YAC3C,OAAO,IAAI,oBAAU,CAAC;gBAClB,IAAI,mBAAS,CAAC,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC;gBAC/B,IAAI,mBAAS,CAAC,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC;aACnC,CAAC,CAAC;QACP,CAAC;QACD,aAAa,EAAE,UAAS,YAAY;YAChC,IAAM,IAAI,GAAG,SAAS,CAAC,IAAI,EAAE,YAAY,CAAC,CAAC;YAC3C,OAAO,IAAI,mBAAS,CAAC,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC;QAC3C,CAAC;QACD,cAAc,EAAE,UAAS,YAAY;YACjC,IAAM,IAAI,GAAG,SAAS,CAAC,IAAI,EAAE,YAAY,CAAC,CAAC;YAC3C,OAAO,IAAI,mBAAS,CAAC,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC;QAC5C,CAAC;KACJ,CAAC;IAEF,2BAAgB,CAAC,WAAW,CAAC,cAAc,CAAC,CAAC;AACjD,CAAC,EAAC", "sourcesContent": ["import Dimension from '../less/tree/dimension';\nimport Expression from '../less/tree/expression';\nimport functionRegistry from './../less/functions/function-registry';\n\nexport default environment => {\n\n    function imageSize(functionContext, filePathNode) {\n        let filePath = filePathNode.value;\n        const currentFileInfo = functionContext.currentFileInfo;\n        const currentDirectory = currentFileInfo.rewriteUrls ?\n            currentFileInfo.currentDirectory : currentFileInfo.entryPath;\n\n        const fragmentStart = filePath.indexOf('#');\n        if (fragmentStart !== -1) {\n            filePath = filePath.slice(0, fragmentStart);\n        }\n\n        const fileManager = environment.getFileManager(filePath, currentDirectory, functionContext.context, environment, true);\n\n        if (!fileManager) {\n            throw {\n                type: 'File',\n                message: `Can not set up FileManager for ${filePathNode}`\n            };\n        }\n\n        const fileSync = fileManager.loadFileSync(filePath, currentDirectory, functionContext.context, environment);\n\n        if (fileSync.error) {\n            throw fileSync.error;\n        }\n\n        const sizeOf = require('image-size');\n        return sizeOf(fileSync.filename);\n    }\n\n    const imageFunctions = {\n        'image-size': function(filePathNode) {\n            const size = imageSize(this, filePathNode);\n            return new Expression([\n                new Dimension(size.width, 'px'),\n                new Dimension(size.height, 'px')\n            ]);\n        },\n        'image-width': function(filePathNode) {\n            const size = imageSize(this, filePathNode);\n            return new Dimension(size.width, 'px');\n        },\n        'image-height': function(filePathNode) {\n            const size = imageSize(this, filePathNode);\n            return new Dimension(size.height, 'px');\n        }\n    };\n\n    functionRegistry.addMultiple(imageFunctions);\n};\n"]}