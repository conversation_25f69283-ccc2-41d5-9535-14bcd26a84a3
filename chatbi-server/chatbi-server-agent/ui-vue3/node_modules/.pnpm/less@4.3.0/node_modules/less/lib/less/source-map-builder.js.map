{"version": 3, "file": "source-map-builder.js", "sourceRoot": "", "sources": ["../../src/less/source-map-builder.js"], "names": [], "mappings": ";;AAAA,mBAAyB,eAAe,EAAE,WAAW;IACjD;QACI,0BAAY,OAAO;YACf,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC;QAC3B,CAAC;QAED,gCAAK,GAAL,UAAM,QAAQ,EAAE,OAAO,EAAE,OAAO;YAC5B,IAAM,eAAe,GAAG,IAAI,eAAe,CACvC;gBACI,uBAAuB,EAAE,OAAO,CAAC,oBAAoB;gBACrD,QAAQ,UAAA;gBACR,WAAW,EAAE,OAAO,CAAC,QAAQ;gBAC7B,iBAAiB,EAAE,IAAI,CAAC,OAAO,CAAC,iBAAiB;gBACjD,YAAY,EAAE,IAAI,CAAC,OAAO,CAAC,YAAY;gBACvC,cAAc,EAAE,IAAI,CAAC,OAAO,CAAC,uBAAuB;gBACpD,iBAAiB,EAAE,IAAI,CAAC,OAAO,CAAC,iBAAiB;gBACjD,iBAAiB,EAAE,IAAI,CAAC,OAAO,CAAC,iBAAiB;gBACjD,iBAAiB,EAAE,IAAI,CAAC,OAAO,CAAC,iBAAiB;gBACjD,kBAAkB,EAAE,IAAI,CAAC,OAAO,CAAC,kBAAkB;gBACnD,mBAAmB,EAAE,IAAI,CAAC,OAAO,CAAC,mBAAmB;gBACrD,0BAA0B,EAAE,IAAI,CAAC,OAAO,CAAC,0BAA0B;aACtE,CAAC,CAAC;YAEP,IAAM,GAAG,GAAG,eAAe,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;YAC3C,IAAI,CAAC,SAAS,GAAG,eAAe,CAAC,SAAS,CAAC;YAC3C,IAAI,CAAC,YAAY,GAAG,eAAe,CAAC,YAAY,CAAC;YACjD,IAAI,IAAI,CAAC,OAAO,CAAC,sBAAsB,EAAE;gBACrC,IAAI,CAAC,sBAAsB,GAAG,eAAe,CAAC,iBAAiB,CAAC,IAAI,CAAC,OAAO,CAAC,sBAAsB,CAAC,CAAC;aACxG;YACD,IAAI,IAAI,CAAC,OAAO,CAAC,iBAAiB,KAAK,SAAS,IAAI,IAAI,CAAC,YAAY,KAAK,SAAS,EAAE;gBACjF,IAAI,CAAC,YAAY,GAAG,eAAe,CAAC,cAAc,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;aACzE;YACD,OAAO,GAAG,GAAG,IAAI,CAAC,eAAe,EAAE,CAAC;QACxC,CAAC;QAED,0CAAe,GAAf;YAEI,IAAI,YAAY,GAAG,IAAI,CAAC,YAAY,CAAC;YACrC,IAAI,IAAI,CAAC,OAAO,CAAC,mBAAmB,EAAE;gBAClC,IAAI,IAAI,CAAC,SAAS,KAAK,SAAS,EAAE;oBAC9B,OAAO,EAAE,CAAC;iBACb;gBACD,YAAY,GAAG,uCAAgC,WAAW,CAAC,YAAY,CAAC,IAAI,CAAC,SAAS,CAAC,CAAE,CAAC;aAC7F;YAED,IAAI,IAAI,CAAC,OAAO,CAAC,0BAA0B,EAAE;gBACzC,OAAO,EAAE,CAAC;aACb;YAED,IAAI,YAAY,EAAE;gBACd,OAAO,+BAAwB,YAAY,QAAK,CAAC;aACpD;YACD,OAAO,EAAE,CAAC;QACd,CAAC;QAED,+CAAoB,GAApB;YACI,OAAO,IAAI,CAAC,SAAS,CAAC;QAC1B,CAAC;QAED,+CAAoB,GAApB,UAAqB,SAAS;YAC1B,IAAI,CAAC,SAAS,GAAG,SAAS,CAAC;QAC/B,CAAC;QAED,mCAAQ,GAAR;YACI,OAAO,IAAI,CAAC,OAAO,CAAC,mBAAmB,CAAC;QAC5C,CAAC;QAED,0CAAe,GAAf;YACI,OAAO,IAAI,CAAC,YAAY,CAAC;QAC7B,CAAC;QAED,4CAAiB,GAAjB;YACI,OAAO,IAAI,CAAC,OAAO,CAAC,uBAAuB,CAAC;QAChD,CAAC;QAED,2CAAgB,GAAhB;YACI,OAAO,IAAI,CAAC,sBAAsB,CAAC;QACvC,CAAC;QACL,uBAAC;IAAD,CAAC,AA7ED,IA6EC;IAED,OAAO,gBAAgB,CAAC;AAC5B,CAAC;AAjFD,4BAiFC", "sourcesContent": ["export default function (SourceMapOutput, environment) {\n    class SourceMapBuilder {\n        constructor(options) {\n            this.options = options;\n        }\n\n        toCSS(rootNode, options, imports) {\n            const sourceMapOutput = new SourceMapOutput(\n                {\n                    contentsIgnoredCharsMap: imports.contentsIgnoredChars,\n                    rootNode,\n                    contentsMap: imports.contents,\n                    sourceMapFilename: this.options.sourceMapFilename,\n                    sourceMapURL: this.options.sourceMapURL,\n                    outputFilename: this.options.sourceMapOutputFilename,\n                    sourceMapBasepath: this.options.sourceMapBasepath,\n                    sourceMapRootpath: this.options.sourceMapRootpath,\n                    outputSourceFiles: this.options.outputSourceFiles,\n                    sourceMapGenerator: this.options.sourceMapGenerator,\n                    sourceMapFileInline: this.options.sourceMapFileInline,    \n                    disableSourcemapAnnotation: this.options.disableSourcemapAnnotation\n                });\n\n            const css = sourceMapOutput.toCSS(options);\n            this.sourceMap = sourceMapOutput.sourceMap;\n            this.sourceMapURL = sourceMapOutput.sourceMapURL;\n            if (this.options.sourceMapInputFilename) {\n                this.sourceMapInputFilename = sourceMapOutput.normalizeFilename(this.options.sourceMapInputFilename);\n            }\n            if (this.options.sourceMapBasepath !== undefined && this.sourceMapURL !== undefined) {\n                this.sourceMapURL = sourceMapOutput.removeBasepath(this.sourceMapURL);\n            }\n            return css + this.getCSSAppendage();\n        }\n\n        getCSSAppendage() {\n\n            let sourceMapURL = this.sourceMapURL;\n            if (this.options.sourceMapFileInline) {\n                if (this.sourceMap === undefined) {\n                    return '';\n                }\n                sourceMapURL = `data:application/json;base64,${environment.encodeBase64(this.sourceMap)}`;\n            }\n\n            if (this.options.disableSourcemapAnnotation) {\n                return '';\n            }\n\n            if (sourceMapURL) {\n                return `/*# sourceMappingURL=${sourceMapURL} */`;\n            }\n            return '';\n        }\n\n        getExternalSourceMap() {\n            return this.sourceMap;\n        }\n\n        setExternalSourceMap(sourceMap) {\n            this.sourceMap = sourceMap;\n        }\n\n        isInline() {\n            return this.options.sourceMapFileInline;\n        }\n\n        getSourceMapURL() {\n            return this.sourceMapURL;\n        }\n\n        getOutputFilename() {\n            return this.options.sourceMapOutputFilename;\n        }\n\n        getInputFilename() {\n            return this.sourceMapInputFilename;\n        }\n    }\n\n    return SourceMapBuilder;\n}\n"]}