{"version": 3, "file": "url.js", "sourceRoot": "", "sources": ["../../../src/less/tree/url.js"], "names": [], "mappings": ";;;AAAA,wDAA0B;AAE1B,SAAS,UAAU,CAAC,IAAI;IACpB,OAAO,IAAI,CAAC,OAAO,CAAC,WAAW,EAAE,UAAS,KAAK,IAAI,OAAO,YAAK,KAAK,CAAE,CAAC,CAAC,CAAC,CAAC,CAAC;AAC/E,CAAC;AAED,IAAM,GAAG,GAAG,UAAS,GAAG,EAAE,KAAK,EAAE,eAAe,EAAE,OAAO;IACrD,IAAI,CAAC,KAAK,GAAG,GAAG,CAAC;IACjB,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC;IACpB,IAAI,CAAC,SAAS,GAAG,eAAe,CAAC;IACjC,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC;AAC3B,CAAC,CAAC;AAEF,GAAG,CAAC,SAAS,GAAG,MAAM,CAAC,MAAM,CAAC,IAAI,cAAI,EAAE,EAAE;IACtC,IAAI,EAAE,KAAK;IAEX,MAAM,YAAC,OAAO;QACV,IAAI,CAAC,KAAK,GAAG,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;IAC3C,CAAC;IAED,MAAM,YAAC,OAAO,EAAE,MAAM;QAClB,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;QACnB,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC;QACnC,MAAM,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;IACpB,CAAC;IAED,IAAI,YAAC,OAAO;QACR,IAAM,GAAG,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QACrC,IAAI,QAAQ,CAAC;QAEb,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE;YACf,iDAAiD;YACjD,QAAQ,GAAG,IAAI,CAAC,QAAQ,EAAE,IAAI,IAAI,CAAC,QAAQ,EAAE,CAAC,QAAQ,CAAC;YACvD,IAAI,OAAO,QAAQ,KAAK,QAAQ;gBAC5B,OAAO,GAAG,CAAC,KAAK,KAAK,QAAQ;gBAC7B,OAAO,CAAC,mBAAmB,CAAC,GAAG,CAAC,KAAK,CAAC,EAAE;gBACxC,IAAI,CAAC,GAAG,CAAC,KAAK,EAAE;oBACZ,QAAQ,GAAG,UAAU,CAAC,QAAQ,CAAC,CAAC;iBACnC;gBACD,GAAG,CAAC,KAAK,GAAG,OAAO,CAAC,WAAW,CAAC,GAAG,CAAC,KAAK,EAAE,QAAQ,CAAC,CAAC;aACxD;iBAAM;gBACH,GAAG,CAAC,KAAK,GAAG,OAAO,CAAC,aAAa,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;aAChD;YAED,0BAA0B;YAC1B,IAAI,OAAO,CAAC,OAAO,EAAE;gBACjB,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,KAAK,CAAC,WAAW,CAAC,EAAE;oBAC/B,IAAM,SAAS,GAAG,GAAG,CAAC,KAAK,CAAC,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC;oBAC5D,IAAM,OAAO,GAAG,SAAS,GAAG,OAAO,CAAC,OAAO,CAAC;oBAC5C,IAAI,GAAG,CAAC,KAAK,CAAC,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,EAAE;wBAC/B,GAAG,CAAC,KAAK,GAAG,GAAG,CAAC,KAAK,CAAC,OAAO,CAAC,GAAG,EAAE,UAAG,OAAO,MAAG,CAAC,CAAC;qBACrD;yBAAM;wBACH,GAAG,CAAC,KAAK,IAAI,OAAO,CAAC;qBACxB;iBACJ;aACJ;SACJ;QAED,OAAO,IAAI,GAAG,CAAC,GAAG,EAAE,IAAI,CAAC,QAAQ,EAAE,EAAE,IAAI,CAAC,QAAQ,EAAE,EAAE,IAAI,CAAC,CAAC;IAChE,CAAC;CACJ,CAAC,CAAC;AAEH,kBAAe,GAAG,CAAC", "sourcesContent": ["import Node from './node';\n\nfunction escapePath(path) {\n    return path.replace(/[()'\"\\s]/g, function(match) { return `\\\\${match}`; });\n}\n\nconst URL = function(val, index, currentFileInfo, isEvald) {\n    this.value = val;\n    this._index = index;\n    this._fileInfo = currentFileInfo;\n    this.isEvald = isEvald;\n};\n\nURL.prototype = Object.assign(new Node(), {\n    type: 'Url',\n\n    accept(visitor) {\n        this.value = visitor.visit(this.value);\n    },\n\n    genCSS(context, output) {\n        output.add('url(');\n        this.value.genCSS(context, output);\n        output.add(')');\n    },\n\n    eval(context) {\n        const val = this.value.eval(context);\n        let rootpath;\n\n        if (!this.isEvald) {\n            // Add the rootpath if the URL requires a rewrite\n            rootpath = this.fileInfo() && this.fileInfo().rootpath;\n            if (typeof rootpath === 'string' &&\n                typeof val.value === 'string' &&\n                context.pathRequiresRewrite(val.value)) {\n                if (!val.quote) {\n                    rootpath = escapePath(rootpath);\n                }\n                val.value = context.rewritePath(val.value, rootpath);\n            } else {\n                val.value = context.normalizePath(val.value);\n            }\n\n            // Add url args if enabled\n            if (context.urlArgs) {\n                if (!val.value.match(/^\\s*data:/)) {\n                    const delimiter = val.value.indexOf('?') === -1 ? '?' : '&';\n                    const urlArgs = delimiter + context.urlArgs;\n                    if (val.value.indexOf('#') !== -1) {\n                        val.value = val.value.replace('#', `${urlArgs}#`);\n                    } else {\n                        val.value += urlArgs;\n                    }\n                }\n            }\n        }\n\n        return new URL(val, this.getIndex(), this.fileInfo(), true);\n    }\n});\n\nexport default URL;\n"]}