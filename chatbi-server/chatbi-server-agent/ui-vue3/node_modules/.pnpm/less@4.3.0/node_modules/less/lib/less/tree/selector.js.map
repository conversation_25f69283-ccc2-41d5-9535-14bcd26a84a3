{"version": 3, "file": "selector.js", "sourceRoot": "", "sources": ["../../../src/less/tree/selector.js"], "names": [], "mappings": ";;;AAAA,wDAA0B;AAC1B,8DAAgC;AAChC,qEAAsC;AACtC,sDAAkC;AAClC,oEAAsC;AAEtC,IAAM,QAAQ,GAAG,UAAS,QAAQ,EAAE,UAAU,EAAE,SAAS,EAAE,KAAK,EAAE,eAAe,EAAE,cAAc;IAC7F,IAAI,CAAC,UAAU,GAAG,UAAU,CAAC;IAC7B,IAAI,CAAC,SAAS,GAAG,SAAS,CAAC;IAC3B,IAAI,CAAC,cAAc,GAAG,CAAC,SAAS,CAAC;IACjC,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC;IACpB,IAAI,CAAC,SAAS,GAAG,eAAe,CAAC;IACjC,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC;IAC3C,IAAI,CAAC,cAAc,GAAG,SAAS,CAAC;IAChC,IAAI,CAAC,kBAAkB,CAAC,cAAc,CAAC,CAAC;IACxC,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC,CAAC;AACxC,CAAC,CAAC;AAEF,QAAQ,CAAC,SAAS,GAAG,MAAM,CAAC,MAAM,CAAC,IAAI,cAAI,EAAE,EAAE;IAC3C,IAAI,EAAE,UAAU;IAEhB,MAAM,YAAC,OAAO;QACV,IAAI,IAAI,CAAC,QAAQ,EAAE;YACf,IAAI,CAAC,QAAQ,GAAG,OAAO,CAAC,UAAU,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;SACrD;QACD,IAAI,IAAI,CAAC,UAAU,EAAE;YACjB,IAAI,CAAC,UAAU,GAAG,OAAO,CAAC,UAAU,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;SACzD;QACD,IAAI,IAAI,CAAC,SAAS,EAAE;YAChB,IAAI,CAAC,SAAS,GAAG,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;SAClD;IACL,CAAC;IAED,aAAa,YAAC,QAAQ,EAAE,UAAU,EAAE,cAAc;QAC9C,QAAQ,GAAG,IAAI,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC;QACtC,IAAM,WAAW,GAAG,IAAI,QAAQ,CAAC,QAAQ,EAAE,UAAU,IAAI,IAAI,CAAC,UAAU,EACpE,IAAI,EAAE,IAAI,CAAC,QAAQ,EAAE,EAAE,IAAI,CAAC,QAAQ,EAAE,EAAE,IAAI,CAAC,cAAc,EAAE,CAAC,CAAC;QACnE,WAAW,CAAC,cAAc,GAAG,CAAC,CAAC,KAAK,CAAC,iBAAiB,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,CAAC,cAAc,CAAC,CAAC,CAAC,IAAI,CAAC,cAAc,CAAC;QAC/G,WAAW,CAAC,UAAU,GAAG,IAAI,CAAC,UAAU,CAAC;QACzC,OAAO,WAAW,CAAC;IACvB,CAAC;IAED,WAAW,YAAC,GAAG;QACX,IAAI,CAAC,GAAG,EAAE;YACN,OAAO,CAAC,IAAI,iBAAO,CAAC,EAAE,EAAE,GAAG,EAAE,KAAK,EAAE,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC;SACrE;QACD,IAAI,OAAO,GAAG,KAAK,QAAQ,EAAE;YACzB,IAAI,gBAAM,CAAC,IAAI,CAAC,KAAK,CAAC,OAAO,EAAE,IAAI,CAAC,KAAK,CAAC,aAAa,EAAE,IAAI,CAAC,SAAS,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC,SAAS,CAC3F,GAAG,EACH,CAAC,UAAU,CAAC,EACZ,UAAS,GAAG,EAAE,MAAM;gBAChB,IAAI,GAAG,EAAE;oBACL,MAAM,IAAI,oBAAS,CAAC;wBAChB,KAAK,EAAE,GAAG,CAAC,KAAK;wBAChB,OAAO,EAAE,GAAG,CAAC,OAAO;qBACvB,EAAE,IAAI,CAAC,KAAK,CAAC,OAAO,EAAE,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC;iBACnD;gBACD,GAAG,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC;YAC7B,CAAC,CAAC,CAAC;SACV;QACD,OAAO,GAAG,CAAC;IACf,CAAC;IAED,oBAAoB;QAChB,IAAM,EAAE,GAAG,IAAI,iBAAO,CAAC,EAAE,EAAE,GAAG,EAAE,KAAK,EAAE,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,SAAS,CAAC,EAAE,IAAI,GAAG,CAAC,IAAI,QAAQ,CAAC,CAAC,EAAE,CAAC,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC;QAC1I,IAAI,CAAC,CAAC,CAAC,CAAC,UAAU,GAAG,IAAI,CAAC;QAC1B,OAAO,IAAI,CAAC;IAChB,CAAC;IAED,KAAK,YAAC,KAAK;QACP,IAAM,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC;QAC/B,IAAM,GAAG,GAAG,QAAQ,CAAC,MAAM,CAAC;QAC5B,IAAI,IAAI,CAAC;QACT,IAAI,CAAC,CAAC;QAEN,KAAK,GAAG,KAAK,CAAC,aAAa,EAAE,CAAC;QAC9B,IAAI,GAAG,KAAK,CAAC,MAAM,CAAC;QACpB,IAAI,IAAI,KAAK,CAAC,IAAI,GAAG,GAAG,IAAI,EAAE;YAC1B,OAAO,CAAC,CAAC;SACZ;aAAM;YACH,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,EAAE,CAAC,EAAE,EAAE;gBACvB,IAAI,QAAQ,CAAC,CAAC,CAAC,CAAC,KAAK,KAAK,KAAK,CAAC,CAAC,CAAC,EAAE;oBAChC,OAAO,CAAC,CAAC;iBACZ;aACJ;SACJ;QAED,OAAO,IAAI,CAAC,CAAC,oCAAoC;IACrD,CAAC;IAED,aAAa;QACT,IAAI,IAAI,CAAC,cAAc,EAAE;YACrB,OAAO,IAAI,CAAC,cAAc,CAAC;SAC9B;QAED,IAAI,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAE,UAAS,CAAC;YACxC,OAAO,CAAC,CAAC,UAAU,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC,KAAK,CAAC,KAAK,IAAI,CAAC,CAAC,KAAK,CAAC,CAAC;QAC3D,CAAC,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,KAAK,CAAC,2BAA2B,CAAC,CAAC;QAE/C,IAAI,QAAQ,EAAE;YACV,IAAI,QAAQ,CAAC,CAAC,CAAC,KAAK,GAAG,EAAE;gBACrB,QAAQ,CAAC,KAAK,EAAE,CAAC;aACpB;SACJ;aAAM;YACH,QAAQ,GAAG,EAAE,CAAC;SACjB;QAED,OAAO,CAAC,IAAI,CAAC,cAAc,GAAG,QAAQ,CAAC,CAAC;IAC5C,CAAC;IAED,oBAAoB;QAChB,OAAO,CAAC,IAAI,CAAC,UAAU;YACnB,IAAI,CAAC,QAAQ,CAAC,MAAM,KAAK,CAAC;YAC1B,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,KAAK,KAAK,GAAG;YAC9B,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,KAAK,KAAK,GAAG,IAAI,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,KAAK,KAAK,EAAE,CAAC,CAAC;IAChG,CAAC;IAED,IAAI,YAAC,OAAO;QACR,IAAM,cAAc,GAAG,IAAI,CAAC,SAAS,IAAI,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QACtE,IAAI,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC;QAC7B,IAAI,UAAU,GAAG,IAAI,CAAC,UAAU,CAAC;QAEjC,QAAQ,GAAG,QAAQ,IAAI,QAAQ,CAAC,GAAG,CAAC,UAAU,CAAC,IAAI,OAAO,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC9E,UAAU,GAAG,UAAU,IAAI,UAAU,CAAC,GAAG,CAAC,UAAS,MAAM,IAAI,OAAO,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAE7F,OAAO,IAAI,CAAC,aAAa,CAAC,QAAQ,EAAE,UAAU,EAAE,cAAc,CAAC,CAAC;IACpE,CAAC;IAED,MAAM,YAAC,OAAO,EAAE,MAAM;QAClB,IAAI,CAAC,EAAE,OAAO,CAAC;QACf,IAAI,CAAC,CAAC,OAAO,IAAI,CAAC,OAAO,CAAC,aAAa,CAAC,IAAI,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,KAAK,KAAK,EAAE,EAAE;YAClF,MAAM,CAAC,GAAG,CAAC,GAAG,EAAE,IAAI,CAAC,QAAQ,EAAE,EAAE,IAAI,CAAC,QAAQ,EAAE,CAAC,CAAC;SACrD;QACD,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YACvC,OAAO,GAAG,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;YAC3B,OAAO,CAAC,MAAM,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC;SACnC;IACL,CAAC;IAED,WAAW;QACP,OAAO,IAAI,CAAC,cAAc,CAAC;IAC/B,CAAC;CACJ,CAAC,CAAC;AAEH,kBAAe,QAAQ,CAAC", "sourcesContent": ["import Node from './node';\nimport Element from './element';\nimport LessError from '../less-error';\nimport * as utils from '../utils';\nimport Parser from '../parser/parser';\n\nconst Selector = function(elements, extendList, condition, index, currentFileInfo, visibilityInfo) {\n    this.extendList = extendList;\n    this.condition = condition;\n    this.evaldCondition = !condition;\n    this._index = index;\n    this._fileInfo = currentFileInfo;\n    this.elements = this.getElements(elements);\n    this.mixinElements_ = undefined;\n    this.copyVisibilityInfo(visibilityInfo);\n    this.setParent(this.elements, this);\n};\n\nSelector.prototype = Object.assign(new Node(), {\n    type: 'Selector',\n\n    accept(visitor) {\n        if (this.elements) {\n            this.elements = visitor.visitArray(this.elements);\n        }\n        if (this.extendList) {\n            this.extendList = visitor.visitArray(this.extendList);\n        }\n        if (this.condition) {\n            this.condition = visitor.visit(this.condition);\n        }\n    },\n\n    createDerived(elements, extendList, evaldCondition) {\n        elements = this.getElements(elements);\n        const newSelector = new Selector(elements, extendList || this.extendList,\n            null, this.getIndex(), this.fileInfo(), this.visibilityInfo());\n        newSelector.evaldCondition = (!utils.isNullOrUndefined(evaldCondition)) ? evaldCondition : this.evaldCondition;\n        newSelector.mediaEmpty = this.mediaEmpty;\n        return newSelector;\n    },\n\n    getElements(els) {\n        if (!els) {\n            return [new Element('', '&', false, this._index, this._fileInfo)];\n        }\n        if (typeof els === 'string') {\n            new Parser(this.parse.context, this.parse.importManager, this._fileInfo, this._index).parseNode(\n                els,\n                ['selector'],\n                function(err, result) {\n                    if (err) {\n                        throw new LessError({\n                            index: err.index,\n                            message: err.message\n                        }, this.parse.imports, this._fileInfo.filename);\n                    }\n                    els = result[0].elements;\n                });\n        }\n        return els;\n    },\n\n    createEmptySelectors() {\n        const el = new Element('', '&', false, this._index, this._fileInfo), sels = [new Selector([el], null, null, this._index, this._fileInfo)];\n        sels[0].mediaEmpty = true;\n        return sels;\n    },\n\n    match(other) {\n        const elements = this.elements;\n        const len = elements.length;\n        let olen;\n        let i;\n\n        other = other.mixinElements();\n        olen = other.length;\n        if (olen === 0 || len < olen) {\n            return 0;\n        } else {\n            for (i = 0; i < olen; i++) {\n                if (elements[i].value !== other[i]) {\n                    return 0;\n                }\n            }\n        }\n\n        return olen; // return number of matched elements\n    },\n\n    mixinElements() {\n        if (this.mixinElements_) {\n            return this.mixinElements_;\n        }\n\n        let elements = this.elements.map( function(v) {\n            return v.combinator.value + (v.value.value || v.value);\n        }).join('').match(/[,&#*.\\w-]([\\w-]|(\\\\.))*/g);\n\n        if (elements) {\n            if (elements[0] === '&') {\n                elements.shift();\n            }\n        } else {\n            elements = [];\n        }\n\n        return (this.mixinElements_ = elements);\n    },\n\n    isJustParentSelector() {\n        return !this.mediaEmpty &&\n            this.elements.length === 1 &&\n            this.elements[0].value === '&' &&\n            (this.elements[0].combinator.value === ' ' || this.elements[0].combinator.value === '');\n    },\n\n    eval(context) {\n        const evaldCondition = this.condition && this.condition.eval(context);\n        let elements = this.elements;\n        let extendList = this.extendList;\n\n        elements = elements && elements.map(function (e) { return e.eval(context); });\n        extendList = extendList && extendList.map(function(extend) { return extend.eval(context); });\n\n        return this.createDerived(elements, extendList, evaldCondition);\n    },\n\n    genCSS(context, output) {\n        let i, element;\n        if ((!context || !context.firstSelector) && this.elements[0].combinator.value === '') {\n            output.add(' ', this.fileInfo(), this.getIndex());\n        }\n        for (i = 0; i < this.elements.length; i++) {\n            element = this.elements[i];\n            element.genCSS(context, output);\n        }\n    },\n\n    getIsOutput() {\n        return this.evaldCondition;\n    }\n});\n\nexport default Selector;\n"]}