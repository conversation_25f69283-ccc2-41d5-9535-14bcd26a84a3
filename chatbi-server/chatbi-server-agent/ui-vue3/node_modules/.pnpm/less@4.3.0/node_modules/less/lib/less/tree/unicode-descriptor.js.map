{"version": 3, "file": "unicode-descriptor.js", "sourceRoot": "", "sources": ["../../../src/less/tree/unicode-descriptor.js"], "names": [], "mappings": ";;;AAAA,wDAA0B;AAE1B,IAAM,iBAAiB,GAAG,UAAS,KAAK;IACpC,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;AACvB,CAAC,CAAA;AAED,iBAAiB,CAAC,SAAS,GAAG,MAAM,CAAC,MAAM,CAAC,IAAI,cAAI,EAAE,EAAE;IACpD,IAAI,EAAE,mBAAmB;CAC5B,CAAC,CAAA;AAEF,kBAAe,iBAAiB,CAAC", "sourcesContent": ["import Node from './node';\n\nconst UnicodeDescriptor = function(value) {\n    this.value = value;\n}\n\nUnicodeDescriptor.prototype = Object.assign(new Node(), {\n    type: 'UnicodeDescriptor'\n})\n\nexport default UnicodeDescriptor;\n"]}