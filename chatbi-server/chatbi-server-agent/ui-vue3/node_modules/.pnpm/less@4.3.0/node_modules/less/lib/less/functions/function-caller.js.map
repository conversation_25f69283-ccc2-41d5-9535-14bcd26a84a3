{"version": 3, "file": "function-caller.js", "sourceRoot": "", "sources": ["../../../src/less/functions/function-caller.js"], "names": [], "mappings": ";;;AAAA,0EAA4C;AAE5C;IACI,wBAAY,IAAI,EAAE,OAAO,EAAE,KAAK,EAAE,eAAe;QAC7C,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,WAAW,EAAE,CAAC;QAC/B,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;QACnB,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC;QACvB,IAAI,CAAC,eAAe,GAAG,eAAe,CAAC;QAEvC,IAAI,CAAC,IAAI,GAAG,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,gBAAgB,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;IAClE,CAAC;IAED,gCAAO,GAAP;QACI,OAAO,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;IAC9B,CAAC;IAED,6BAAI,GAAJ,UAAK,IAAI;QAAT,iBAmCC;QAlCG,IAAI,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,EAAE;YACxB,IAAI,GAAG,CAAC,IAAI,CAAC,CAAC;SACjB;QACD,IAAM,QAAQ,GAAG,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC;QACpC,IAAI,QAAQ,KAAK,KAAK,EAAE;YACpB,IAAI,GAAG,IAAI,CAAC,GAAG,CAAC,UAAA,CAAC,IAAI,OAAA,CAAC,CAAC,IAAI,CAAC,KAAI,CAAC,OAAO,CAAC,EAApB,CAAoB,CAAC,CAAC;SAC9C;QACD,IAAM,aAAa,GAAG,UAAA,IAAI,IAAI,OAAA,CAAC,CAAC,IAAI,CAAC,IAAI,KAAK,SAAS,CAAC,EAA1B,CAA0B,CAAC;QAEzD,oEAAoE;QACpE,8CAA8C;QAC9C,IAAI,GAAG,IAAI;aACN,MAAM,CAAC,aAAa,CAAC;aACrB,GAAG,CAAC,UAAA,IAAI;YACL,IAAI,IAAI,CAAC,IAAI,KAAK,YAAY,EAAE;gBAC5B,IAAM,QAAQ,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,aAAa,CAAC,CAAC;gBAClD,IAAI,QAAQ,CAAC,MAAM,KAAK,CAAC,EAAE;oBACvB,8CAA8C;oBAC9C,IAAI,IAAI,CAAC,MAAM,IAAI,QAAQ,CAAC,CAAC,CAAC,CAAC,EAAE,KAAK,GAAG,EAAE;wBACvC,OAAO,IAAI,CAAC;qBACf;oBACD,OAAO,QAAQ,CAAC,CAAC,CAAC,CAAC;iBACtB;qBAAM;oBACH,OAAO,IAAI,oBAAU,CAAC,QAAQ,CAAC,CAAC;iBACnC;aACJ;YACD,OAAO,IAAI,CAAC;QAChB,CAAC,CAAC,CAAC;QAEP,IAAI,QAAQ,KAAK,KAAK,EAAE;YACpB,OAAO,IAAI,CAAC,IAAI,OAAT,IAAI,yBAAM,IAAI,CAAC,OAAO,GAAK,IAAI,UAAE;SAC3C;QAED,OAAO,IAAI,CAAC,IAAI,OAAT,IAAI,EAAS,IAAI,EAAE;IAC9B,CAAC;IACL,qBAAC;AAAD,CAAC,AAlDD,IAkDC;AAED,kBAAe,cAAc,CAAC", "sourcesContent": ["import Expression from '../tree/expression';\n\nclass functionCaller {\n    constructor(name, context, index, currentFileInfo) {\n        this.name = name.toLowerCase();\n        this.index = index;\n        this.context = context;\n        this.currentFileInfo = currentFileInfo;\n\n        this.func = context.frames[0].functionRegistry.get(this.name);\n    }\n\n    isValid() {\n        return Boolean(this.func);\n    }\n\n    call(args) {\n        if (!(Array.isArray(args))) {\n            args = [args];\n        }\n        const evalArgs = this.func.evalArgs;\n        if (evalArgs !== false) {\n            args = args.map(a => a.eval(this.context));\n        }\n        const commentFilter = item => !(item.type === 'Comment');\n\n        // This code is terrible and should be replaced as per this issue...\n        // https://github.com/less/less.js/issues/2477\n        args = args\n            .filter(commentFilter)\n            .map(item => {\n                if (item.type === 'Expression') {\n                    const subNodes = item.value.filter(commentFilter);\n                    if (subNodes.length === 1) {\n                        // https://github.com/less/less.js/issues/3616\n                        if (item.parens && subNodes[0].op === '/') {\n                            return item;\n                        }\n                        return subNodes[0];\n                    } else {\n                        return new Expression(subNodes);\n                    }\n                }\n                return item;\n            });\n\n        if (evalArgs === false) {\n            return this.func(this.context, ...args);\n        }\n\n        return this.func(...args);\n    }\n}\n\nexport default functionCaller;\n"]}