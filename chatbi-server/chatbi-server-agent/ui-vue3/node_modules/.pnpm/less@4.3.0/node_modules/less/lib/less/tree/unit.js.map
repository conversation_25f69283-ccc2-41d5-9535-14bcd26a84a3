{"version": 3, "file": "unit.js", "sourceRoot": "", "sources": ["../../../src/less/tree/unit.js"], "names": [], "mappings": ";;;AAAA,wDAA0B;AAC1B,sFAAuD;AACvD,sDAAkC;AAElC,IAAM,IAAI,GAAG,UAAS,SAAS,EAAE,WAAW,EAAE,UAAU;IACpD,IAAI,CAAC,SAAS,GAAG,SAAS,CAAC,CAAC,CAAC,KAAK,CAAC,SAAS,CAAC,SAAS,CAAC,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC;IACpE,IAAI,CAAC,WAAW,GAAG,WAAW,CAAC,CAAC,CAAC,KAAK,CAAC,SAAS,CAAC,WAAW,CAAC,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC;IAC1E,IAAI,UAAU,EAAE;QACZ,IAAI,CAAC,UAAU,GAAG,UAAU,CAAC;KAChC;SAAM,IAAI,SAAS,IAAI,SAAS,CAAC,MAAM,EAAE;QACtC,IAAI,CAAC,UAAU,GAAG,SAAS,CAAC,CAAC,CAAC,CAAC;KAClC;AACL,CAAC,CAAC;AAEF,IAAI,CAAC,SAAS,GAAG,MAAM,CAAC,MAAM,CAAC,IAAI,cAAI,EAAE,EAAE;IACvC,IAAI,EAAE,MAAM;IAEZ,KAAK;QACD,OAAO,IAAI,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,IAAI,CAAC,SAAS,CAAC,EAAE,KAAK,CAAC,SAAS,CAAC,IAAI,CAAC,WAAW,CAAC,EAAE,IAAI,CAAC,UAAU,CAAC,CAAC;IACzG,CAAC;IAED,MAAM,YAAC,OAAO,EAAE,MAAM;QAClB,oFAAoF;QACpF,IAAM,WAAW,GAAG,OAAO,IAAI,OAAO,CAAC,WAAW,CAAC;QACnD,IAAI,IAAI,CAAC,SAAS,CAAC,MAAM,KAAK,CAAC,EAAE;YAC7B,MAAM,CAAC,GAAG,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,sBAAsB;SACxD;aAAM,IAAI,CAAC,WAAW,IAAI,IAAI,CAAC,UAAU,EAAE;YACxC,MAAM,CAAC,GAAG,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;SAC/B;aAAM,IAAI,CAAC,WAAW,IAAI,IAAI,CAAC,WAAW,CAAC,MAAM,EAAE;YAChD,MAAM,CAAC,GAAG,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC;SACnC;IACL,CAAC;IAED,QAAQ;QACJ,IAAI,CAAC,EAAE,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;QAC5C,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,WAAW,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YAC1C,SAAS,IAAI,WAAI,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,CAAE,CAAC;SAC1C;QACD,OAAO,SAAS,CAAC;IACrB,CAAC;IAED,OAAO,YAAC,KAAK;QACT,OAAO,IAAI,CAAC,EAAE,CAAC,KAAK,CAAC,QAAQ,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC;IACrD,CAAC;IAED,EAAE,YAAC,UAAU;QACT,OAAO,IAAI,CAAC,QAAQ,EAAE,CAAC,WAAW,EAAE,KAAK,UAAU,CAAC,WAAW,EAAE,CAAC;IACtE,CAAC;IAED,QAAQ;QACJ,OAAO,MAAM,CAAC,uDAAuD,EAAE,IAAI,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,EAAE,CAAC,CAAC;IACpG,CAAC;IAED,OAAO;QACH,OAAO,IAAI,CAAC,SAAS,CAAC,MAAM,KAAK,CAAC,IAAI,IAAI,CAAC,WAAW,CAAC,MAAM,KAAK,CAAC,CAAC;IACxE,CAAC;IAED,UAAU;QACN,OAAO,IAAI,CAAC,SAAS,CAAC,MAAM,IAAI,CAAC,IAAI,IAAI,CAAC,WAAW,CAAC,MAAM,KAAK,CAAC,CAAC;IACvE,CAAC;IAED,GAAG,YAAC,QAAQ;QACR,IAAI,CAAC,CAAC;QAEN,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YACxC,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,GAAG,QAAQ,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC;SAC1D;QAED,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,WAAW,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YAC1C,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,GAAG,QAAQ,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC;SAC7D;IACL,CAAC;IAED,SAAS;QACL,IAAI,KAAK,CAAC;QACV,IAAM,MAAM,GAAG,EAAE,CAAC;QAClB,IAAI,OAAO,CAAC;QACZ,IAAI,SAAS,CAAC;QAEd,OAAO,GAAG,UAAU,UAAU;YAC1B,iDAAiD;YACjD,IAAI,KAAK,CAAC,cAAc,CAAC,UAAU,CAAC,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,EAAE;gBACxD,MAAM,CAAC,SAAS,CAAC,GAAG,UAAU,CAAC;aAClC;YAED,OAAO,UAAU,CAAC;QACtB,CAAC,CAAC;QAEF,KAAK,SAAS,IAAI,0BAAe,EAAE;YAC/B,iDAAiD;YACjD,IAAI,0BAAe,CAAC,cAAc,CAAC,SAAS,CAAC,EAAE;gBAC3C,KAAK,GAAG,0BAAe,CAAC,SAAS,CAAC,CAAC;gBAEnC,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;aACrB;SACJ;QAED,OAAO,MAAM,CAAC;IAClB,CAAC;IAED,MAAM;QACF,IAAM,OAAO,GAAG,EAAE,CAAC;QACnB,IAAI,UAAU,CAAC;QACf,IAAI,CAAC,CAAC;QAEN,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YACxC,UAAU,GAAG,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC;YAC/B,OAAO,CAAC,UAAU,CAAC,GAAG,CAAC,OAAO,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC;SACxD;QAED,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,WAAW,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YAC1C,UAAU,GAAG,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC;YACjC,OAAO,CAAC,UAAU,CAAC,GAAG,CAAC,OAAO,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC;SACxD;QAED,IAAI,CAAC,SAAS,GAAG,EAAE,CAAC;QACpB,IAAI,CAAC,WAAW,GAAG,EAAE,CAAC;QAEtB,KAAK,UAAU,IAAI,OAAO,EAAE;YACxB,iDAAiD;YACjD,IAAI,OAAO,CAAC,cAAc,CAAC,UAAU,CAAC,EAAE;gBACpC,IAAM,KAAK,GAAG,OAAO,CAAC,UAAU,CAAC,CAAC;gBAElC,IAAI,KAAK,GAAG,CAAC,EAAE;oBACX,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,EAAE,CAAC,EAAE,EAAE;wBACxB,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;qBACnC;iBACJ;qBAAM,IAAI,KAAK,GAAG,CAAC,EAAE;oBAClB,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,KAAK,EAAE,CAAC,EAAE,EAAE;wBACzB,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;qBACrC;iBACJ;aACJ;SACJ;QAED,IAAI,CAAC,SAAS,CAAC,IAAI,EAAE,CAAC;QACtB,IAAI,CAAC,WAAW,CAAC,IAAI,EAAE,CAAC;IAC5B,CAAC;CACJ,CAAC,CAAC;AAEH,kBAAe,IAAI,CAAC", "sourcesContent": ["import Node from './node';\nimport unitConversions from '../data/unit-conversions';\nimport * as utils from '../utils';\n\nconst Unit = function(numerator, denominator, backupUnit) {\n    this.numerator = numerator ? utils.copyArray(numerator).sort() : [];\n    this.denominator = denominator ? utils.copyArray(denominator).sort() : [];\n    if (backupUnit) {\n        this.backupUnit = backupUnit;\n    } else if (numerator && numerator.length) {\n        this.backupUnit = numerator[0];\n    }\n};\n\nUnit.prototype = Object.assign(new Node(), {\n    type: 'Unit',\n\n    clone() {\n        return new Unit(utils.copyArray(this.numerator), utils.copyArray(this.denominator), this.backupUnit);\n    },\n\n    genCSS(context, output) {\n        // Dimension checks the unit is singular and throws an error if in strict math mode.\n        const strictUnits = context && context.strictUnits;\n        if (this.numerator.length === 1) {\n            output.add(this.numerator[0]); // the ideal situation\n        } else if (!strictUnits && this.backupUnit) {\n            output.add(this.backupUnit);\n        } else if (!strictUnits && this.denominator.length) {\n            output.add(this.denominator[0]);\n        }\n    },\n\n    toString() {\n        let i, returnStr = this.numerator.join('*');\n        for (i = 0; i < this.denominator.length; i++) {\n            returnStr += `/${this.denominator[i]}`;\n        }\n        return returnStr;\n    },\n\n    compare(other) {\n        return this.is(other.toString()) ? 0 : undefined;\n    },\n\n    is(unitString) {\n        return this.toString().toUpperCase() === unitString.toUpperCase();\n    },\n\n    isLength() {\n        return RegExp('^(px|em|ex|ch|rem|in|cm|mm|pc|pt|ex|vw|vh|vmin|vmax)$', 'gi').test(this.toCSS());\n    },\n\n    isEmpty() {\n        return this.numerator.length === 0 && this.denominator.length === 0;\n    },\n\n    isSingular() {\n        return this.numerator.length <= 1 && this.denominator.length === 0;\n    },\n\n    map(callback) {\n        let i;\n\n        for (i = 0; i < this.numerator.length; i++) {\n            this.numerator[i] = callback(this.numerator[i], false);\n        }\n\n        for (i = 0; i < this.denominator.length; i++) {\n            this.denominator[i] = callback(this.denominator[i], true);\n        }\n    },\n\n    usedUnits() {\n        let group;\n        const result = {};\n        let mapUnit;\n        let groupName;\n\n        mapUnit = function (atomicUnit) {\n            // eslint-disable-next-line no-prototype-builtins\n            if (group.hasOwnProperty(atomicUnit) && !result[groupName]) {\n                result[groupName] = atomicUnit;\n            }\n\n            return atomicUnit;\n        };\n\n        for (groupName in unitConversions) {\n            // eslint-disable-next-line no-prototype-builtins\n            if (unitConversions.hasOwnProperty(groupName)) {\n                group = unitConversions[groupName];\n\n                this.map(mapUnit);\n            }\n        }\n\n        return result;\n    },\n\n    cancel() {\n        const counter = {};\n        let atomicUnit;\n        let i;\n\n        for (i = 0; i < this.numerator.length; i++) {\n            atomicUnit = this.numerator[i];\n            counter[atomicUnit] = (counter[atomicUnit] || 0) + 1;\n        }\n\n        for (i = 0; i < this.denominator.length; i++) {\n            atomicUnit = this.denominator[i];\n            counter[atomicUnit] = (counter[atomicUnit] || 0) - 1;\n        }\n\n        this.numerator = [];\n        this.denominator = [];\n\n        for (atomicUnit in counter) {\n            // eslint-disable-next-line no-prototype-builtins\n            if (counter.hasOwnProperty(atomicUnit)) {\n                const count = counter[atomicUnit];\n\n                if (count > 0) {\n                    for (i = 0; i < count; i++) {\n                        this.numerator.push(atomicUnit);\n                    }\n                } else if (count < 0) {\n                    for (i = 0; i < -count; i++) {\n                        this.denominator.push(atomicUnit);\n                    }\n                }\n            }\n        }\n\n        this.numerator.sort();\n        this.denominator.sort();\n    }\n});\n\nexport default Unit;\n"]}