{"version": 3, "file": "url-file-manager.js", "sourceRoot": "", "sources": ["../../src/less-node/url-file-manager.js"], "names": [], "mappings": ";;;AAAA,mCAAmC;AACnC;;;GAGG;AACH,IAAM,OAAO,GAAG,oBAAoB,CAAC;AACrC,oDAAsB;AACtB,IAAI,OAAO,CAAC;AACZ,kHAA+E;AAC/E,kEAAoC;AAEpC,IAAM,cAAc,GAAG,cAAY,CAAC,CAAA;AACpC,cAAc,CAAC,SAAS,GAAG,MAAM,CAAC,MAAM,CAAC,IAAI,kCAAmB,EAAE,EAAE;IAChE,QAAQ,YAAC,QAAQ,EAAE,gBAAgB,EAAE,OAAO,EAAE,WAAW;QACrD,OAAO,OAAO,CAAC,IAAI,CAAE,QAAQ,CAAE,IAAI,OAAO,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;IACtE,CAAC;IAED,QAAQ,YAAC,QAAQ,EAAE,gBAAgB,EAAE,OAAO,EAAE,WAAW;QACrD,OAAO,IAAI,OAAO,CAAC,UAAC,OAAO,EAAE,MAAM;YAC/B,IAAI,OAAO,KAAK,SAAS,EAAE;gBACvB,IAAI;oBAAE,OAAO,GAAG,OAAO,CAAC,QAAQ,CAAC,CAAC;iBAAE;gBACpC,OAAO,CAAC,EAAE;oBAAE,OAAO,GAAG,IAAI,CAAC;iBAAE;aAChC;YACD,IAAI,CAAC,OAAO,EAAE;gBACV,MAAM,CAAC,EAAE,IAAI,EAAE,MAAM,EAAE,OAAO,EAAE,kEAAkE,EAAE,CAAC,CAAC;gBACtG,OAAO;aACV;YAED,IAAI,MAAM,GAAG,OAAO,CAAC,IAAI,CAAE,QAAQ,CAAE,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,aAAG,CAAC,OAAO,CAAC,gBAAgB,EAAE,QAAQ,CAAC,CAAC;YAE3F,yCAAyC;YACzC,IAAM,UAAU,GAAG,MAAM,CAAC,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,GAAG,GAAG,CAAC,CAAC,CAAC,MAAM,CAAA;YAErE,OAAO,CAAC,GAAG,CAAC,UAAU,EAAE,EAAE,UAAU,EAAE,CAAC,EAAE,EAAE,UAAC,GAAG,EAAE,IAAI,EAAE,IAAI;gBACvD,IAAI,GAAG,IAAI,IAAI,IAAI,IAAI,CAAC,UAAU,IAAI,GAAG,EAAE;oBACvC,IAAM,OAAO,GAAG,IAAI,IAAI,IAAI,CAAC,UAAU,KAAK,GAAG;wBAC3C,CAAC,CAAC,oBAAa,MAAM,sBAAmB;wBACxC,CAAC,CAAC,oBAAa,MAAM,mCAAyB,GAAG,IAAI,IAAI,CAAC,aAAa,IAAI,IAAI,CAAC,UAAU,OAAI,CAAC;oBACnG,MAAM,CAAC,EAAE,IAAI,EAAE,MAAM,EAAE,OAAO,SAAA,EAAE,CAAC,CAAC;oBAClC,OAAO;iBACV;gBACD,IAAI,IAAI,CAAC,UAAU,IAAI,GAAG,EAAE;oBACxB,MAAM,CAAC,EAAE,IAAI,EAAE,MAAM,EAAE,OAAO,EAAE,oBAAa,MAAM,gCAA6B,EAAE,CAAC,CAAC;oBACpF,OAAO;iBACV;gBACD,IAAI,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC;gBAC7B,IAAI,CAAC,IAAI,EAAE;oBACP,gBAAM,CAAC,IAAI,CAAC,oCAA6B,IAAI,CAAC,UAAU,6BAAkB,MAAM,OAAG,CAAC,CAAC;iBACxF;gBACD,OAAO,CAAC,EAAE,QAAQ,EAAE,IAAI,IAAI,EAAE,EAAE,QAAQ,EAAE,MAAM,EAAE,CAAC,CAAC;YACxD,CAAC,CAAC,CAAC;QACP,CAAC,CAAC,CAAC;IACP,CAAC;CACJ,CAAC,CAAC;AAEH,kBAAe,cAAc,CAAC", "sourcesContent": ["/* eslint-disable no-unused-vars */\n/**\n * @todo - remove top eslint rule when FileManagers have JSDoc type\n * and are TS-type-checked\n */\nconst isUrlRe = /^(?:https?:)?\\/\\//i;\nimport url from 'url';\nlet request;\nimport AbstractFileManager from '../less/environment/abstract-file-manager.js';\nimport logger from '../less/logger';\n\nconst UrlFileManager = function() {}\nUrlFileManager.prototype = Object.assign(new AbstractFileManager(), {\n    supports(filename, currentDirectory, options, environment) {\n        return isUrlRe.test( filename ) || isUrlRe.test(currentDirectory);\n    },\n\n    loadFile(filename, currentDirectory, options, environment) {\n        return new Promise((fulfill, reject) => {\n            if (request === undefined) {\n                try { request = require('needle'); }\n                catch (e) { request = null; }\n            }\n            if (!request) {\n                reject({ type: 'File', message: 'optional dependency \\'needle\\' required to import over http(s)\\n' });\n                return;\n            }\n\n            let urlStr = isUrlRe.test( filename ) ? filename : url.resolve(currentDirectory, filename);\n\n            /** native-request currently has a bug */\n            const hackUrlStr = urlStr.indexOf('?') === -1 ? urlStr + '?' : urlStr\n\n            request.get(hackUrlStr, { follow_max: 5 }, (err, resp, body) => {\n                if (err || resp && resp.statusCode >= 400) {\n                    const message = resp && resp.statusCode === 404\n                        ? `resource '${urlStr}' was not found\\n`\n                        : `resource '${urlStr}' gave this Error:\\n  ${err || resp.statusMessage || resp.statusCode}\\n`;\n                    reject({ type: 'File', message });\n                    return;\n                }\n                if (resp.statusCode >= 300) {\n                    reject({ type: 'File', message: `resource '${urlStr}' caused too many redirects` });\n                    return;\n                }\n                body = body.toString('utf8');\n                if (!body) {\n                    logger.warn(`Warning: Empty body (HTTP ${resp.statusCode}) returned by \"${urlStr}\"`);\n                }\n                fulfill({ contents: body || '', filename: urlStr });\n            });\n        });\n    }\n});\n\nexport default UrlFileManager;\n"]}