{"version": 3, "file": "error-reporting.js", "sourceRoot": "", "sources": ["../../src/less-browser/error-reporting.js"], "names": [], "mappings": ";;;AAAA,qDAAiC;AACjC,8DAAgC;AAEhC,mBAAe,UAAC,MAAM,EAAE,IAAI,EAAE,OAAO;IAEjC,SAAS,SAAS,CAAC,CAAC,EAAE,QAAQ;QAC1B,IAAM,EAAE,GAAG,6BAAsB,KAAK,CAAC,SAAS,CAAC,QAAQ,IAAI,EAAE,CAAC,CAAE,CAAC;QACnE,IAAM,QAAQ,GAAG,oEAAoE,CAAC;QACtF,IAAM,IAAI,GAAG,MAAM,CAAC,QAAQ,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;QAClD,IAAI,KAAK,CAAC;QACV,IAAI,OAAO,CAAC;QACZ,IAAM,MAAM,GAAG,EAAE,CAAC;QAClB,IAAM,QAAQ,GAAG,CAAC,CAAC,QAAQ,IAAI,QAAQ,CAAC;QACxC,IAAM,cAAc,GAAG,QAAQ,CAAC,KAAK,CAAC,iBAAiB,CAAC,CAAC,CAAC,CAAC,CAAC;QAE5D,IAAI,CAAC,EAAE,GAAU,EAAE,CAAC;QACpB,IAAI,CAAC,SAAS,GAAG,oBAAoB,CAAC;QAEtC,OAAO,GAAG,cAAO,CAAC,CAAC,IAAI,IAAI,QAAQ,oBAAU,CAAC,CAAC,OAAO,IAAI,sCAAsC,CAAE;YAC9F,+BAAuB,QAAQ,gBAAK,cAAc,UAAO,CAAC;QAE9D,IAAM,SAAS,GAAG,UAAC,CAAC,EAAE,CAAC,EAAE,SAAS;YAC9B,IAAI,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,KAAK,SAAS,EAAE;gBAC5B,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,UAAU,EAAE,CAAC,QAAQ,CAAC,CAAC,CAAC,IAAI,EAAE,EAAE,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;qBAC1E,OAAO,CAAC,WAAW,EAAE,SAAS,CAAC;qBAC/B,OAAO,CAAC,aAAa,EAAE,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;aAC9C;QACL,CAAC,CAAC;QAEF,IAAI,CAAC,CAAC,IAAI,EAAE;YACR,SAAS,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC;YACpB,SAAS,CAAC,CAAC,EAAE,CAAC,EAAE,MAAM,CAAC,CAAC;YACxB,SAAS,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC;YACpB,OAAO,IAAI,kBAAW,CAAC,CAAC,IAAI,sBAAY,CAAC,CAAC,MAAM,GAAG,CAAC,sBAAY,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,UAAO,CAAC;SAC1F;QACD,IAAI,CAAC,CAAC,KAAK,IAAI,CAAC,CAAC,CAAC,OAAO,IAAI,OAAO,CAAC,QAAQ,IAAI,CAAC,CAAC,EAAE;YACjD,OAAO,IAAI,iCAA0B,CAAC,CAAC,KAAK,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,CAAE,CAAC;SACrF;QACD,IAAI,CAAC,SAAS,GAAG,OAAO,CAAC;QAEzB,yBAAyB;QACzB,iBAAO,CAAC,SAAS,CAAC,MAAM,CAAC,QAAQ,EAAE;YAC/B,kDAAkD;YAClD,wBAAwB;YACxB,qBAAqB;YACrB,iBAAiB;YACjB,YAAY;YACZ,GAAG;YACH,6BAA6B;YAC7B,kBAAkB;YAClB,qBAAqB;YACrB,iBAAiB;YACjB,iBAAiB;YACjB,GAAG;YACH,2BAA2B;YAC3B,iBAAiB;YACjB,iBAAiB;YACjB,YAAY;YACZ,wBAAwB;YACxB,GAAG;YACH,gCAAgC;YAChC,iBAAiB;YACjB,GAAG;YACH,0BAA0B;YAC1B,kBAAkB;YAClB,oBAAoB;YACpB,wBAAwB;YACxB,YAAY;YACZ,GAAG;YACH,yBAAyB;YACzB,aAAa;YACb,GAAG;YACH,8BAA8B;YAC9B,aAAa;YACb,oBAAoB;YACpB,sBAAsB;YACtB,gCAAgC;YAChC,GAAG;SACN,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,EAAE,KAAK,EAAE,eAAe,EAAE,CAAC,CAAC;QAE1C,IAAI,CAAC,KAAK,CAAC,OAAO,GAAG;YACjB,gCAAgC;YAChC,wBAAwB;YACxB,wBAAwB;YACxB,oBAAoB;YACpB,4BAA4B;YAC5B,yBAAyB;YACzB,aAAa;YACb,eAAe;YACf,qBAAqB;SACxB,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;QAEZ,IAAI,OAAO,CAAC,GAAG,KAAK,aAAa,EAAE;YAC/B,KAAK,GAAG,WAAW,CAAC;gBAChB,IAAM,QAAQ,GAAG,MAAM,CAAC,QAAQ,CAAC;gBACjC,IAAM,IAAI,GAAG,QAAQ,CAAC,IAAI,CAAC;gBAC3B,IAAI,IAAI,EAAE;oBACN,IAAI,QAAQ,CAAC,cAAc,CAAC,EAAE,CAAC,EAAE;wBAC7B,IAAI,CAAC,YAAY,CAAC,IAAI,EAAE,QAAQ,CAAC,cAAc,CAAC,EAAE,CAAC,CAAC,CAAC;qBACxD;yBAAM;wBACH,IAAI,CAAC,YAAY,CAAC,IAAI,EAAE,IAAI,CAAC,UAAU,CAAC,CAAC;qBAC5C;oBACD,aAAa,CAAC,KAAK,CAAC,CAAC;iBACxB;YACL,CAAC,EAAE,EAAE,CAAC,CAAC;SACV;IACL,CAAC;IAED,SAAS,eAAe,CAAC,IAAI;QACzB,IAAM,IAAI,GAAG,MAAM,CAAC,QAAQ,CAAC,cAAc,CAAC,6BAAsB,KAAK,CAAC,SAAS,CAAC,IAAI,CAAC,CAAE,CAAC,CAAC;QAC3F,IAAI,IAAI,EAAE;YACN,IAAI,CAAC,UAAU,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC;SACrC;IACL,CAAC;IAED,SAAS,kBAAkB;QACvB,YAAY;IAChB,CAAC;IAED,SAAS,WAAW,CAAC,IAAI;QACrB,IAAI,CAAC,OAAO,CAAC,cAAc,IAAI,OAAO,CAAC,cAAc,KAAK,MAAM,EAAE;YAC9D,eAAe,CAAC,IAAI,CAAC,CAAC;SACzB;aAAM,IAAI,OAAO,CAAC,cAAc,KAAK,SAAS,EAAE;YAC7C,kBAAkB,CAAC,IAAI,CAAC,CAAC;SAC5B;aAAM,IAAI,OAAO,OAAO,CAAC,cAAc,KAAK,UAAU,EAAE;YACrD,OAAO,CAAC,cAAc,CAAC,QAAQ,EAAE,IAAI,CAAC,CAAC;SAC1C;IACL,CAAC;IAED,SAAS,YAAY,CAAC,CAAC,EAAE,QAAQ;QAC7B,IAAM,QAAQ,GAAG,kBAAkB,CAAC;QACpC,IAAM,QAAQ,GAAG,CAAC,CAAC,QAAQ,IAAI,QAAQ,CAAC;QACxC,IAAM,MAAM,GAAG,EAAE,CAAC;QAClB,IAAI,OAAO,GAAG,UAAG,CAAC,CAAC,IAAI,IAAI,QAAQ,oBAAU,CAAC,CAAC,OAAO,IAAI,sCAAsC,iBAAO,QAAQ,CAAE,CAAC;QAElH,IAAM,SAAS,GAAG,UAAC,CAAC,EAAE,CAAC,EAAE,SAAS;YAC9B,IAAI,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,KAAK,SAAS,EAAE;gBAC5B,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,UAAU,EAAE,CAAC,QAAQ,CAAC,CAAC,CAAC,IAAI,EAAE,EAAE,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;qBAC1E,OAAO,CAAC,WAAW,EAAE,SAAS,CAAC;qBAC/B,OAAO,CAAC,aAAa,EAAE,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;aAC9C;QACL,CAAC,CAAC;QAEF,IAAI,CAAC,CAAC,IAAI,EAAE;YACR,SAAS,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC;YACpB,SAAS,CAAC,CAAC,EAAE,CAAC,EAAE,MAAM,CAAC,CAAC;YACxB,SAAS,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC;YACpB,OAAO,IAAI,mBAAY,CAAC,CAAC,IAAI,sBAAY,CAAC,CAAC,MAAM,GAAG,CAAC,gBAAM,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAE,CAAC;SAClF;QACD,IAAI,CAAC,CAAC,KAAK,IAAI,CAAC,CAAC,CAAC,OAAO,IAAI,OAAO,CAAC,QAAQ,IAAI,CAAC,CAAC,EAAE;YACjD,OAAO,IAAI,yBAAkB,CAAC,CAAC,KAAK,CAAE,CAAC;SAC1C;QACD,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;IAC/B,CAAC;IAED,SAAS,KAAK,CAAC,CAAC,EAAE,QAAQ;QACtB,IAAI,CAAC,OAAO,CAAC,cAAc,IAAI,OAAO,CAAC,cAAc,KAAK,MAAM,EAAE;YAC9D,SAAS,CAAC,CAAC,EAAE,QAAQ,CAAC,CAAC;SAC1B;aAAM,IAAI,OAAO,CAAC,cAAc,KAAK,SAAS,EAAE;YAC7C,YAAY,CAAC,CAAC,EAAE,QAAQ,CAAC,CAAC;SAC7B;aAAM,IAAI,OAAO,OAAO,CAAC,cAAc,KAAK,UAAU,EAAE;YACrD,OAAO,CAAC,cAAc,CAAC,KAAK,EAAE,CAAC,EAAE,QAAQ,CAAC,CAAC;SAC9C;IACL,CAAC;IAED,OAAO;QACH,GAAG,EAAE,KAAK;QACV,MAAM,EAAE,WAAW;KACtB,CAAC;AACN,CAAC,EAAC", "sourcesContent": ["import * as utils from './utils';\nimport browser from './browser';\n\nexport default (window, less, options) => {\n\n    function errorHTML(e, rootHref) {\n        const id = `less-error-message:${utils.extractId(rootHref || '')}`;\n        const template = '<li><label>{line}</label><pre class=\"{class}\">{content}</pre></li>';\n        const elem = window.document.createElement('div');\n        let timer;\n        let content;\n        const errors = [];\n        const filename = e.filename || rootHref;\n        const filenameNoPath = filename.match(/([^/]+(\\?.*)?)$/)[1];\n\n        elem.id        = id;\n        elem.className = 'less-error-message';\n\n        content = `<h3>${e.type || 'Syntax'}Error: ${e.message || 'There is an error in your .less file'}` + \n            `</h3><p>in <a href=\"${filename}\">${filenameNoPath}</a> `;\n\n        const errorline = (e, i, classname) => {\n            if (e.extract[i] !== undefined) {\n                errors.push(template.replace(/\\{line\\}/, (parseInt(e.line, 10) || 0) + (i - 1))\n                    .replace(/\\{class\\}/, classname)\n                    .replace(/\\{content\\}/, e.extract[i]));\n            }\n        };\n\n        if (e.line) {\n            errorline(e, 0, '');\n            errorline(e, 1, 'line');\n            errorline(e, 2, '');\n            content += `on line ${e.line}, column ${e.column + 1}:</p><ul>${errors.join('')}</ul>`;\n        }\n        if (e.stack && (e.extract || options.logLevel >= 4)) {\n            content += `<br/>Stack Trace</br />${e.stack.split('\\n').slice(1).join('<br/>')}`;\n        }\n        elem.innerHTML = content;\n\n        // CSS for error messages\n        browser.createCSS(window.document, [\n            '.less-error-message ul, .less-error-message li {',\n            'list-style-type: none;',\n            'margin-right: 15px;',\n            'padding: 4px 0;',\n            'margin: 0;',\n            '}',\n            '.less-error-message label {',\n            'font-size: 12px;',\n            'margin-right: 15px;',\n            'padding: 4px 0;',\n            'color: #cc7777;',\n            '}',\n            '.less-error-message pre {',\n            'color: #dd6666;',\n            'padding: 4px 0;',\n            'margin: 0;',\n            'display: inline-block;',\n            '}',\n            '.less-error-message pre.line {',\n            'color: #ff0000;',\n            '}',\n            '.less-error-message h3 {',\n            'font-size: 20px;',\n            'font-weight: bold;',\n            'padding: 15px 0 5px 0;',\n            'margin: 0;',\n            '}',\n            '.less-error-message a {',\n            'color: #10a',\n            '}',\n            '.less-error-message .error {',\n            'color: red;',\n            'font-weight: bold;',\n            'padding-bottom: 2px;',\n            'border-bottom: 1px dashed red;',\n            '}'\n        ].join('\\n'), { title: 'error-message' });\n\n        elem.style.cssText = [\n            'font-family: Arial, sans-serif',\n            'border: 1px solid #e00',\n            'background-color: #eee',\n            'border-radius: 5px',\n            '-webkit-border-radius: 5px',\n            '-moz-border-radius: 5px',\n            'color: #e00',\n            'padding: 15px',\n            'margin-bottom: 15px'\n        ].join(';');\n\n        if (options.env === 'development') {\n            timer = setInterval(() => {\n                const document = window.document;\n                const body = document.body;\n                if (body) {\n                    if (document.getElementById(id)) {\n                        body.replaceChild(elem, document.getElementById(id));\n                    } else {\n                        body.insertBefore(elem, body.firstChild);\n                    }\n                    clearInterval(timer);\n                }\n            }, 10);\n        }\n    }\n\n    function removeErrorHTML(path) {\n        const node = window.document.getElementById(`less-error-message:${utils.extractId(path)}`);\n        if (node) {\n            node.parentNode.removeChild(node);\n        }\n    }\n\n    function removeErrorConsole() {\n        // no action\n    }\n\n    function removeError(path) {\n        if (!options.errorReporting || options.errorReporting === 'html') {\n            removeErrorHTML(path);\n        } else if (options.errorReporting === 'console') {\n            removeErrorConsole(path);\n        } else if (typeof options.errorReporting === 'function') {\n            options.errorReporting('remove', path);\n        }\n    }\n\n    function errorConsole(e, rootHref) {\n        const template = '{line} {content}';\n        const filename = e.filename || rootHref;\n        const errors = [];\n        let content = `${e.type || 'Syntax'}Error: ${e.message || 'There is an error in your .less file'} in ${filename}`;\n\n        const errorline = (e, i, classname) => {\n            if (e.extract[i] !== undefined) {\n                errors.push(template.replace(/\\{line\\}/, (parseInt(e.line, 10) || 0) + (i - 1))\n                    .replace(/\\{class\\}/, classname)\n                    .replace(/\\{content\\}/, e.extract[i]));\n            }\n        };\n\n        if (e.line) {\n            errorline(e, 0, '');\n            errorline(e, 1, 'line');\n            errorline(e, 2, '');\n            content += ` on line ${e.line}, column ${e.column + 1}:\\n${errors.join('\\n')}`;\n        }\n        if (e.stack && (e.extract || options.logLevel >= 4)) {\n            content += `\\nStack Trace\\n${e.stack}`;\n        }\n        less.logger.error(content);\n    }\n\n    function error(e, rootHref) {\n        if (!options.errorReporting || options.errorReporting === 'html') {\n            errorHTML(e, rootHref);\n        } else if (options.errorReporting === 'console') {\n            errorConsole(e, rootHref);\n        } else if (typeof options.errorReporting === 'function') {\n            options.errorReporting('add', e, rootHref);\n        }\n    }\n\n    return {\n        add: error,\n        remove: removeError\n    };\n};\n"]}