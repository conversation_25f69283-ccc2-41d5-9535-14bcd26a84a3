{"version": 3, "file": "default.js", "sourceRoot": "", "sources": ["../../../src/less/functions/default.js"], "names": [], "mappings": ";;;AAAA,oEAAsC;AACtC,sDAAkC;AAElC,IAAM,WAAW,GAAG;IAChB,IAAI,EAAE;QACF,IAAM,CAAC,GAAG,IAAI,CAAC,MAAM,CAAC;QACtB,IAAM,CAAC,GAAG,IAAI,CAAC,MAAM,CAAC;QACtB,IAAI,CAAC,EAAE;YACH,MAAM,CAAC,CAAC;SACX;QACD,IAAI,CAAC,KAAK,CAAC,iBAAiB,CAAC,CAAC,CAAC,EAAE;YAC7B,OAAO,CAAC,CAAC,CAAC,CAAC,iBAAO,CAAC,IAAI,CAAC,CAAC,CAAC,iBAAO,CAAC,KAAK,CAAC;SAC3C;IACL,CAAC;IACD,KAAK,EAAE,UAAU,CAAC;QACd,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC;IACpB,CAAC;IACD,KAAK,EAAE,UAAU,CAAC;QACd,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC;IACpB,CAAC;IACD,KAAK,EAAE;QACH,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC;IACrC,CAAC;CACJ,CAAC;AAEF,kBAAe,WAAW,CAAC", "sourcesContent": ["import Keyword from '../tree/keyword';\nimport * as utils from '../utils';\n\nconst defaultFunc = {\n    eval: function () {\n        const v = this.value_;\n        const e = this.error_;\n        if (e) {\n            throw e;\n        }\n        if (!utils.isNullOrUndefined(v)) {\n            return v ? Keyword.True : Keyword.False;\n        }\n    },\n    value: function (v) {\n        this.value_ = v;\n    },\n    error: function (e) {\n        this.error_ = e;\n    },\n    reset: function () {\n        this.value_ = this.error_ = null;\n    }\n};\n\nexport default defaultFunc;\n"]}