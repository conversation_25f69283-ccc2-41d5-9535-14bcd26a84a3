{"version": 3, "file": "dimension.js", "sourceRoot": "", "sources": ["../../../src/less/tree/dimension.js"], "names": [], "mappings": ";;;AAAA,0CAA0C;AAC1C,wDAA0B;AAC1B,sFAAuD;AACvD,wDAA0B;AAC1B,0DAA4B;AAE5B,EAAE;AACF,uBAAuB;AACvB,EAAE;AACF,IAAM,SAAS,GAAG,UAAS,KAAK,EAAE,IAAI;IAClC,IAAI,CAAC,KAAK,GAAG,UAAU,CAAC,KAAK,CAAC,CAAC;IAC/B,IAAI,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE;QACnB,MAAM,IAAI,KAAK,CAAC,4BAA4B,CAAC,CAAC;KACjD;IACD,IAAI,CAAC,IAAI,GAAG,CAAC,IAAI,IAAI,IAAI,YAAY,cAAI,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC;QAC/C,IAAI,cAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC;IACxC,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;AACpC,CAAC,CAAC;AAEF,SAAS,CAAC,SAAS,GAAG,MAAM,CAAC,MAAM,CAAC,IAAI,cAAI,EAAE,EAAE;IAC5C,IAAI,EAAE,WAAW;IAEjB,MAAM,YAAC,OAAO;QACV,IAAI,CAAC,IAAI,GAAG,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;IACzC,CAAC;IAED,qCAAqC;IACrC,0CAA0C;IAC1C,IAAI,YAAC,OAAO;QACR,OAAO,IAAI,CAAC;IAChB,CAAC;IAED,OAAO;QACH,OAAO,IAAI,eAAK,CAAC,CAAC,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC;IAC3D,CAAC;IAED,MAAM,YAAC,OAAO,EAAE,MAAM;QAClB,IAAI,CAAC,OAAO,IAAI,OAAO,CAAC,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE,EAAE;YAC7D,MAAM,IAAI,KAAK,CAAC,6FAAsF,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,CAAE,CAAC,CAAC;SACjI;QAED,IAAM,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC,OAAO,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC;QAC/C,IAAI,QAAQ,GAAG,MAAM,CAAC,KAAK,CAAC,CAAC;QAE7B,IAAI,KAAK,KAAK,CAAC,IAAI,KAAK,GAAG,QAAQ,IAAI,KAAK,GAAG,CAAC,QAAQ,EAAE;YACtD,4BAA4B;YAC5B,QAAQ,GAAG,KAAK,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC,CAAC;SACnD;QAED,IAAI,OAAO,IAAI,OAAO,CAAC,QAAQ,EAAE;YAC7B,kCAAkC;YAClC,IAAI,KAAK,KAAK,CAAC,IAAI,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,EAAE;gBACrC,MAAM,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;gBACrB,OAAO;aACV;YAED,2CAA2C;YAC3C,IAAI,KAAK,GAAG,CAAC,IAAI,KAAK,GAAG,CAAC,EAAE;gBACxB,QAAQ,GAAG,CAAC,QAAQ,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;aACnC;SACJ;QAED,MAAM,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;QACrB,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC;IACtC,CAAC;IAED,0CAA0C;IAC1C,4CAA4C;IAC5C,iCAAiC;IACjC,OAAO,YAAC,OAAO,EAAE,EAAE,EAAE,KAAK;QACtB,0BAA0B;QAC1B,IAAI,KAAK,GAAG,IAAI,CAAC,QAAQ,CAAC,OAAO,EAAE,EAAE,EAAE,IAAI,CAAC,KAAK,EAAE,KAAK,CAAC,KAAK,CAAC,CAAC;QAChE,IAAI,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC,KAAK,EAAE,CAAC;QAE7B,IAAI,EAAE,KAAK,GAAG,IAAI,EAAE,KAAK,GAAG,EAAE;YAC1B,IAAI,IAAI,CAAC,SAAS,CAAC,MAAM,KAAK,CAAC,IAAI,IAAI,CAAC,WAAW,CAAC,MAAM,KAAK,CAAC,EAAE;gBAC9D,IAAI,GAAG,KAAK,CAAC,IAAI,CAAC,KAAK,EAAE,CAAC;gBAC1B,IAAI,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE;oBACtB,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC;iBAC1C;aACJ;iBAAM,IAAI,KAAK,CAAC,IAAI,CAAC,SAAS,CAAC,MAAM,KAAK,CAAC,IAAI,IAAI,CAAC,WAAW,CAAC,MAAM,KAAK,CAAC,EAAE;gBAC3E,aAAa;aAChB;iBAAM;gBACH,KAAK,GAAG,KAAK,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,CAAC,CAAC;gBAE/C,IAAI,OAAO,CAAC,WAAW,IAAI,KAAK,CAAC,IAAI,CAAC,QAAQ,EAAE,KAAK,IAAI,CAAC,QAAQ,EAAE,EAAE;oBAClE,MAAM,IAAI,KAAK,CAAC,iEAAiE;0BAC3E,sBAAe,IAAI,CAAC,QAAQ,EAAE,oBAAU,KAAK,CAAC,IAAI,CAAC,QAAQ,EAAE,OAAI,CAAC,CAAC;iBAC5E;gBAED,KAAK,GAAG,IAAI,CAAC,QAAQ,CAAC,OAAO,EAAE,EAAE,EAAE,IAAI,CAAC,KAAK,EAAE,KAAK,CAAC,KAAK,CAAC,CAAC;aAC/D;SACJ;aAAM,IAAI,EAAE,KAAK,GAAG,EAAE;YACnB,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,IAAI,EAAE,CAAC;YACpE,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC,IAAI,EAAE,CAAC;YAC1E,IAAI,CAAC,MAAM,EAAE,CAAC;SACjB;aAAM,IAAI,EAAE,KAAK,GAAG,EAAE;YACnB,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC,IAAI,EAAE,CAAC;YACtE,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,IAAI,EAAE,CAAC;YACxE,IAAI,CAAC,MAAM,EAAE,CAAC;SACjB;QACD,OAAO,IAAI,SAAS,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC;IACtC,CAAC;IAED,OAAO,YAAC,KAAK;QACT,IAAI,CAAC,EAAE,CAAC,CAAC;QAET,IAAI,CAAC,CAAC,KAAK,YAAY,SAAS,CAAC,EAAE;YAC/B,OAAO,SAAS,CAAC;SACpB;QAED,IAAI,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,IAAI,KAAK,CAAC,IAAI,CAAC,OAAO,EAAE,EAAE;YAC7C,CAAC,GAAG,IAAI,CAAC;YACT,CAAC,GAAG,KAAK,CAAC;SACb;aAAM;YACH,CAAC,GAAG,IAAI,CAAC,KAAK,EAAE,CAAC;YACjB,CAAC,GAAG,KAAK,CAAC,KAAK,EAAE,CAAC;YAClB,IAAI,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE;gBAC9B,OAAO,SAAS,CAAC;aACpB;SACJ;QAED,OAAO,cAAI,CAAC,cAAc,CAAC,CAAC,CAAC,KAAK,EAAE,CAAC,CAAC,KAAK,CAAC,CAAC;IACjD,CAAC;IAED,KAAK;QACD,OAAO,IAAI,CAAC,SAAS,CAAC,EAAE,MAAM,EAAE,IAAI,EAAE,QAAQ,EAAE,GAAG,EAAE,KAAK,EAAE,KAAK,EAAE,CAAC,CAAC;IACzE,CAAC;IAED,SAAS,YAAC,WAAW;QACjB,IAAI,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC;QACvB,IAAM,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC,KAAK,EAAE,CAAC;QAC/B,IAAI,CAAC,CAAC;QACN,IAAI,SAAS,CAAC;QACd,IAAI,KAAK,CAAC;QACV,IAAI,UAAU,CAAC;QACf,IAAI,kBAAkB,GAAG,EAAE,CAAC;QAC5B,IAAI,SAAS,CAAC;QAEd,IAAI,OAAO,WAAW,KAAK,QAAQ,EAAE;YACjC,KAAK,CAAC,IAAI,0BAAe,EAAE;gBACvB,IAAI,0BAAe,CAAC,CAAC,CAAC,CAAC,cAAc,CAAC,WAAW,CAAC,EAAE;oBAChD,kBAAkB,GAAG,EAAE,CAAC;oBACxB,kBAAkB,CAAC,CAAC,CAAC,GAAG,WAAW,CAAC;iBACvC;aACJ;YACD,WAAW,GAAG,kBAAkB,CAAC;SACpC;QACD,SAAS,GAAG,UAAU,UAAU,EAAE,WAAW;YACzC,IAAI,KAAK,CAAC,cAAc,CAAC,UAAU,CAAC,EAAE;gBAClC,IAAI,WAAW,EAAE;oBACb,KAAK,GAAG,KAAK,GAAG,CAAC,KAAK,CAAC,UAAU,CAAC,GAAG,KAAK,CAAC,UAAU,CAAC,CAAC,CAAC;iBAC3D;qBAAM;oBACH,KAAK,GAAG,KAAK,GAAG,CAAC,KAAK,CAAC,UAAU,CAAC,GAAG,KAAK,CAAC,UAAU,CAAC,CAAC,CAAC;iBAC3D;gBAED,OAAO,UAAU,CAAC;aACrB;YAED,OAAO,UAAU,CAAC;QACtB,CAAC,CAAC;QAEF,KAAK,SAAS,IAAI,WAAW,EAAE;YAC3B,IAAI,WAAW,CAAC,cAAc,CAAC,SAAS,CAAC,EAAE;gBACvC,UAAU,GAAG,WAAW,CAAC,SAAS,CAAC,CAAC;gBACpC,KAAK,GAAG,0BAAe,CAAC,SAAS,CAAC,CAAC;gBAEnC,IAAI,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;aACvB;SACJ;QAED,IAAI,CAAC,MAAM,EAAE,CAAC;QAEd,OAAO,IAAI,SAAS,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC;IACtC,CAAC;CACJ,CAAC,CAAC;AAEH,kBAAe,SAAS,CAAC", "sourcesContent": ["/* eslint-disable no-prototype-builtins */\nimport Node from './node';\nimport unitConversions from '../data/unit-conversions';\nimport Unit from './unit';\nimport Color from './color';\n\n//\n// A number with a unit\n//\nconst Dimension = function(value, unit) {\n    this.value = parseFloat(value);\n    if (isNaN(this.value)) {\n        throw new Error('Dimension is not a number.');\n    }\n    this.unit = (unit && unit instanceof Unit) ? unit :\n        new Unit(unit ? [unit] : undefined);\n    this.setParent(this.unit, this);\n};\n\nDimension.prototype = Object.assign(new Node(), {\n    type: 'Dimension',\n\n    accept(visitor) {\n        this.unit = visitor.visit(this.unit);\n    },\n\n    // remove when Nodes have JSDoc types\n    // eslint-disable-next-line no-unused-vars\n    eval(context) {\n        return this;\n    },\n\n    toColor() {\n        return new Color([this.value, this.value, this.value]);\n    },\n\n    genCSS(context, output) {\n        if ((context && context.strictUnits) && !this.unit.isSingular()) {\n            throw new Error(`Multiple units in dimension. Correct the units or use the unit function. Bad unit: ${this.unit.toString()}`);\n        }\n\n        const value = this.fround(context, this.value);\n        let strValue = String(value);\n\n        if (value !== 0 && value < 0.000001 && value > -0.000001) {\n            // would be output 1e-6 etc.\n            strValue = value.toFixed(20).replace(/0+$/, '');\n        }\n\n        if (context && context.compress) {\n            // Zero values doesn't need a unit\n            if (value === 0 && this.unit.isLength()) {\n                output.add(strValue);\n                return;\n            }\n\n            // Float values doesn't need a leading zero\n            if (value > 0 && value < 1) {\n                strValue = (strValue).substr(1);\n            }\n        }\n\n        output.add(strValue);\n        this.unit.genCSS(context, output);\n    },\n\n    // In an operation between two Dimensions,\n    // we default to the first Dimension's unit,\n    // so `1px + 2` will yield `3px`.\n    operate(context, op, other) {\n        /* jshint noempty:false */\n        let value = this._operate(context, op, this.value, other.value);\n        let unit = this.unit.clone();\n\n        if (op === '+' || op === '-') {\n            if (unit.numerator.length === 0 && unit.denominator.length === 0) {\n                unit = other.unit.clone();\n                if (this.unit.backupUnit) {\n                    unit.backupUnit = this.unit.backupUnit;\n                }\n            } else if (other.unit.numerator.length === 0 && unit.denominator.length === 0) {\n                // do nothing\n            } else {\n                other = other.convertTo(this.unit.usedUnits());\n\n                if (context.strictUnits && other.unit.toString() !== unit.toString()) {\n                    throw new Error('Incompatible units. Change the units or use the unit function. '\n                        + `Bad units: '${unit.toString()}' and '${other.unit.toString()}'.`);\n                }\n\n                value = this._operate(context, op, this.value, other.value);\n            }\n        } else if (op === '*') {\n            unit.numerator = unit.numerator.concat(other.unit.numerator).sort();\n            unit.denominator = unit.denominator.concat(other.unit.denominator).sort();\n            unit.cancel();\n        } else if (op === '/') {\n            unit.numerator = unit.numerator.concat(other.unit.denominator).sort();\n            unit.denominator = unit.denominator.concat(other.unit.numerator).sort();\n            unit.cancel();\n        }\n        return new Dimension(value, unit);\n    },\n\n    compare(other) {\n        let a, b;\n\n        if (!(other instanceof Dimension)) {\n            return undefined;\n        }\n\n        if (this.unit.isEmpty() || other.unit.isEmpty()) {\n            a = this;\n            b = other;\n        } else {\n            a = this.unify();\n            b = other.unify();\n            if (a.unit.compare(b.unit) !== 0) {\n                return undefined;\n            }\n        }\n\n        return Node.numericCompare(a.value, b.value);\n    },\n\n    unify() {\n        return this.convertTo({ length: 'px', duration: 's', angle: 'rad' });\n    },\n\n    convertTo(conversions) {\n        let value = this.value;\n        const unit = this.unit.clone();\n        let i;\n        let groupName;\n        let group;\n        let targetUnit;\n        let derivedConversions = {};\n        let applyUnit;\n\n        if (typeof conversions === 'string') {\n            for (i in unitConversions) {\n                if (unitConversions[i].hasOwnProperty(conversions)) {\n                    derivedConversions = {};\n                    derivedConversions[i] = conversions;\n                }\n            }\n            conversions = derivedConversions;\n        }\n        applyUnit = function (atomicUnit, denominator) {\n            if (group.hasOwnProperty(atomicUnit)) {\n                if (denominator) {\n                    value = value / (group[atomicUnit] / group[targetUnit]);\n                } else {\n                    value = value * (group[atomicUnit] / group[targetUnit]);\n                }\n\n                return targetUnit;\n            }\n\n            return atomicUnit;\n        };\n\n        for (groupName in conversions) {\n            if (conversions.hasOwnProperty(groupName)) {\n                targetUnit = conversions[groupName];\n                group = unitConversions[groupName];\n\n                unit.map(applyUnit);\n            }\n        }\n\n        unit.cancel();\n\n        return new Dimension(value, unit);\n    }\n});\n\nexport default Dimension;\n"]}