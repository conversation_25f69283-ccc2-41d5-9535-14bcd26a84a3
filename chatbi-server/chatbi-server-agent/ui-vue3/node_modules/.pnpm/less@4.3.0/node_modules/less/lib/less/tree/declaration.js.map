{"version": 3, "file": "declaration.js", "sourceRoot": "", "sources": ["../../../src/less/tree/declaration.js"], "names": [], "mappings": ";;;AAAA,wDAA0B;AAC1B,0DAA4B;AAC5B,8DAAgC;AAChC,kEAAoC;AACpC,8DAA0C;AAC1C,IAAM,IAAI,GAAG,SAAS,CAAC,IAAI,CAAC;AAE5B,SAAS,QAAQ,CAAC,OAAO,EAAE,IAAI;IAC3B,IAAI,KAAK,GAAG,EAAE,CAAC;IACf,IAAI,CAAC,CAAC;IACN,IAAM,CAAC,GAAG,IAAI,CAAC,MAAM,CAAC;IACtB,IAAM,MAAM,GAAG,EAAC,GAAG,EAAE,UAAU,CAAC,IAAG,KAAK,IAAI,CAAC,CAAC,CAAA,CAAC,EAAC,CAAC;IACjD,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE;QACpB,IAAI,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,MAAM,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC;KACjD;IACD,OAAO,KAAK,CAAC;AACjB,CAAC;AAED,IAAM,WAAW,GAAG,UAAS,IAAI,EAAE,KAAK,EAAE,SAAS,EAAE,KAAK,EAAE,KAAK,EAAE,eAAe,EAAE,MAAM,EAAE,QAAQ;IAChG,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;IACjB,IAAI,CAAC,KAAK,GAAG,CAAC,KAAK,YAAY,cAAI,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,eAAK,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,mBAAS,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC;IAChG,IAAI,CAAC,SAAS,GAAG,SAAS,CAAC,CAAC,CAAC,WAAI,SAAS,CAAC,IAAI,EAAE,CAAE,CAAC,CAAC,CAAC,EAAE,CAAC;IACzD,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;IACnB,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC;IACpB,IAAI,CAAC,SAAS,GAAG,eAAe,CAAC;IACjC,IAAI,CAAC,MAAM,GAAG,MAAM,IAAI,KAAK,CAAC;IAC9B,IAAI,CAAC,QAAQ,GAAG,CAAC,QAAQ,KAAK,SAAS,CAAC,CAAC,CAAC,CAAC,QAAQ;QAC/C,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC;IAChD,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;IACtB,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC;AACrC,CAAC,CAAC;AAEF,WAAW,CAAC,SAAS,GAAG,MAAM,CAAC,MAAM,CAAC,IAAI,cAAI,EAAE,EAAE;IAC9C,IAAI,EAAE,aAAa;IAEnB,MAAM,YAAC,OAAO,EAAE,MAAM;QAClB,MAAM,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,GAAG,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC,EAAE,IAAI,CAAC,QAAQ,EAAE,EAAE,IAAI,CAAC,QAAQ,EAAE,CAAC,CAAC;QAC1F,IAAI;YACA,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC;SACtC;QACD,OAAO,CAAC,EAAE;YACN,CAAC,CAAC,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC;YACtB,CAAC,CAAC,QAAQ,GAAG,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC;YACrC,MAAM,CAAC,CAAC;SACX;QACD,MAAM,CAAC,GAAG,CAAC,IAAI,CAAC,SAAS,GAAG,CAAC,CAAC,IAAI,CAAC,MAAM,IAAI,CAAC,OAAO,CAAC,QAAQ,IAAI,OAAO,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC,EAAE,IAAI,CAAC,SAAS,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC;IACnI,CAAC;IAED,IAAI,YAAC,OAAO;QACR,IAAI,UAAU,GAAG,KAAK,EAAE,QAAQ,EAAE,IAAI,GAAG,IAAI,CAAC,IAAI,EAAE,UAAU,EAAE,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC;QACzF,IAAI,OAAO,IAAI,KAAK,QAAQ,EAAE;YAC1B,0CAA0C;YAC1C,2CAA2C;YAC3C,IAAI,GAAG,CAAC,IAAI,CAAC,MAAM,KAAK,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,YAAY,iBAAO,CAAC,CAAC,CAAC;gBACxD,IAAI,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,QAAQ,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC;YAC5C,QAAQ,GAAG,KAAK,CAAC,CAAC,0DAA0D;SAC/E;QAED,+CAA+C;QAC/C,IAAI,IAAI,KAAK,MAAM,IAAI,OAAO,CAAC,IAAI,KAAK,IAAI,CAAC,MAAM,EAAE;YACjD,UAAU,GAAG,IAAI,CAAC;YAClB,QAAQ,GAAG,OAAO,CAAC,IAAI,CAAC;YACxB,OAAO,CAAC,IAAI,GAAG,IAAI,CAAC,eAAe,CAAC;SACvC;QACD,IAAI;YACA,OAAO,CAAC,cAAc,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;YAChC,UAAU,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;YAEtC,IAAI,CAAC,IAAI,CAAC,QAAQ,IAAI,UAAU,CAAC,IAAI,KAAK,iBAAiB,EAAE;gBACzD,MAAM,EAAE,OAAO,EAAE,6CAA6C;oBAC1D,KAAK,EAAE,IAAI,CAAC,QAAQ,EAAE,EAAE,QAAQ,EAAE,IAAI,CAAC,QAAQ,EAAE,CAAC,QAAQ,EAAE,CAAC;aACpE;YACD,IAAI,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC;YAC/B,IAAM,eAAe,GAAG,OAAO,CAAC,cAAc,CAAC,GAAG,EAAE,CAAC;YACrD,IAAI,CAAC,SAAS,IAAI,eAAe,CAAC,SAAS,EAAE;gBACzC,SAAS,GAAG,eAAe,CAAC,SAAS,CAAC;aACzC;YAED,OAAO,IAAI,WAAW,CAAC,IAAI,EACvB,UAAU,EACV,SAAS,EACT,IAAI,CAAC,KAAK,EACV,IAAI,CAAC,QAAQ,EAAE,EAAE,IAAI,CAAC,QAAQ,EAAE,EAAE,IAAI,CAAC,MAAM,EAC7C,QAAQ,CAAC,CAAC;SACjB;QACD,OAAO,CAAC,EAAE;YACN,IAAI,OAAO,CAAC,CAAC,KAAK,KAAK,QAAQ,EAAE;gBAC7B,CAAC,CAAC,KAAK,GAAG,IAAI,CAAC,QAAQ,EAAE,CAAC;gBAC1B,CAAC,CAAC,QAAQ,GAAG,IAAI,CAAC,QAAQ,EAAE,CAAC,QAAQ,CAAC;aACzC;YACD,MAAM,CAAC,CAAC;SACX;gBACO;YACJ,IAAI,UAAU,EAAE;gBACZ,OAAO,CAAC,IAAI,GAAG,QAAQ,CAAC;aAC3B;SACJ;IACL,CAAC;IAED,aAAa;QACT,OAAO,IAAI,WAAW,CAAC,IAAI,CAAC,IAAI,EAC5B,IAAI,CAAC,KAAK,EACV,YAAY,EACZ,IAAI,CAAC,KAAK,EACV,IAAI,CAAC,QAAQ,EAAE,EAAE,IAAI,CAAC,QAAQ,EAAE,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC;IACvD,CAAC;CACJ,CAAC,CAAC;AAEH,kBAAe,WAAW,CAAC", "sourcesContent": ["import Node from './node';\nimport Value from './value';\nimport Keyword from './keyword';\nimport Anonymous from './anonymous';\nimport * as Constants from '../constants';\nconst MATH = Constants.Math;\n\nfunction evalName(context, name) {\n    let value = '';\n    let i;\n    const n = name.length;\n    const output = {add: function (s) {value += s;}};\n    for (i = 0; i < n; i++) {\n        name[i].eval(context).genCSS(context, output);\n    }\n    return value;\n}\n\nconst Declaration = function(name, value, important, merge, index, currentFileInfo, inline, variable) {\n    this.name = name;\n    this.value = (value instanceof Node) ? value : new Value([value ? new Anonymous(value) : null]);\n    this.important = important ? ` ${important.trim()}` : '';\n    this.merge = merge;\n    this._index = index;\n    this._fileInfo = currentFileInfo;\n    this.inline = inline || false;\n    this.variable = (variable !== undefined) ? variable\n        : (name.charAt && (name.charAt(0) === '@'));\n    this.allowRoot = true;\n    this.setParent(this.value, this);\n};\n\nDeclaration.prototype = Object.assign(new Node(), {\n    type: 'Declaration',\n\n    genCSS(context, output) {\n        output.add(this.name + (context.compress ? ':' : ': '), this.fileInfo(), this.getIndex());\n        try {\n            this.value.genCSS(context, output);\n        }\n        catch (e) {\n            e.index = this._index;\n            e.filename = this._fileInfo.filename;\n            throw e;\n        }\n        output.add(this.important + ((this.inline || (context.lastRule && context.compress)) ? '' : ';'), this._fileInfo, this._index);\n    },\n\n    eval(context) {\n        let mathBypass = false, prevMath, name = this.name, evaldValue, variable = this.variable;\n        if (typeof name !== 'string') {\n            // expand 'primitive' name directly to get\n            // things faster (~10% for benchmark.less):\n            name = (name.length === 1) && (name[0] instanceof Keyword) ?\n                name[0].value : evalName(context, name);\n            variable = false; // never treat expanded interpolation as new variable name\n        }\n\n        // @todo remove when parens-division is default\n        if (name === 'font' && context.math === MATH.ALWAYS) {\n            mathBypass = true;\n            prevMath = context.math;\n            context.math = MATH.PARENS_DIVISION;\n        }\n        try {\n            context.importantScope.push({});\n            evaldValue = this.value.eval(context);\n\n            if (!this.variable && evaldValue.type === 'DetachedRuleset') {\n                throw { message: 'Rulesets cannot be evaluated on a property.',\n                    index: this.getIndex(), filename: this.fileInfo().filename };\n            }\n            let important = this.important;\n            const importantResult = context.importantScope.pop();\n            if (!important && importantResult.important) {\n                important = importantResult.important;\n            }\n\n            return new Declaration(name,\n                evaldValue,\n                important,\n                this.merge,\n                this.getIndex(), this.fileInfo(), this.inline,\n                variable);\n        }\n        catch (e) {\n            if (typeof e.index !== 'number') {\n                e.index = this.getIndex();\n                e.filename = this.fileInfo().filename;\n            }\n            throw e;\n        }\n        finally {\n            if (mathBypass) {\n                context.math = prevMath;\n            }\n        }\n    },\n\n    makeImportant() {\n        return new Declaration(this.name,\n            this.value,\n            '!important',\n            this.merge,\n            this.getIndex(), this.fileInfo(), this.inline);\n    }\n});\n\nexport default Declaration;"]}