{"version": 3, "file": "parse-tree.js", "sourceRoot": "", "sources": ["../../src/less/parse-tree.js"], "names": [], "mappings": ";;;AAAA,oEAAqC;AACrC,4EAA6C;AAC7C,4DAA8B;AAE9B,mBAAwB,gBAAgB;IACpC;QACI,mBAAY,IAAI,EAAE,OAAO;YACrB,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;YACjB,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC;QAC3B,CAAC;QAED,yBAAK,GAAL,UAAM,OAAO;YACT,IAAI,SAAS,CAAC;YACd,IAAM,MAAM,GAAG,EAAE,CAAC;YAClB,IAAI,gBAAgB,CAAC;YACrB,IAAI;gBACA,SAAS,GAAG,IAAA,wBAAa,EAAC,IAAI,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;aACjD;YAAC,OAAO,CAAC,EAAE;gBACR,MAAM,IAAI,oBAAS,CAAC,CAAC,EAAE,IAAI,CAAC,OAAO,CAAC,CAAC;aACxC;YAED,IAAI;gBACA,IAAM,QAAQ,GAAG,OAAO,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;gBAC3C,IAAI,QAAQ,EAAE;oBACV,gBAAM,CAAC,IAAI,CAAC,2CAA2C;wBACnD,wFAAwF,CAAC,CAAC;iBACjG;gBAED,IAAM,YAAY,GAAG;oBACjB,QAAQ,UAAA;oBACR,eAAe,EAAE,OAAO,CAAC,eAAe;oBACxC,WAAW,EAAE,OAAO,CAAC,OAAO,CAAC,WAAW,CAAC;oBACzC,YAAY,EAAE,CAAC;iBAAC,CAAC;gBAErB,IAAI,OAAO,CAAC,SAAS,EAAE;oBACnB,gBAAgB,GAAG,IAAI,gBAAgB,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC;oBAC3D,MAAM,CAAC,GAAG,GAAG,gBAAgB,CAAC,KAAK,CAAC,SAAS,EAAE,YAAY,EAAE,IAAI,CAAC,OAAO,CAAC,CAAC;iBAC9E;qBAAM;oBACH,MAAM,CAAC,GAAG,GAAG,SAAS,CAAC,KAAK,CAAC,YAAY,CAAC,CAAC;iBAC9C;aACJ;YAAC,OAAO,CAAC,EAAE;gBACR,MAAM,IAAI,oBAAS,CAAC,CAAC,EAAE,IAAI,CAAC,OAAO,CAAC,CAAC;aACxC;YAED,IAAI,OAAO,CAAC,aAAa,EAAE;gBACvB,IAAM,cAAc,GAAG,OAAO,CAAC,aAAa,CAAC,iBAAiB,EAAE,CAAC;gBACjE,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,cAAc,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;oBAC5C,MAAM,CAAC,GAAG,GAAG,cAAc,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,MAAM,CAAC,GAAG,EAAE,EAAE,SAAS,EAAE,gBAAgB,EAAE,OAAO,SAAA,EAAE,OAAO,EAAE,IAAI,CAAC,OAAO,EAAE,CAAC,CAAC;iBACvH;aACJ;YACD,IAAI,OAAO,CAAC,SAAS,EAAE;gBACnB,MAAM,CAAC,GAAG,GAAG,gBAAgB,CAAC,oBAAoB,EAAE,CAAC;aACxD;YAED,MAAM,CAAC,OAAO,GAAG,EAAE,CAAC;YACpB,KAAK,IAAM,IAAI,IAAI,IAAI,CAAC,OAAO,CAAC,KAAK,EAAE;gBACnC,IAAI,MAAM,CAAC,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,KAAK,EAAE,IAAI,CAAC,IAAI,IAAI,KAAK,IAAI,CAAC,OAAO,CAAC,YAAY,EAAE;oBACtG,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;iBAC7B;aACJ;YACD,OAAO,MAAM,CAAC;QAClB,CAAC;QACL,gBAAC;IAAD,CAAC,AAzDD,IAyDC;IAED,OAAO,SAAS,CAAC;AACrB,CAAC;AA7DD,4BA6DC", "sourcesContent": ["import LessError from './less-error';\nimport transformTree from './transform-tree';\nimport logger from './logger';\n\nexport default function(SourceMapBuilder) {\n    class ParseTree {\n        constructor(root, imports) {\n            this.root = root;\n            this.imports = imports;\n        }\n\n        toCSS(options) {\n            let evaldRoot;\n            const result = {};\n            let sourceMapBuilder;\n            try {\n                evaldRoot = transformTree(this.root, options);\n            } catch (e) {\n                throw new LessError(e, this.imports);\n            }\n\n            try {\n                const compress = Boolean(options.compress);\n                if (compress) {\n                    logger.warn('The compress option has been deprecated. ' + \n                        'We recommend you use a dedicated css minifier, for instance see less-plugin-clean-css.');\n                }\n\n                const toCSSOptions = {\n                    compress,\n                    dumpLineNumbers: options.dumpLineNumbers,\n                    strictUnits: Boolean(options.strictUnits),\n                    numPrecision: 8};\n\n                if (options.sourceMap) {\n                    sourceMapBuilder = new SourceMapBuilder(options.sourceMap);\n                    result.css = sourceMapBuilder.toCSS(evaldRoot, toCSSOptions, this.imports);\n                } else {\n                    result.css = evaldRoot.toCSS(toCSSOptions);\n                }\n            } catch (e) {\n                throw new LessError(e, this.imports);\n            }\n\n            if (options.pluginManager) {\n                const postProcessors = options.pluginManager.getPostProcessors();\n                for (let i = 0; i < postProcessors.length; i++) {\n                    result.css = postProcessors[i].process(result.css, { sourceMap: sourceMapBuilder, options, imports: this.imports });\n                }\n            }\n            if (options.sourceMap) {\n                result.map = sourceMapBuilder.getExternalSourceMap();\n            }\n\n            result.imports = [];\n            for (const file in this.imports.files) {\n                if (Object.prototype.hasOwnProperty.call(this.imports.files, file) && file !== this.imports.rootFilename) {\n                    result.imports.push(file);\n                }\n            }\n            return result;\n        }\n    }\n\n    return ParseTree;\n}\n"]}