{"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["../../../src/less/visitors/index.js"], "names": [], "mappings": ";;;AAAA,8DAAgC;AAChC,4EAA6C;AAC7C,sGAAwE;AACxE,4EAA6C;AAC7C,0FAA0D;AAC1D,4EAA4C;AAE5C,kBAAe;IACX,OAAO,mBAAA;IACP,aAAa,0BAAA;IACb,2BAA2B,uCAAA;IAC3B,aAAa,0BAAA;IACb,mBAAmB,iCAAA;IACnB,YAAY,0BAAA;CACf,CAAC", "sourcesContent": ["import Visitor from './visitor';\nimport ImportVisitor from './import-visitor';\nimport MarkVisibleSelectorsVisitor from './set-tree-visibility-visitor';\nimport ExtendVisitor from './extend-visitor';\nimport JoinSelectorVisitor from './join-selector-visitor';\nimport ToCSSVisitor from './to-css-visitor';\n\nexport default {\n    Visitor,\n    ImportVisitor,\n    MarkVisibleSelectorsVisitor,\n    ExtendVisitor,\n    JoinSelectorVisitor,\n    ToCSSVisitor\n};\n"]}