{"version": 3, "file": "import-sequencer.js", "sourceRoot": "", "sources": ["../../../src/less/visitors/import-sequencer.js"], "names": [], "mappings": ";;AAAA;IACI,yBAAY,gBAAgB;QACxB,IAAI,CAAC,OAAO,GAAG,EAAE,CAAC;QAClB,IAAI,CAAC,eAAe,GAAG,EAAE,CAAC;QAC1B,IAAI,CAAC,iBAAiB,GAAG,gBAAgB,CAAC;QAC1C,IAAI,CAAC,aAAa,GAAG,CAAC,CAAC;IAC3B,CAAC;IAED,mCAAS,GAAT,UAAU,QAAQ;QACd,IAAM,eAAe,GAAG,IAAI,EACxB,UAAU,GAAG;YACT,QAAQ,UAAA;YACR,IAAI,EAAE,IAAI;YACV,OAAO,EAAE,KAAK;SACjB,CAAC;QACN,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;QAC9B,OAAO;YACH,UAAU,CAAC,IAAI,GAAG,KAAK,CAAC,SAAS,CAAC,KAAK,CAAC,IAAI,CAAC,SAAS,EAAE,CAAC,CAAC,CAAC;YAC3D,UAAU,CAAC,OAAO,GAAG,IAAI,CAAC;YAC1B,eAAe,CAAC,MAAM,EAAE,CAAC;QAC7B,CAAC,CAAC;IACN,CAAC;IAED,2CAAiB,GAAjB,UAAkB,QAAQ;QACtB,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;IACxC,CAAC;IAED,gCAAM,GAAN;QACI,IAAI,CAAC,aAAa,EAAE,CAAC;QACrB,IAAI;YACA,OAAO,IAAI,EAAE;gBACT,OAAO,IAAI,CAAC,OAAO,CAAC,MAAM,GAAG,CAAC,EAAE;oBAC5B,IAAM,UAAU,GAAG,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;oBACnC,IAAI,CAAC,UAAU,CAAC,OAAO,EAAE;wBACrB,OAAO;qBACV;oBACD,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;oBACrC,UAAU,CAAC,QAAQ,CAAC,KAAK,CAAC,IAAI,EAAE,UAAU,CAAC,IAAI,CAAC,CAAC;iBACpD;gBACD,IAAI,IAAI,CAAC,eAAe,CAAC,MAAM,KAAK,CAAC,EAAE;oBACnC,MAAM;iBACT;gBACD,IAAM,cAAc,GAAG,IAAI,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC;gBAC/C,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC,eAAe,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;gBACrD,cAAc,EAAE,CAAC;aACpB;SACJ;gBAAS;YACN,IAAI,CAAC,aAAa,EAAE,CAAC;SACxB;QACD,IAAI,IAAI,CAAC,aAAa,KAAK,CAAC,IAAI,IAAI,CAAC,iBAAiB,EAAE;YACpD,IAAI,CAAC,iBAAiB,EAAE,CAAC;SAC5B;IACL,CAAC;IACL,sBAAC;AAAD,CAAC,AArDD,IAqDC;AAED,kBAAe,eAAe,CAAC", "sourcesContent": ["class ImportSequencer {\n    constructor(onSequencerEmpty) {\n        this.imports = [];\n        this.variableImports = [];\n        this._onSequencerEmpty = onSequencerEmpty;\n        this._currentDepth = 0;\n    }\n\n    addImport(callback) {\n        const importSequencer = this,\n            importItem = {\n                callback,\n                args: null,\n                isReady: false\n            };\n        this.imports.push(importItem);\n        return function() {\n            importItem.args = Array.prototype.slice.call(arguments, 0);\n            importItem.isReady = true;\n            importSequencer.tryRun();\n        };\n    }\n\n    addVariableImport(callback) {\n        this.variableImports.push(callback);\n    }\n\n    tryRun() {\n        this._currentDepth++;\n        try {\n            while (true) {\n                while (this.imports.length > 0) {\n                    const importItem = this.imports[0];\n                    if (!importItem.isReady) {\n                        return;\n                    }\n                    this.imports = this.imports.slice(1);\n                    importItem.callback.apply(null, importItem.args);\n                }\n                if (this.variableImports.length === 0) {\n                    break;\n                }\n                const variableImport = this.variableImports[0];\n                this.variableImports = this.variableImports.slice(1);\n                variableImport();\n            }\n        } finally {\n            this._currentDepth--;\n        }\n        if (this._currentDepth === 0 && this._onSequencerEmpty) {\n            this._onSequencerEmpty();\n        }\n    }\n}\n\nexport default ImportSequencer;\n"]}