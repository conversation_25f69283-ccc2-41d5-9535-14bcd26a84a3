{"version": 3, "file": "extend-visitor.js", "sourceRoot": "", "sources": ["../../../src/less/visitors/extend-visitor.js"], "names": [], "mappings": ";;;AAAA,mCAAmC;AACnC;;GAEG;AACH,yDAA2B;AAC3B,8DAAgC;AAChC,6DAA+B;AAC/B,sDAAkC;AAElC,0BAA0B;AAE1B;IACI;QACI,IAAI,CAAC,QAAQ,GAAG,IAAI,iBAAO,CAAC,IAAI,CAAC,CAAC;QAClC,IAAI,CAAC,QAAQ,GAAG,EAAE,CAAC;QACnB,IAAI,CAAC,eAAe,GAAG,CAAC,EAAE,CAAC,CAAC;IAChC,CAAC;IAED,iCAAG,GAAH,UAAI,IAAI;QACJ,IAAI,GAAG,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;QACjC,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC;QAC1C,OAAO,IAAI,CAAC;IAChB,CAAC;IAED,8CAAgB,GAAhB,UAAiB,QAAQ,EAAE,SAAS;QAChC,SAAS,CAAC,WAAW,GAAG,KAAK,CAAC;IAClC,CAAC;IAED,kDAAoB,GAApB,UAAqB,mBAAmB,EAAE,SAAS;QAC/C,SAAS,CAAC,WAAW,GAAG,KAAK,CAAC;IAClC,CAAC;IAED,0CAAY,GAAZ,UAAa,WAAW,EAAE,SAAS;QAC/B,IAAI,WAAW,CAAC,IAAI,EAAE;YAClB,OAAO;SACV;QAED,IAAI,CAAC,CAAC;QACN,IAAI,CAAC,CAAC;QACN,IAAI,MAAM,CAAC;QACX,IAAM,sBAAsB,GAAG,EAAE,CAAC;QAClC,IAAI,UAAU,CAAC;QAEf,uEAAuE;QACvE,IAAM,KAAK,GAAG,WAAW,CAAC,KAAK,EAAE,OAAO,GAAG,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;QACpE,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,OAAO,EAAE,CAAC,EAAE,EAAE;YAC1B,IAAI,WAAW,CAAC,KAAK,CAAC,CAAC,CAAC,YAAY,cAAI,CAAC,MAAM,EAAE;gBAC7C,sBAAsB,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;gBACtC,WAAW,CAAC,iBAAiB,GAAG,IAAI,CAAC;aACxC;SACJ;QAED,0EAA0E;QAC1E,mDAAmD;QACnD,IAAM,KAAK,GAAG,WAAW,CAAC,KAAK,CAAC;QAChC,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YAC/B,IAAM,YAAY,GAAG,KAAK,CAAC,CAAC,CAAC,EAAE,QAAQ,GAAG,YAAY,CAAC,YAAY,CAAC,MAAM,GAAG,CAAC,CAAC,EAAE,aAAa,GAAG,QAAQ,CAAC,UAAU,CAAC;YAErH,UAAU,GAAG,aAAa,CAAC,CAAC,CAAC,KAAK,CAAC,SAAS,CAAC,aAAa,CAAC,CAAC,MAAM,CAAC,sBAAsB,CAAC;gBACtF,CAAC,CAAC,sBAAsB,CAAC;YAE7B,IAAI,UAAU,EAAE;gBACZ,UAAU,GAAG,UAAU,CAAC,GAAG,CAAC,UAAS,kBAAkB;oBACnD,OAAO,kBAAkB,CAAC,KAAK,EAAE,CAAC;gBACtC,CAAC,CAAC,CAAC;aACN;YAED,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,UAAU,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;gBACpC,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC;gBACzB,MAAM,GAAG,UAAU,CAAC,CAAC,CAAC,CAAC;gBACvB,MAAM,CAAC,iBAAiB,CAAC,YAAY,CAAC,CAAC;gBACvC,MAAM,CAAC,OAAO,GAAG,WAAW,CAAC;gBAC7B,IAAI,CAAC,KAAK,CAAC,EAAE;oBAAE,MAAM,CAAC,6BAA6B,GAAG,IAAI,CAAC;iBAAE;gBAC7D,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,eAAe,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;aACtE;SACJ;QAED,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,WAAW,CAAC,SAAS,CAAC,CAAC;IAC9C,CAAC;IAED,6CAAe,GAAf,UAAgB,WAAW;QACvB,IAAI,CAAC,WAAW,CAAC,IAAI,EAAE;YACnB,IAAI,CAAC,QAAQ,CAAC,MAAM,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,GAAG,CAAC,CAAC;SACnD;IACL,CAAC;IAED,wCAAU,GAAV,UAAW,SAAS,EAAE,SAAS;QAC3B,SAAS,CAAC,UAAU,GAAG,EAAE,CAAC;QAC1B,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,SAAS,CAAC,UAAU,CAAC,CAAC;IACpD,CAAC;IAED,2CAAa,GAAb,UAAc,SAAS;QACnB,IAAI,CAAC,eAAe,CAAC,MAAM,GAAG,IAAI,CAAC,eAAe,CAAC,MAAM,GAAG,CAAC,CAAC;IAClE,CAAC;IAED,yCAAW,GAAX,UAAY,UAAU,EAAE,SAAS;QAC7B,UAAU,CAAC,UAAU,GAAG,EAAE,CAAC;QAC3B,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,UAAU,CAAC,UAAU,CAAC,CAAC;IACrD,CAAC;IAED,4CAAc,GAAd,UAAe,UAAU;QACrB,IAAI,CAAC,eAAe,CAAC,MAAM,GAAG,IAAI,CAAC,eAAe,CAAC,MAAM,GAAG,CAAC,CAAC;IAClE,CAAC;IACL,0BAAC;AAAD,CAAC,AA5FD,IA4FC;AAED;IACI;QACI,IAAI,CAAC,QAAQ,GAAG,IAAI,iBAAO,CAAC,IAAI,CAAC,CAAC;IACtC,CAAC;IAED,mCAAG,GAAH,UAAI,IAAI;QACJ,IAAM,YAAY,GAAG,IAAI,mBAAmB,EAAE,CAAC;QAC/C,IAAI,CAAC,aAAa,GAAG,EAAE,CAAC;QACxB,YAAY,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;QACvB,IAAI,CAAC,YAAY,CAAC,YAAY,EAAE;YAAE,OAAO,IAAI,CAAC;SAAE;QAChD,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,UAAU,EAAE,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC;QAClG,IAAI,CAAC,eAAe,GAAG,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;QACzC,IAAM,OAAO,GAAG,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;QAC1C,IAAI,CAAC,yBAAyB,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;QAChD,OAAO,OAAO,CAAC;IACnB,CAAC;IAED,yDAAyB,GAAzB,UAA0B,UAAU;QAChC,IAAM,OAAO,GAAG,IAAI,CAAC,aAAa,CAAC;QACnC,UAAU,CAAC,MAAM,CAAC,UAAS,MAAM;YAC7B,OAAO,CAAC,MAAM,CAAC,eAAe,IAAI,MAAM,CAAC,UAAU,CAAC,MAAM,IAAI,CAAC,CAAC;QACpE,CAAC,CAAC,CAAC,OAAO,CAAC,UAAS,MAAM;YACtB,IAAI,QAAQ,GAAG,WAAW,CAAC;YAC3B,IAAI;gBACA,QAAQ,GAAG,MAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC;aACxC;YACD,OAAO,CAAC,EAAE,GAAE;YAEZ,IAAI,CAAC,OAAO,CAAC,UAAG,MAAM,CAAC,KAAK,cAAI,QAAQ,CAAE,CAAC,EAAE;gBACzC,OAAO,CAAC,UAAG,MAAM,CAAC,KAAK,cAAI,QAAQ,CAAE,CAAC,GAAG,IAAI,CAAC;gBAC9C;;;;mBAIG;gBACH,gBAAM,CAAC,IAAI,CAAC,2BAAoB,QAAQ,qBAAkB,CAAC,CAAC;aAC/D;QACL,CAAC,CAAC,CAAC;IACP,CAAC;IAED,gDAAgB,GAAhB,UAAiB,WAAW,EAAE,iBAAiB,EAAE,cAAc;QAC3D,EAAE;QACF,8GAA8G;QAC9G,gHAAgH;QAChH,iEAAiE;QACjE,EAAE;QACF,uHAAuH;QACvH,oHAAoH;QACpH,8EAA8E;QAE9E,IAAI,WAAW,CAAC;QAEhB,IAAI,iBAAiB,CAAC;QACtB,IAAI,OAAO,CAAC;QACZ,IAAM,YAAY,GAAG,EAAE,CAAC;QACxB,IAAI,WAAW,CAAC;QAChB,IAAM,aAAa,GAAG,IAAI,CAAC;QAC3B,IAAI,YAAY,CAAC;QACjB,IAAI,MAAM,CAAC;QACX,IAAI,YAAY,CAAC;QACjB,IAAI,SAAS,CAAC;QAEd,cAAc,GAAG,cAAc,IAAI,CAAC,CAAC;QAErC,gEAAgE;QAChE,yFAAyF;QACzF,4FAA4F;QAC5F,gCAAgC;QAChC,qGAAqG;QACrG,qCAAqC;QACrC,KAAK,WAAW,GAAG,CAAC,EAAE,WAAW,GAAG,WAAW,CAAC,MAAM,EAAE,WAAW,EAAE,EAAE;YACnE,KAAK,iBAAiB,GAAG,CAAC,EAAE,iBAAiB,GAAG,iBAAiB,CAAC,MAAM,EAAE,iBAAiB,EAAE,EAAE;gBAE3F,MAAM,GAAG,WAAW,CAAC,WAAW,CAAC,CAAC;gBAClC,YAAY,GAAG,iBAAiB,CAAC,iBAAiB,CAAC,CAAC;gBAEpD,+BAA+B;gBAC/B,IAAK,MAAM,CAAC,UAAU,CAAC,OAAO,CAAE,YAAY,CAAC,SAAS,CAAE,IAAI,CAAC,EAAG;oBAAE,SAAS;iBAAE;gBAE7E,4EAA4E;gBAC5E,YAAY,GAAG,CAAC,YAAY,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC,CAAC;gBAC/C,OAAO,GAAG,aAAa,CAAC,SAAS,CAAC,MAAM,EAAE,YAAY,CAAC,CAAC;gBAExD,IAAI,OAAO,CAAC,MAAM,EAAE;oBAChB,MAAM,CAAC,eAAe,GAAG,IAAI,CAAC;oBAE9B,gDAAgD;oBAChD,MAAM,CAAC,aAAa,CAAC,OAAO,CAAC,UAAS,YAAY;wBAC9C,IAAM,IAAI,GAAG,YAAY,CAAC,cAAc,EAAE,CAAC;wBAE3C,8BAA8B;wBAC9B,WAAW,GAAG,aAAa,CAAC,cAAc,CAAC,OAAO,EAAE,YAAY,EAAE,YAAY,EAAE,MAAM,CAAC,SAAS,EAAE,CAAC,CAAC;wBAEpG,yCAAyC;wBACzC,SAAS,GAAG,IAAG,CAAC,cAAI,CAAC,MAAM,CAAC,CAAC,YAAY,CAAC,QAAQ,EAAE,YAAY,CAAC,MAAM,EAAE,CAAC,EAAE,YAAY,CAAC,QAAQ,EAAE,EAAE,IAAI,CAAC,CAAC;wBAC3G,SAAS,CAAC,aAAa,GAAG,WAAW,CAAC;wBAEtC,4DAA4D;wBAC5D,WAAW,CAAC,WAAW,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,UAAU,GAAG,CAAC,SAAS,CAAC,CAAC;wBAE7D,iCAAiC;wBACjC,YAAY,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;wBAC7B,SAAS,CAAC,OAAO,GAAG,YAAY,CAAC,OAAO,CAAC;wBAEzC,+CAA+C;wBAC/C,SAAS,CAAC,UAAU,GAAG,SAAS,CAAC,UAAU,CAAC,MAAM,CAAC,YAAY,CAAC,UAAU,EAAE,MAAM,CAAC,UAAU,CAAC,CAAC;wBAE/F,2EAA2E;wBAC3E,iEAAiE;wBACjE,kFAAkF;wBAClF,IAAI,YAAY,CAAC,6BAA6B,EAAE;4BAC5C,SAAS,CAAC,6BAA6B,GAAG,IAAI,CAAC;4BAC/C,YAAY,CAAC,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;yBAChD;oBACL,CAAC,CAAC,CAAC;iBACN;aACJ;SACJ;QAED,IAAI,YAAY,CAAC,MAAM,EAAE;YACrB,8DAA8D;YAC9D,2BAA2B;YAC3B,IAAI,CAAC,gBAAgB,EAAE,CAAC;YACxB,IAAI,cAAc,GAAG,GAAG,EAAE;gBACtB,IAAI,WAAW,GAAG,uBAAuB,CAAC;gBAC1C,IAAI,WAAW,GAAG,uBAAuB,CAAC;gBAC1C,IAAI;oBACA,WAAW,GAAG,YAAY,CAAC,CAAC,CAAC,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC,KAAK,EAAE,CAAC;oBACvD,WAAW,GAAG,YAAY,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,KAAK,EAAE,CAAC;iBAClD;gBACD,OAAO,CAAC,EAAE,GAAE;gBACZ,MAAM,EAAE,OAAO,EAAE,uFAAgF,WAAW,qBAAW,WAAW,MAAG,EAAC,CAAC;aAC1I;YAED,8GAA8G;YAC9G,mBAAmB;YACnB,OAAO,YAAY,CAAC,MAAM,CAAC,aAAa,CAAC,gBAAgB,CAAC,YAAY,EAAE,iBAAiB,EAAE,cAAc,GAAG,CAAC,CAAC,CAAC,CAAC;SACnH;aAAM;YACH,OAAO,YAAY,CAAC;SACvB;IACL,CAAC;IAED,gDAAgB,GAAhB,UAAiB,QAAQ,EAAE,SAAS;QAChC,SAAS,CAAC,WAAW,GAAG,KAAK,CAAC;IAClC,CAAC;IAED,oDAAoB,GAApB,UAAqB,mBAAmB,EAAE,SAAS;QAC/C,SAAS,CAAC,WAAW,GAAG,KAAK,CAAC;IAClC,CAAC;IAED,6CAAa,GAAb,UAAc,YAAY,EAAE,SAAS;QACjC,SAAS,CAAC,WAAW,GAAG,KAAK,CAAC;IAClC,CAAC;IAED,4CAAY,GAAZ,UAAa,WAAW,EAAE,SAAS;QAC/B,IAAI,WAAW,CAAC,IAAI,EAAE;YAClB,OAAO;SACV;QACD,IAAI,OAAO,CAAC;QACZ,IAAI,SAAS,CAAC;QACd,IAAI,WAAW,CAAC;QAChB,IAAM,UAAU,GAAG,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,eAAe,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;QACzE,IAAM,cAAc,GAAG,EAAE,CAAC;QAC1B,IAAM,aAAa,GAAG,IAAI,CAAC;QAC3B,IAAI,YAAY,CAAC;QAEjB,qGAAqG;QAErG,KAAK,WAAW,GAAG,CAAC,EAAE,WAAW,GAAG,UAAU,CAAC,MAAM,EAAE,WAAW,EAAE,EAAE;YAClE,KAAK,SAAS,GAAG,CAAC,EAAE,SAAS,GAAG,WAAW,CAAC,KAAK,CAAC,MAAM,EAAE,SAAS,EAAE,EAAE;gBACnE,YAAY,GAAG,WAAW,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC;gBAE5C,4DAA4D;gBAC5D,IAAI,WAAW,CAAC,iBAAiB,EAAE;oBAAE,SAAS;iBAAE;gBAChD,IAAM,UAAU,GAAG,YAAY,CAAC,YAAY,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,UAAU,CAAC;gBACpE,IAAI,UAAU,IAAI,UAAU,CAAC,MAAM,EAAE;oBAAE,SAAS;iBAAE;gBAElD,OAAO,GAAG,IAAI,CAAC,SAAS,CAAC,UAAU,CAAC,WAAW,CAAC,EAAE,YAAY,CAAC,CAAC;gBAEhE,IAAI,OAAO,CAAC,MAAM,EAAE;oBAChB,UAAU,CAAC,WAAW,CAAC,CAAC,eAAe,GAAG,IAAI,CAAC;oBAE/C,UAAU,CAAC,WAAW,CAAC,CAAC,aAAa,CAAC,OAAO,CAAC,UAAS,YAAY;wBAC/D,IAAI,iBAAiB,CAAC;wBACtB,iBAAiB,GAAG,aAAa,CAAC,cAAc,CAAC,OAAO,EAAE,YAAY,EAAE,YAAY,EAAE,UAAU,CAAC,WAAW,CAAC,CAAC,SAAS,EAAE,CAAC,CAAC;wBAC3H,cAAc,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC;oBAC3C,CAAC,CAAC,CAAC;iBACN;aACJ;SACJ;QACD,WAAW,CAAC,KAAK,GAAG,WAAW,CAAC,KAAK,CAAC,MAAM,CAAC,cAAc,CAAC,CAAC;IACjE,CAAC;IAED,yCAAS,GAAT,UAAU,MAAM,EAAE,oBAAoB;QAClC,EAAE;QACF,uFAAuF;QACvF,iEAAiE;QACjE,EAAE;QACF,IAAI,qBAAqB,CAAC;QAE1B,IAAI,iBAAiB,CAAC;QACtB,IAAI,qBAAqB,CAAC;QAC1B,IAAI,eAAe,CAAC;QACpB,IAAI,gBAAgB,CAAC;QACrB,IAAI,CAAC,CAAC;QACN,IAAM,aAAa,GAAG,IAAI,CAAC;QAC3B,IAAM,cAAc,GAAG,MAAM,CAAC,QAAQ,CAAC,QAAQ,CAAC;QAChD,IAAM,gBAAgB,GAAG,EAAE,CAAC;QAC5B,IAAI,cAAc,CAAC;QACnB,IAAM,OAAO,GAAG,EAAE,CAAC;QAEnB,qCAAqC;QACrC,KAAK,qBAAqB,GAAG,CAAC,EAAE,qBAAqB,GAAG,oBAAoB,CAAC,MAAM,EAAE,qBAAqB,EAAE,EAAE;YAC1G,iBAAiB,GAAG,oBAAoB,CAAC,qBAAqB,CAAC,CAAC;YAEhE,KAAK,qBAAqB,GAAG,CAAC,EAAE,qBAAqB,GAAG,iBAAiB,CAAC,QAAQ,CAAC,MAAM,EAAE,qBAAqB,EAAE,EAAE;gBAEhH,eAAe,GAAG,iBAAiB,CAAC,QAAQ,CAAC,qBAAqB,CAAC,CAAC;gBAEpE,sHAAsH;gBACtH,IAAI,MAAM,CAAC,WAAW,IAAI,CAAC,qBAAqB,KAAK,CAAC,IAAI,qBAAqB,KAAK,CAAC,CAAC,EAAE;oBACpF,gBAAgB,CAAC,IAAI,CAAC,EAAC,SAAS,EAAE,qBAAqB,EAAE,KAAK,EAAE,qBAAqB,EAAE,OAAO,EAAE,CAAC;wBAC7F,iBAAiB,EAAE,eAAe,CAAC,UAAU,EAAC,CAAC,CAAC;iBACvD;gBAED,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,gBAAgB,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;oBAC1C,cAAc,GAAG,gBAAgB,CAAC,CAAC,CAAC,CAAC;oBAErC,2GAA2G;oBAC3G,2GAA2G;oBAC3G,iDAAiD;oBACjD,gBAAgB,GAAG,eAAe,CAAC,UAAU,CAAC,KAAK,CAAC;oBACpD,IAAI,gBAAgB,KAAK,EAAE,IAAI,qBAAqB,KAAK,CAAC,EAAE;wBACxD,gBAAgB,GAAG,GAAG,CAAC;qBAC1B;oBAED,wDAAwD;oBACxD,IAAI,CAAC,aAAa,CAAC,oBAAoB,CAAC,cAAc,CAAC,cAAc,CAAC,OAAO,CAAC,CAAC,KAAK,EAAE,eAAe,CAAC,KAAK,CAAC;wBACxG,CAAC,cAAc,CAAC,OAAO,GAAG,CAAC,IAAI,cAAc,CAAC,cAAc,CAAC,OAAO,CAAC,CAAC,UAAU,CAAC,KAAK,KAAK,gBAAgB,CAAC,EAAE;wBAC9G,cAAc,GAAG,IAAI,CAAC;qBACzB;yBAAM;wBACH,cAAc,CAAC,OAAO,EAAE,CAAC;qBAC5B;oBAED,6GAA6G;oBAC7G,IAAI,cAAc,EAAE;wBAChB,cAAc,CAAC,QAAQ,GAAG,cAAc,CAAC,OAAO,KAAK,cAAc,CAAC,MAAM,CAAC;wBAC3E,IAAI,cAAc,CAAC,QAAQ;4BACvB,CAAC,CAAC,MAAM,CAAC,UAAU;gCACf,CAAC,qBAAqB,GAAG,CAAC,GAAG,iBAAiB,CAAC,QAAQ,CAAC,MAAM,IAAI,qBAAqB,GAAG,CAAC,GAAG,oBAAoB,CAAC,MAAM,CAAC,CAAC,EAAE;4BACjI,cAAc,GAAG,IAAI,CAAC;yBACzB;qBACJ;oBACD,6FAA6F;oBAC7F,IAAI,cAAc,EAAE;wBAChB,IAAI,cAAc,CAAC,QAAQ,EAAE;4BACzB,cAAc,CAAC,MAAM,GAAG,cAAc,CAAC,MAAM,CAAC;4BAC9C,cAAc,CAAC,YAAY,GAAG,qBAAqB,CAAC;4BACpD,cAAc,CAAC,mBAAmB,GAAG,qBAAqB,GAAG,CAAC,CAAC,CAAC,2BAA2B;4BAC3F,gBAAgB,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,6DAA6D;4BAC1F,OAAO,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;yBAChC;qBACJ;yBAAM;wBACH,gBAAgB,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;wBAC9B,CAAC,EAAE,CAAC;qBACP;iBACJ;aACJ;SACJ;QACD,OAAO,OAAO,CAAC;IACnB,CAAC;IAED,oDAAoB,GAApB,UAAqB,aAAa,EAAE,aAAa;QAC7C,IAAI,OAAO,aAAa,KAAK,QAAQ,IAAI,OAAO,aAAa,KAAK,QAAQ,EAAE;YACxE,OAAO,aAAa,KAAK,aAAa,CAAC;SAC1C;QACD,IAAI,aAAa,YAAY,cAAI,CAAC,SAAS,EAAE;YACzC,IAAI,aAAa,CAAC,EAAE,KAAK,aAAa,CAAC,EAAE,IAAI,aAAa,CAAC,GAAG,KAAK,aAAa,CAAC,GAAG,EAAE;gBAClF,OAAO,KAAK,CAAC;aAChB;YACD,IAAI,CAAC,aAAa,CAAC,KAAK,IAAI,CAAC,aAAa,CAAC,KAAK,EAAE;gBAC9C,IAAI,aAAa,CAAC,KAAK,IAAI,aAAa,CAAC,KAAK,EAAE;oBAC5C,OAAO,KAAK,CAAC;iBAChB;gBACD,OAAO,IAAI,CAAC;aACf;YACD,aAAa,GAAG,aAAa,CAAC,KAAK,CAAC,KAAK,IAAI,aAAa,CAAC,KAAK,CAAC;YACjE,aAAa,GAAG,aAAa,CAAC,KAAK,CAAC,KAAK,IAAI,aAAa,CAAC,KAAK,CAAC;YACjE,OAAO,aAAa,KAAK,aAAa,CAAC;SAC1C;QACD,aAAa,GAAG,aAAa,CAAC,KAAK,CAAC;QACpC,aAAa,GAAG,aAAa,CAAC,KAAK,CAAC;QACpC,IAAI,aAAa,YAAY,cAAI,CAAC,QAAQ,EAAE;YACxC,IAAI,CAAC,CAAC,aAAa,YAAY,cAAI,CAAC,QAAQ,CAAC,IAAI,aAAa,CAAC,QAAQ,CAAC,MAAM,KAAK,aAAa,CAAC,QAAQ,CAAC,MAAM,EAAE;gBAC9G,OAAO,KAAK,CAAC;aAChB;YACD,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAI,aAAa,CAAC,QAAQ,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;gBACrD,IAAI,aAAa,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,KAAK,KAAK,aAAa,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,KAAK,EAAE;oBAC3F,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,KAAK,IAAI,GAAG,CAAC,KAAK,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,KAAK,IAAI,GAAG,CAAC,EAAE;wBACxH,OAAO,KAAK,CAAC;qBAChB;iBACJ;gBACD,IAAI,CAAC,IAAI,CAAC,oBAAoB,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,KAAK,EAAE,aAAa,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,EAAE;oBAC9F,OAAO,KAAK,CAAC;iBAChB;aACJ;YACD,OAAO,IAAI,CAAC;SACf;QACD,OAAO,KAAK,CAAC;IACjB,CAAC;IAED,8CAAc,GAAd,UAAe,OAAO,EAAE,YAAY,EAAE,mBAAmB,EAAE,SAAS;QAEhE,yEAAyE;QAEzE,IAAI,wBAAwB,GAAG,CAAC,EAAE,+BAA+B,GAAG,CAAC,EAAE,IAAI,GAAG,EAAE,EAAE,UAAU,EAAE,QAAQ,EAAE,YAAY,EAAE,KAAK,EAAE,WAAW,CAAC;QAEzI,KAAK,UAAU,GAAG,CAAC,EAAE,UAAU,GAAG,OAAO,CAAC,MAAM,EAAE,UAAU,EAAE,EAAE;YAC5D,KAAK,GAAG,OAAO,CAAC,UAAU,CAAC,CAAC;YAC5B,QAAQ,GAAG,YAAY,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC;YACzC,YAAY,GAAG,IAAI,cAAI,CAAC,OAAO,CAC3B,KAAK,CAAC,iBAAiB,EACvB,mBAAmB,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,KAAK,EACrC,mBAAmB,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,UAAU,EAC1C,mBAAmB,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,QAAQ,EAAE,EAC1C,mBAAmB,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,QAAQ,EAAE,CAC7C,CAAC;YAEF,IAAI,KAAK,CAAC,SAAS,GAAG,wBAAwB,IAAI,+BAA+B,GAAG,CAAC,EAAE;gBACnF,IAAI,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,QAAQ,GAAG,IAAI,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC;qBACjD,QAAQ,CAAC,MAAM,CAAC,YAAY,CAAC,wBAAwB,CAAC,CAAC,QAAQ,CAAC,KAAK,CAAC,+BAA+B,CAAC,CAAC,CAAC;gBAC7G,+BAA+B,GAAG,CAAC,CAAC;gBACpC,wBAAwB,EAAE,CAAC;aAC9B;YAED,WAAW,GAAG,QAAQ,CAAC,QAAQ;iBAC1B,KAAK,CAAC,+BAA+B,EAAE,KAAK,CAAC,KAAK,CAAC;iBACnD,MAAM,CAAC,CAAC,YAAY,CAAC,CAAC;iBACtB,MAAM,CAAC,mBAAmB,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;YAEnD,IAAI,wBAAwB,KAAK,KAAK,CAAC,SAAS,IAAI,UAAU,GAAG,CAAC,EAAE;gBAChE,IAAI,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,QAAQ;oBAC1B,IAAI,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,QAAQ,CAAC,MAAM,CAAC,WAAW,CAAC,CAAC;aAC1D;iBAAM;gBACH,IAAI,GAAG,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC,KAAK,CAAC,wBAAwB,EAAE,KAAK,CAAC,SAAS,CAAC,CAAC,CAAC;gBAElF,IAAI,CAAC,IAAI,CAAC,IAAI,cAAI,CAAC,QAAQ,CACvB,WAAW,CACd,CAAC,CAAC;aACN;YACD,wBAAwB,GAAG,KAAK,CAAC,YAAY,CAAC;YAC9C,+BAA+B,GAAG,KAAK,CAAC,mBAAmB,CAAC;YAC5D,IAAI,+BAA+B,IAAI,YAAY,CAAC,wBAAwB,CAAC,CAAC,QAAQ,CAAC,MAAM,EAAE;gBAC3F,+BAA+B,GAAG,CAAC,CAAC;gBACpC,wBAAwB,EAAE,CAAC;aAC9B;SACJ;QAED,IAAI,wBAAwB,GAAG,YAAY,CAAC,MAAM,IAAI,+BAA+B,GAAG,CAAC,EAAE;YACvF,IAAI,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,QAAQ,GAAG,IAAI,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC;iBACjD,QAAQ,CAAC,MAAM,CAAC,YAAY,CAAC,wBAAwB,CAAC,CAAC,QAAQ,CAAC,KAAK,CAAC,+BAA+B,CAAC,CAAC,CAAC;YAC7G,wBAAwB,EAAE,CAAC;SAC9B;QAED,IAAI,GAAG,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC,KAAK,CAAC,wBAAwB,EAAE,YAAY,CAAC,MAAM,CAAC,CAAC,CAAC;QACtF,IAAI,GAAG,IAAI,CAAC,GAAG,CAAC,UAAU,YAAY;YAClC,0FAA0F;YAC1F,IAAM,OAAO,GAAG,YAAY,CAAC,aAAa,CAAC,YAAY,CAAC,QAAQ,CAAC,CAAC;YAClE,IAAI,SAAS,EAAE;gBACX,OAAO,CAAC,gBAAgB,EAAE,CAAC;aAC9B;iBAAM;gBACH,OAAO,CAAC,kBAAkB,EAAE,CAAC;aAChC;YACD,OAAO,OAAO,CAAC;QACnB,CAAC,CAAC,CAAC;QACH,OAAO,IAAI,CAAC;IAChB,CAAC;IAED,0CAAU,GAAV,UAAW,SAAS,EAAE,SAAS;QAC3B,IAAI,aAAa,GAAG,SAAS,CAAC,UAAU,CAAC,MAAM,CAAC,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,eAAe,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC;QACvG,aAAa,GAAG,aAAa,CAAC,MAAM,CAAC,IAAI,CAAC,gBAAgB,CAAC,aAAa,EAAE,SAAS,CAAC,UAAU,CAAC,CAAC,CAAC;QACjG,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;IAC7C,CAAC;IAED,6CAAa,GAAb,UAAc,SAAS;QACnB,IAAM,SAAS,GAAG,IAAI,CAAC,eAAe,CAAC,MAAM,GAAG,CAAC,CAAC;QAClD,IAAI,CAAC,eAAe,CAAC,MAAM,GAAG,SAAS,CAAC;IAC5C,CAAC;IAED,2CAAW,GAAX,UAAY,UAAU,EAAE,SAAS;QAC7B,IAAI,aAAa,GAAG,UAAU,CAAC,UAAU,CAAC,MAAM,CAAC,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,eAAe,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC;QACxG,aAAa,GAAG,aAAa,CAAC,MAAM,CAAC,IAAI,CAAC,gBAAgB,CAAC,aAAa,EAAE,UAAU,CAAC,UAAU,CAAC,CAAC,CAAC;QAClG,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;IAC7C,CAAC;IAED,8CAAc,GAAd,UAAe,UAAU;QACrB,IAAM,SAAS,GAAG,IAAI,CAAC,eAAe,CAAC,MAAM,GAAG,CAAC,CAAC;QAClD,IAAI,CAAC,eAAe,CAAC,MAAM,GAAG,SAAS,CAAC;IAC5C,CAAC;IACL,4BAAC;AAAD,CAAC,AA/YD,IA+YC;AAED,kBAAe,qBAAqB,CAAC", "sourcesContent": ["/* eslint-disable no-unused-vars */\n/**\n * @todo - Remove unused when JSDoc types are added for visitor methods\n */\nimport tree from '../tree';\nimport Visitor from './visitor';\nimport logger from '../logger';\nimport * as utils from '../utils';\n\n/* jshint loopfunc:true */\n\nclass ExtendFinderVisitor {\n    constructor() {\n        this._visitor = new Visitor(this);\n        this.contexts = [];\n        this.allExtendsStack = [[]];\n    }\n\n    run(root) {\n        root = this._visitor.visit(root);\n        root.allExtends = this.allExtendsStack[0];\n        return root;\n    }\n\n    visitDeclaration(declNode, visitArgs) {\n        visitArgs.visitDeeper = false;\n    }\n\n    visitMixinDefinition(mixinDefinitionNode, visitArgs) {\n        visitArgs.visitDeeper = false;\n    }\n\n    visitRuleset(rulesetNode, visitArgs) {\n        if (rulesetNode.root) {\n            return;\n        }\n\n        let i;\n        let j;\n        let extend;\n        const allSelectorsExtendList = [];\n        let extendList;\n\n        // get &:extend(.a); rules which apply to all selectors in this ruleset\n        const rules = rulesetNode.rules, ruleCnt = rules ? rules.length : 0;\n        for (i = 0; i < ruleCnt; i++) {\n            if (rulesetNode.rules[i] instanceof tree.Extend) {\n                allSelectorsExtendList.push(rules[i]);\n                rulesetNode.extendOnEveryPath = true;\n            }\n        }\n\n        // now find every selector and apply the extends that apply to all extends\n        // and the ones which apply to an individual extend\n        const paths = rulesetNode.paths;\n        for (i = 0; i < paths.length; i++) {\n            const selectorPath = paths[i], selector = selectorPath[selectorPath.length - 1], selExtendList = selector.extendList;\n\n            extendList = selExtendList ? utils.copyArray(selExtendList).concat(allSelectorsExtendList)\n                : allSelectorsExtendList;\n\n            if (extendList) {\n                extendList = extendList.map(function(allSelectorsExtend) {\n                    return allSelectorsExtend.clone();\n                });\n            }\n\n            for (j = 0; j < extendList.length; j++) {\n                this.foundExtends = true;\n                extend = extendList[j];\n                extend.findSelfSelectors(selectorPath);\n                extend.ruleset = rulesetNode;\n                if (j === 0) { extend.firstExtendOnThisSelectorPath = true; }\n                this.allExtendsStack[this.allExtendsStack.length - 1].push(extend);\n            }\n        }\n\n        this.contexts.push(rulesetNode.selectors);\n    }\n\n    visitRulesetOut(rulesetNode) {\n        if (!rulesetNode.root) {\n            this.contexts.length = this.contexts.length - 1;\n        }\n    }\n\n    visitMedia(mediaNode, visitArgs) {\n        mediaNode.allExtends = [];\n        this.allExtendsStack.push(mediaNode.allExtends);\n    }\n\n    visitMediaOut(mediaNode) {\n        this.allExtendsStack.length = this.allExtendsStack.length - 1;\n    }\n\n    visitAtRule(atRuleNode, visitArgs) {\n        atRuleNode.allExtends = [];\n        this.allExtendsStack.push(atRuleNode.allExtends);\n    }\n\n    visitAtRuleOut(atRuleNode) {\n        this.allExtendsStack.length = this.allExtendsStack.length - 1;\n    }\n}\n\nclass ProcessExtendsVisitor {\n    constructor() {\n        this._visitor = new Visitor(this);\n    }\n\n    run(root) {\n        const extendFinder = new ExtendFinderVisitor();\n        this.extendIndices = {};\n        extendFinder.run(root);\n        if (!extendFinder.foundExtends) { return root; }\n        root.allExtends = root.allExtends.concat(this.doExtendChaining(root.allExtends, root.allExtends));\n        this.allExtendsStack = [root.allExtends];\n        const newRoot = this._visitor.visit(root);\n        this.checkExtendsForNonMatched(root.allExtends);\n        return newRoot;\n    }\n\n    checkExtendsForNonMatched(extendList) {\n        const indices = this.extendIndices;\n        extendList.filter(function(extend) {\n            return !extend.hasFoundMatches && extend.parent_ids.length == 1;\n        }).forEach(function(extend) {\n            let selector = '_unknown_';\n            try {\n                selector = extend.selector.toCSS({});\n            }\n            catch (_) {}\n\n            if (!indices[`${extend.index} ${selector}`]) {\n                indices[`${extend.index} ${selector}`] = true;\n                /**\n                 * @todo Shouldn't this be an error? To alert the developer\n                 * that they may have made an error in the selector they are\n                 * targeting?\n                 */\n                logger.warn(`WARNING: extend '${selector}' has no matches`);\n            }\n        });\n    }\n\n    doExtendChaining(extendsList, extendsListTarget, iterationCount) {\n        //\n        // chaining is different from normal extension.. if we extend an extend then we are not just copying, altering\n        // and pasting the selector we would do normally, but we are also adding an extend with the same target selector\n        // this means this new extend can then go and alter other extends\n        //\n        // this method deals with all the chaining work - without it, extend is flat and doesn't work on other extend selectors\n        // this is also the most expensive.. and a match on one selector can cause an extension of a selector we had already\n        // processed if we look at each selector at a time, as is done in visitRuleset\n\n        let extendIndex;\n\n        let targetExtendIndex;\n        let matches;\n        const extendsToAdd = [];\n        let newSelector;\n        const extendVisitor = this;\n        let selectorPath;\n        let extend;\n        let targetExtend;\n        let newExtend;\n\n        iterationCount = iterationCount || 0;\n\n        // loop through comparing every extend with every target extend.\n        // a target extend is the one on the ruleset we are looking at copy/edit/pasting in place\n        // e.g.  .a:extend(.b) {}  and .b:extend(.c) {} then the first extend extends the second one\n        // and the second is the target.\n        // the separation into two lists allows us to process a subset of chains with a bigger set, as is the\n        // case when processing media queries\n        for (extendIndex = 0; extendIndex < extendsList.length; extendIndex++) {\n            for (targetExtendIndex = 0; targetExtendIndex < extendsListTarget.length; targetExtendIndex++) {\n\n                extend = extendsList[extendIndex];\n                targetExtend = extendsListTarget[targetExtendIndex];\n\n                // look for circular references\n                if ( extend.parent_ids.indexOf( targetExtend.object_id ) >= 0 ) { continue; }\n\n                // find a match in the target extends self selector (the bit before :extend)\n                selectorPath = [targetExtend.selfSelectors[0]];\n                matches = extendVisitor.findMatch(extend, selectorPath);\n\n                if (matches.length) {\n                    extend.hasFoundMatches = true;\n\n                    // we found a match, so for each self selector..\n                    extend.selfSelectors.forEach(function(selfSelector) {\n                        const info = targetExtend.visibilityInfo();\n\n                        // process the extend as usual\n                        newSelector = extendVisitor.extendSelector(matches, selectorPath, selfSelector, extend.isVisible());\n\n                        // but now we create a new extend from it\n                        newExtend = new(tree.Extend)(targetExtend.selector, targetExtend.option, 0, targetExtend.fileInfo(), info);\n                        newExtend.selfSelectors = newSelector;\n\n                        // add the extend onto the list of extends for that selector\n                        newSelector[newSelector.length - 1].extendList = [newExtend];\n\n                        // record that we need to add it.\n                        extendsToAdd.push(newExtend);\n                        newExtend.ruleset = targetExtend.ruleset;\n\n                        // remember its parents for circular references\n                        newExtend.parent_ids = newExtend.parent_ids.concat(targetExtend.parent_ids, extend.parent_ids);\n\n                        // only process the selector once.. if we have :extend(.a,.b) then multiple\n                        // extends will look at the same selector path, so when extending\n                        // we know that any others will be duplicates in terms of what is added to the css\n                        if (targetExtend.firstExtendOnThisSelectorPath) {\n                            newExtend.firstExtendOnThisSelectorPath = true;\n                            targetExtend.ruleset.paths.push(newSelector);\n                        }\n                    });\n                }\n            }\n        }\n\n        if (extendsToAdd.length) {\n            // try to detect circular references to stop a stack overflow.\n            // may no longer be needed.\n            this.extendChainCount++;\n            if (iterationCount > 100) {\n                let selectorOne = '{unable to calculate}';\n                let selectorTwo = '{unable to calculate}';\n                try {\n                    selectorOne = extendsToAdd[0].selfSelectors[0].toCSS();\n                    selectorTwo = extendsToAdd[0].selector.toCSS();\n                }\n                catch (e) {}\n                throw { message: `extend circular reference detected. One of the circular extends is currently:${selectorOne}:extend(${selectorTwo})`};\n            }\n\n            // now process the new extends on the existing rules so that we can handle a extending b extending c extending\n            // d extending e...\n            return extendsToAdd.concat(extendVisitor.doExtendChaining(extendsToAdd, extendsListTarget, iterationCount + 1));\n        } else {\n            return extendsToAdd;\n        }\n    }\n\n    visitDeclaration(ruleNode, visitArgs) {\n        visitArgs.visitDeeper = false;\n    }\n\n    visitMixinDefinition(mixinDefinitionNode, visitArgs) {\n        visitArgs.visitDeeper = false;\n    }\n\n    visitSelector(selectorNode, visitArgs) {\n        visitArgs.visitDeeper = false;\n    }\n\n    visitRuleset(rulesetNode, visitArgs) {\n        if (rulesetNode.root) {\n            return;\n        }\n        let matches;\n        let pathIndex;\n        let extendIndex;\n        const allExtends = this.allExtendsStack[this.allExtendsStack.length - 1];\n        const selectorsToAdd = [];\n        const extendVisitor = this;\n        let selectorPath;\n\n        // look at each selector path in the ruleset, find any extend matches and then copy, find and replace\n\n        for (extendIndex = 0; extendIndex < allExtends.length; extendIndex++) {\n            for (pathIndex = 0; pathIndex < rulesetNode.paths.length; pathIndex++) {\n                selectorPath = rulesetNode.paths[pathIndex];\n\n                // extending extends happens initially, before the main pass\n                if (rulesetNode.extendOnEveryPath) { continue; }\n                const extendList = selectorPath[selectorPath.length - 1].extendList;\n                if (extendList && extendList.length) { continue; }\n\n                matches = this.findMatch(allExtends[extendIndex], selectorPath);\n\n                if (matches.length) {\n                    allExtends[extendIndex].hasFoundMatches = true;\n\n                    allExtends[extendIndex].selfSelectors.forEach(function(selfSelector) {\n                        let extendedSelectors;\n                        extendedSelectors = extendVisitor.extendSelector(matches, selectorPath, selfSelector, allExtends[extendIndex].isVisible());\n                        selectorsToAdd.push(extendedSelectors);\n                    });\n                }\n            }\n        }\n        rulesetNode.paths = rulesetNode.paths.concat(selectorsToAdd);\n    }\n\n    findMatch(extend, haystackSelectorPath) {\n        //\n        // look through the haystack selector path to try and find the needle - extend.selector\n        // returns an array of selector matches that can then be replaced\n        //\n        let haystackSelectorIndex;\n\n        let hackstackSelector;\n        let hackstackElementIndex;\n        let haystackElement;\n        let targetCombinator;\n        let i;\n        const extendVisitor = this;\n        const needleElements = extend.selector.elements;\n        const potentialMatches = [];\n        let potentialMatch;\n        const matches = [];\n\n        // loop through the haystack elements\n        for (haystackSelectorIndex = 0; haystackSelectorIndex < haystackSelectorPath.length; haystackSelectorIndex++) {\n            hackstackSelector = haystackSelectorPath[haystackSelectorIndex];\n\n            for (hackstackElementIndex = 0; hackstackElementIndex < hackstackSelector.elements.length; hackstackElementIndex++) {\n\n                haystackElement = hackstackSelector.elements[hackstackElementIndex];\n\n                // if we allow elements before our match we can add a potential match every time. otherwise only at the first element.\n                if (extend.allowBefore || (haystackSelectorIndex === 0 && hackstackElementIndex === 0)) {\n                    potentialMatches.push({pathIndex: haystackSelectorIndex, index: hackstackElementIndex, matched: 0,\n                        initialCombinator: haystackElement.combinator});\n                }\n\n                for (i = 0; i < potentialMatches.length; i++) {\n                    potentialMatch = potentialMatches[i];\n\n                    // selectors add \" \" onto the first element. When we use & it joins the selectors together, but if we don't\n                    // then each selector in haystackSelectorPath has a space before it added in the toCSS phase. so we need to\n                    // work out what the resulting combinator will be\n                    targetCombinator = haystackElement.combinator.value;\n                    if (targetCombinator === '' && hackstackElementIndex === 0) {\n                        targetCombinator = ' ';\n                    }\n\n                    // if we don't match, null our match to indicate failure\n                    if (!extendVisitor.isElementValuesEqual(needleElements[potentialMatch.matched].value, haystackElement.value) ||\n                        (potentialMatch.matched > 0 && needleElements[potentialMatch.matched].combinator.value !== targetCombinator)) {\n                        potentialMatch = null;\n                    } else {\n                        potentialMatch.matched++;\n                    }\n\n                    // if we are still valid and have finished, test whether we have elements after and whether these are allowed\n                    if (potentialMatch) {\n                        potentialMatch.finished = potentialMatch.matched === needleElements.length;\n                        if (potentialMatch.finished &&\n                            (!extend.allowAfter &&\n                                (hackstackElementIndex + 1 < hackstackSelector.elements.length || haystackSelectorIndex + 1 < haystackSelectorPath.length))) {\n                            potentialMatch = null;\n                        }\n                    }\n                    // if null we remove, if not, we are still valid, so either push as a valid match or continue\n                    if (potentialMatch) {\n                        if (potentialMatch.finished) {\n                            potentialMatch.length = needleElements.length;\n                            potentialMatch.endPathIndex = haystackSelectorIndex;\n                            potentialMatch.endPathElementIndex = hackstackElementIndex + 1; // index after end of match\n                            potentialMatches.length = 0; // we don't allow matches to overlap, so start matching again\n                            matches.push(potentialMatch);\n                        }\n                    } else {\n                        potentialMatches.splice(i, 1);\n                        i--;\n                    }\n                }\n            }\n        }\n        return matches;\n    }\n\n    isElementValuesEqual(elementValue1, elementValue2) {\n        if (typeof elementValue1 === 'string' || typeof elementValue2 === 'string') {\n            return elementValue1 === elementValue2;\n        }\n        if (elementValue1 instanceof tree.Attribute) {\n            if (elementValue1.op !== elementValue2.op || elementValue1.key !== elementValue2.key) {\n                return false;\n            }\n            if (!elementValue1.value || !elementValue2.value) {\n                if (elementValue1.value || elementValue2.value) {\n                    return false;\n                }\n                return true;\n            }\n            elementValue1 = elementValue1.value.value || elementValue1.value;\n            elementValue2 = elementValue2.value.value || elementValue2.value;\n            return elementValue1 === elementValue2;\n        }\n        elementValue1 = elementValue1.value;\n        elementValue2 = elementValue2.value;\n        if (elementValue1 instanceof tree.Selector) {\n            if (!(elementValue2 instanceof tree.Selector) || elementValue1.elements.length !== elementValue2.elements.length) {\n                return false;\n            }\n            for (let i = 0; i  < elementValue1.elements.length; i++) {\n                if (elementValue1.elements[i].combinator.value !== elementValue2.elements[i].combinator.value) {\n                    if (i !== 0 || (elementValue1.elements[i].combinator.value || ' ') !== (elementValue2.elements[i].combinator.value || ' ')) {\n                        return false;\n                    }\n                }\n                if (!this.isElementValuesEqual(elementValue1.elements[i].value, elementValue2.elements[i].value)) {\n                    return false;\n                }\n            }\n            return true;\n        }\n        return false;\n    }\n\n    extendSelector(matches, selectorPath, replacementSelector, isVisible) {\n\n        // for a set of matches, replace each match with the replacement selector\n\n        let currentSelectorPathIndex = 0, currentSelectorPathElementIndex = 0, path = [], matchIndex, selector, firstElement, match, newElements;\n\n        for (matchIndex = 0; matchIndex < matches.length; matchIndex++) {\n            match = matches[matchIndex];\n            selector = selectorPath[match.pathIndex];\n            firstElement = new tree.Element(\n                match.initialCombinator,\n                replacementSelector.elements[0].value,\n                replacementSelector.elements[0].isVariable,\n                replacementSelector.elements[0].getIndex(),\n                replacementSelector.elements[0].fileInfo()\n            );\n\n            if (match.pathIndex > currentSelectorPathIndex && currentSelectorPathElementIndex > 0) {\n                path[path.length - 1].elements = path[path.length - 1]\n                    .elements.concat(selectorPath[currentSelectorPathIndex].elements.slice(currentSelectorPathElementIndex));\n                currentSelectorPathElementIndex = 0;\n                currentSelectorPathIndex++;\n            }\n\n            newElements = selector.elements\n                .slice(currentSelectorPathElementIndex, match.index)\n                .concat([firstElement])\n                .concat(replacementSelector.elements.slice(1));\n\n            if (currentSelectorPathIndex === match.pathIndex && matchIndex > 0) {\n                path[path.length - 1].elements =\n                    path[path.length - 1].elements.concat(newElements);\n            } else {\n                path = path.concat(selectorPath.slice(currentSelectorPathIndex, match.pathIndex));\n\n                path.push(new tree.Selector(\n                    newElements\n                ));\n            }\n            currentSelectorPathIndex = match.endPathIndex;\n            currentSelectorPathElementIndex = match.endPathElementIndex;\n            if (currentSelectorPathElementIndex >= selectorPath[currentSelectorPathIndex].elements.length) {\n                currentSelectorPathElementIndex = 0;\n                currentSelectorPathIndex++;\n            }\n        }\n\n        if (currentSelectorPathIndex < selectorPath.length && currentSelectorPathElementIndex > 0) {\n            path[path.length - 1].elements = path[path.length - 1]\n                .elements.concat(selectorPath[currentSelectorPathIndex].elements.slice(currentSelectorPathElementIndex));\n            currentSelectorPathIndex++;\n        }\n\n        path = path.concat(selectorPath.slice(currentSelectorPathIndex, selectorPath.length));\n        path = path.map(function (currentValue) {\n            // we can re-use elements here, because the visibility property matters only for selectors\n            const derived = currentValue.createDerived(currentValue.elements);\n            if (isVisible) {\n                derived.ensureVisibility();\n            } else {\n                derived.ensureInvisibility();\n            }\n            return derived;\n        });\n        return path;\n    }\n\n    visitMedia(mediaNode, visitArgs) {\n        let newAllExtends = mediaNode.allExtends.concat(this.allExtendsStack[this.allExtendsStack.length - 1]);\n        newAllExtends = newAllExtends.concat(this.doExtendChaining(newAllExtends, mediaNode.allExtends));\n        this.allExtendsStack.push(newAllExtends);\n    }\n\n    visitMediaOut(mediaNode) {\n        const lastIndex = this.allExtendsStack.length - 1;\n        this.allExtendsStack.length = lastIndex;\n    }\n\n    visitAtRule(atRuleNode, visitArgs) {\n        let newAllExtends = atRuleNode.allExtends.concat(this.allExtendsStack[this.allExtendsStack.length - 1]);\n        newAllExtends = newAllExtends.concat(this.doExtendChaining(newAllExtends, atRuleNode.allExtends));\n        this.allExtendsStack.push(newAllExtends);\n    }\n\n    visitAtRuleOut(atRuleNode) {\n        const lastIndex = this.allExtendsStack.length - 1;\n        this.allExtendsStack.length = lastIndex;\n    }\n}\n\nexport default ProcessExtendsVisitor;\n"]}