{"version": 3, "file": "contexts.js", "sourceRoot": "", "sources": ["../../src/less/contexts.js"], "names": [], "mappings": ";;;AAAA,IAAM,QAAQ,GAAG,EAAE,CAAC;AACpB,kBAAe,QAAQ,CAAC;AACxB,6DAAyC;AAEzC,IAAM,gBAAgB,GAAG,SAAS,gBAAgB,CAAC,QAAQ,EAAE,WAAW,EAAE,gBAAgB;IACtF,IAAI,CAAC,QAAQ,EAAE;QAAE,OAAO;KAAE;IAE1B,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,gBAAgB,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;QAC9C,IAAI,MAAM,CAAC,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,QAAQ,EAAE,gBAAgB,CAAC,CAAC,CAAC,CAAC,EAAE;YACrE,WAAW,CAAC,gBAAgB,CAAC,CAAC,CAAC,CAAC,GAAG,QAAQ,CAAC,gBAAgB,CAAC,CAAC,CAAC,CAAC,CAAC;SACpE;KACJ;AACL,CAAC,CAAC;AAEF;;GAEG;AACH,IAAM,mBAAmB,GAAG;IACxB,UAAU;IACV,OAAO;IACP,aAAa;IACb,UAAU;IACV,eAAe;IACf,UAAU;IACV,iBAAiB;IACjB,UAAU;IACV,YAAY;IACZ,YAAY;IACZ,MAAM;IACN,cAAc;IACd,UAAU;IACV,gBAAgB;IAChB,6EAA6E;IAC7E,eAAe;IACf,OAAO,EAAa,mCAAmC;CAC1D,CAAC;AAEF,QAAQ,CAAC,KAAK,GAAG,UAAS,OAAO;IAC7B,gBAAgB,CAAC,OAAO,EAAE,IAAI,EAAE,mBAAmB,CAAC,CAAC;IAErD,IAAI,OAAO,IAAI,CAAC,KAAK,KAAK,QAAQ,EAAE;QAAE,IAAI,CAAC,KAAK,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;KAAE;AACtE,CAAC,CAAC;AAEF,IAAM,kBAAkB,GAAG;IACvB,OAAO;IACP,UAAU;IACV,MAAM;IACN,aAAa;IACb,WAAW;IACX,gBAAgB;IAChB,SAAS;IACT,mBAAmB;IACnB,eAAe;IACf,gBAAgB;IAChB,aAAa,CAAQ,kDAAkD;CAC1E,CAAC;AAEF,QAAQ,CAAC,IAAI,GAAG,UAAS,OAAO,EAAE,MAAM;IACpC,gBAAgB,CAAC,OAAO,EAAE,IAAI,EAAE,kBAAkB,CAAC,CAAC;IAEpD,IAAI,OAAO,IAAI,CAAC,KAAK,KAAK,QAAQ,EAAE;QAAE,IAAI,CAAC,KAAK,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;KAAE;IAElE,IAAI,CAAC,MAAM,GAAG,MAAM,IAAI,EAAE,CAAC;IAC3B,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,cAAc,IAAI,EAAE,CAAC;AACpD,CAAC,CAAC;AAEF,QAAQ,CAAC,IAAI,CAAC,SAAS,CAAC,SAAS,GAAG;IAChC,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE;QACjB,IAAI,CAAC,SAAS,GAAG,EAAE,CAAC;KACvB;IACD,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;IAC1B,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC;AACvB,CAAC,CAAC;AAEF,QAAQ,CAAC,IAAI,CAAC,SAAS,CAAC,QAAQ,GAAG;IAC/B,IAAI,CAAC,SAAS,CAAC,GAAG,EAAE,CAAC;IACrB,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE;QACxB,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC;KACvB;AACL,CAAC,CAAC;AAEF,QAAQ,CAAC,IAAI,CAAC,SAAS,CAAC,aAAa,GAAG;IACpC,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE;QACnB,IAAI,CAAC,WAAW,GAAG,EAAE,CAAC;KACzB;IACD,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;AAChC,CAAC,CAAC;AAEF,QAAQ,CAAC,IAAI,CAAC,SAAS,CAAC,gBAAgB,GAAG;IACvC,IAAI,CAAC,WAAW,CAAC,GAAG,EAAE,CAAC;AAC3B,CAAC,CAAC;AAEF,QAAQ,CAAC,IAAI,CAAC,SAAS,CAAC,MAAM,GAAG,KAAK,CAAC;AACvC,QAAQ,CAAC,IAAI,CAAC,SAAS,CAAC,MAAM,GAAG,IAAI,CAAC;AACtC,QAAQ,CAAC,IAAI,CAAC,SAAS,CAAC,QAAQ,GAAG,UAAU,EAAE;IAC3C,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE;QACd,OAAO,KAAK,CAAC;KAChB;IACD,IAAI,EAAE,KAAK,GAAG,IAAI,IAAI,CAAC,IAAI,KAAK,SAAS,CAAC,IAAI,CAAC,MAAM,IAAI,CAAC,CAAC,IAAI,CAAC,WAAW,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC,EAAE;QACtG,OAAO,KAAK,CAAC;KAChB;IACD,IAAI,IAAI,CAAC,IAAI,GAAG,SAAS,CAAC,IAAI,CAAC,eAAe,EAAE;QAC5C,OAAO,IAAI,CAAC,WAAW,IAAI,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC;KACtD;IACD,OAAO,IAAI,CAAC;AAChB,CAAC,CAAC;AAEF,QAAQ,CAAC,IAAI,CAAC,SAAS,CAAC,mBAAmB,GAAG,UAAU,IAAI;IACxD,IAAM,UAAU,GAAG,IAAI,CAAC,WAAW,KAAK,SAAS,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC,CAAC,mBAAmB,CAAC,CAAC,CAAC,cAAc,CAAC;IAE3G,OAAO,UAAU,CAAC,IAAI,CAAC,CAAC;AAC5B,CAAC,CAAC;AAEF,QAAQ,CAAC,IAAI,CAAC,SAAS,CAAC,WAAW,GAAG,UAAU,IAAI,EAAE,QAAQ;IAC1D,IAAI,OAAO,CAAC;IAEZ,QAAQ,GAAG,QAAQ,IAAI,EAAE,CAAC;IAC1B,OAAO,GAAG,IAAI,CAAC,aAAa,CAAC,QAAQ,GAAG,IAAI,CAAC,CAAC;IAE9C,4EAA4E;IAC5E,8DAA8D;IAC9D,IAAI,mBAAmB,CAAC,IAAI,CAAC;QACzB,cAAc,CAAC,QAAQ,CAAC;QACxB,mBAAmB,CAAC,OAAO,CAAC,KAAK,KAAK,EAAE;QACxC,OAAO,GAAG,YAAK,OAAO,CAAE,CAAC;KAC5B;IAED,OAAO,OAAO,CAAC;AACnB,CAAC,CAAC;AAEF,QAAQ,CAAC,IAAI,CAAC,SAAS,CAAC,aAAa,GAAG,UAAU,IAAI;IAClD,IAAM,QAAQ,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,OAAO,EAAE,CAAC;IAC3C,IAAI,OAAO,CAAC;IAEZ,IAAI,GAAG,EAAE,CAAC;IACV,OAAO,QAAQ,CAAC,MAAM,KAAK,CAAC,EAAE;QAC1B,OAAO,GAAG,QAAQ,CAAC,GAAG,EAAE,CAAC;QACzB,QAAS,OAAO,EAAG;YACf,KAAK,GAAG;gBACJ,MAAM;YACV,KAAK,IAAI;gBACL,IAAI,CAAC,IAAI,CAAC,MAAM,KAAK,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC,KAAK,IAAI,CAAC,EAAE;oBACzD,IAAI,CAAC,IAAI,CAAE,OAAO,CAAE,CAAC;iBACxB;qBAAM;oBACH,IAAI,CAAC,GAAG,EAAE,CAAC;iBACd;gBACD,MAAM;YACV;gBACI,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;gBACnB,MAAM;SACb;KACJ;IAED,OAAO,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;AAC1B,CAAC,CAAC;AAEF,SAAS,cAAc,CAAC,IAAI;IACxB,OAAO,CAAC,qBAAqB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;AAC7C,CAAC;AAED,SAAS,mBAAmB,CAAC,IAAI;IAC7B,OAAO,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,KAAK,GAAG,CAAC;AAClC,CAAC;AAED,qCAAqC", "sourcesContent": ["const contexts = {};\nexport default contexts;\nimport * as Constants from './constants';\n\nconst copyFromOriginal = function copyFromOriginal(original, destination, propertiesToCopy) {\n    if (!original) { return; }\n\n    for (let i = 0; i < propertiesToCopy.length; i++) {\n        if (Object.prototype.hasOwnProperty.call(original, propertiesToCopy[i])) {\n            destination[propertiesToCopy[i]] = original[propertiesToCopy[i]];\n        }\n    }\n};\n\n/*\n parse is used whilst parsing\n */\nconst parseCopyProperties = [\n    // options\n    'paths',            // option - unmodified - paths to search for imports on\n    'rewriteUrls',      // option - whether to adjust URL's to be relative\n    'rootpath',         // option - rootpath to append to URL's\n    'strictImports',    // option -\n    'insecure',         // option - whether to allow imports from insecure ssl hosts\n    'dumpLineNumbers',  // option - whether to dump line numbers\n    'compress',         // option - whether to compress\n    'syncImport',       // option - whether to import synchronously\n    'chunkInput',       // option - whether to chunk input. more performant but causes parse issues.\n    'mime',             // browser only - mime type for sheet import\n    'useFileCache',     // browser only - whether to use the per file session cache\n    // context\n    'processImports',   // option & context - whether to process imports. if false then imports will not be imported.\n    // Used by the import manager to stop multiple import visitors being created.\n    'pluginManager',    // Used as the plugin manager for the session\n    'quiet',            // option - whether to log warnings\n];\n\ncontexts.Parse = function(options) {\n    copyFromOriginal(options, this, parseCopyProperties);\n\n    if (typeof this.paths === 'string') { this.paths = [this.paths]; }\n};\n\nconst evalCopyProperties = [\n    'paths',             // additional include paths\n    'compress',          // whether to compress\n    'math',              // whether math has to be within parenthesis\n    'strictUnits',       // whether units need to evaluate correctly\n    'sourceMap',         // whether to output a source map\n    'importMultiple',    // whether we are currently importing multiple copies\n    'urlArgs',           // whether to add args into url tokens\n    'javascriptEnabled', // option - whether Inline JavaScript is enabled. if undefined, defaults to false\n    'pluginManager',     // Used as the plugin manager for the session\n    'importantScope',    // used to bubble up !important statements\n    'rewriteUrls'        // option - whether to adjust URL's to be relative\n];\n\ncontexts.Eval = function(options, frames) {\n    copyFromOriginal(options, this, evalCopyProperties);\n\n    if (typeof this.paths === 'string') { this.paths = [this.paths]; }\n\n    this.frames = frames || [];\n    this.importantScope = this.importantScope || [];\n};\n\ncontexts.Eval.prototype.enterCalc = function () {\n    if (!this.calcStack) {\n        this.calcStack = [];\n    }\n    this.calcStack.push(true);\n    this.inCalc = true;\n};\n\ncontexts.Eval.prototype.exitCalc = function () {\n    this.calcStack.pop();\n    if (!this.calcStack.length) {\n        this.inCalc = false;\n    }\n};\n\ncontexts.Eval.prototype.inParenthesis = function () {\n    if (!this.parensStack) {\n        this.parensStack = [];\n    }\n    this.parensStack.push(true);\n};\n\ncontexts.Eval.prototype.outOfParenthesis = function () {\n    this.parensStack.pop();\n};\n\ncontexts.Eval.prototype.inCalc = false;\ncontexts.Eval.prototype.mathOn = true;\ncontexts.Eval.prototype.isMathOn = function (op) {\n    if (!this.mathOn) {\n        return false;\n    }\n    if (op === '/' && this.math !== Constants.Math.ALWAYS && (!this.parensStack || !this.parensStack.length)) {\n        return false;\n    }\n    if (this.math > Constants.Math.PARENS_DIVISION) {\n        return this.parensStack && this.parensStack.length;\n    }\n    return true;\n};\n\ncontexts.Eval.prototype.pathRequiresRewrite = function (path) {\n    const isRelative = this.rewriteUrls === Constants.RewriteUrls.LOCAL ? isPathLocalRelative : isPathRelative;\n\n    return isRelative(path);\n};\n\ncontexts.Eval.prototype.rewritePath = function (path, rootpath) {\n    let newPath;\n\n    rootpath = rootpath || '';\n    newPath = this.normalizePath(rootpath + path);\n\n    // If a path was explicit relative and the rootpath was not an absolute path\n    // we must ensure that the new path is also explicit relative.\n    if (isPathLocalRelative(path) &&\n        isPathRelative(rootpath) &&\n        isPathLocalRelative(newPath) === false) {\n        newPath = `./${newPath}`;\n    }\n\n    return newPath;\n};\n\ncontexts.Eval.prototype.normalizePath = function (path) {\n    const segments = path.split('/').reverse();\n    let segment;\n\n    path = [];\n    while (segments.length !== 0) {\n        segment = segments.pop();\n        switch ( segment ) {\n            case '.':\n                break;\n            case '..':\n                if ((path.length === 0) || (path[path.length - 1] === '..')) {\n                    path.push( segment );\n                } else {\n                    path.pop();\n                }\n                break;\n            default:\n                path.push(segment);\n                break;\n        }\n    }\n\n    return path.join('/');\n};\n\nfunction isPathRelative(path) {\n    return !/^(?:[a-z-]+:|\\/|#)/i.test(path);\n}\n\nfunction isPathLocalRelative(path) {\n    return path.charAt(0) === '.';\n}\n\n// todo - do the same for the toCSS ?\n"]}