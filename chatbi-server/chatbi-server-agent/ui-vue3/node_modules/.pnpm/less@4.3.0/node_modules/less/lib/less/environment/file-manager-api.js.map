{"version": 3, "file": "file-manager-api.js", "sourceRoot": "", "sources": ["../../../src/less/environment/file-manager-api.ts"], "names": [], "mappings": "", "sourcesContent": ["import type { Environment } from './environment-api'\n\nexport interface FileManager {\n    /**\n     * Given the full path to a file, return the path component\n     * Provided by AbstractFileManager\n     */\n    getPath(filename: string): string\n    /**\n     * Append a .less extension if appropriate. Only called if less thinks one could be added.\n     * Provided by AbstractFileManager\n     */\n    tryAppendLessExtension(filename: string): string\n    /**\n     * Whether the rootpath should be converted to be absolute.\n     * The browser ovverides this to return true because urls must be absolute.\n     * Provided by AbstractFileManager (returns false)\n     */\n    alwaysMakePathsAbsolute(): boolean\n    /**\n     * Returns whether a path is absolute\n     * Provided by AbstractFileManager\n     */\n    isPathAbsolute(path: string): boolean\n    /**\n     * joins together 2 paths\n     * Provided by AbstractFileManager\n     */\n    join(basePath: string, laterPath: string): string\n    /**\n     * Returns the difference between 2 paths\n     * E.g. url = a/ baseUrl = a/b/ returns ../\n     * url = a/b/ baseUrl = a/ returns b/\n     * Provided by AbstractFileManager\n     */\n    pathDiff(url: string, baseUrl: string): string\n    /**\n     * Returns whether this file manager supports this file for syncronous file retrieval\n     * If true is returned, loadFileSync will then be called with the file.\n     * Provided by AbstractFileManager (returns false)\n     * \n     * @todo - Narrow Options type\n     */\n    supportsSync(\n        filename: string,\n        currentDirectory: string,\n        options: Record<string, any>,\n        environment: Environment\n    ): boolean\n    /**\n     * If file manager supports async file retrieval for this file type\n     */\n    supports(\n        filename: string,\n        currentDirectory: string,\n        options: Record<string, any>,\n        environment: Environment\n    ): boolean\n    /**\n     * Loads a file asynchronously.\n     */\n    loadFile(\n        filename: string,\n        currentDirectory: string,\n        options: Record<string, any>,\n        environment: Environment\n    ): Promise<{ filename: string, contents: string }>\n    /**\n     * Loads a file synchronously. Expects an immediate return with an object\n     */\n    loadFileSync(\n        filename: string,\n        currentDirectory: string,\n        options: Record<string, any>,\n        environment: Environment\n    ): { error?: unknown, filename: string, contents: string }\n}\n"]}