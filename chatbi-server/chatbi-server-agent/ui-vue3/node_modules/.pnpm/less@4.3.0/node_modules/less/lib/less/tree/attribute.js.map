{"version": 3, "file": "attribute.js", "sourceRoot": "", "sources": ["../../../src/less/tree/attribute.js"], "names": [], "mappings": ";;;AAAA,wDAA0B;AAE1B,IAAM,SAAS,GAAG,UAAS,GAAG,EAAE,EAAE,EAAE,KAAK,EAAE,GAAG;IAC1C,IAAI,CAAC,GAAG,GAAG,GAAG,CAAC;IACf,IAAI,CAAC,EAAE,GAAG,EAAE,CAAC;IACb,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;IACnB,IAAI,CAAC,GAAG,GAAG,GAAG,CAAC;AACnB,CAAC,CAAA;AAED,SAAS,CAAC,SAAS,GAAG,MAAM,CAAC,MAAM,CAAC,IAAI,cAAI,EAAE,EAAE;IAC5C,IAAI,EAAE,WAAW;IAEjB,IAAI,YAAC,OAAO;QACR,OAAO,IAAI,SAAS,CAChB,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,EACjD,IAAI,CAAC,EAAE,EACP,CAAC,IAAI,CAAC,KAAK,IAAI,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,EACvE,IAAI,CAAC,GAAG,CACX,CAAC;IACN,CAAC;IAED,MAAM,YAAC,OAAO,EAAE,MAAM;QAClB,MAAM,CAAC,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC;IACpC,CAAC;IAED,KAAK,YAAC,OAAO;QACT,IAAI,KAAK,GAAG,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC;QAEhE,IAAI,IAAI,CAAC,EAAE,EAAE;YACT,KAAK,IAAI,IAAI,CAAC,EAAE,CAAC;YACjB,KAAK,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;SACxE;QAED,IAAI,IAAI,CAAC,GAAG,EAAE;YACV,KAAK,GAAG,KAAK,GAAG,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC;SAClC;QAED,OAAO,WAAI,KAAK,MAAG,CAAC;IACxB,CAAC;CACJ,CAAC,CAAC;AAEH,kBAAe,SAAS,CAAC", "sourcesContent": ["import Node from './node';\n\nconst Attribute = function(key, op, value, cif) {\n    this.key = key;\n    this.op = op;\n    this.value = value;\n    this.cif = cif;\n}\n\nAttribute.prototype = Object.assign(new Node(), {\n    type: 'Attribute',\n\n    eval(context) {\n        return new Attribute(\n            this.key.eval ? this.key.eval(context) : this.key,\n            this.op,\n            (this.value && this.value.eval) ? this.value.eval(context) : this.value,\n            this.cif\n        );\n    },\n\n    genCSS(context, output) {\n        output.add(this.toCSS(context));\n    },\n\n    toCSS(context) {\n        let value = this.key.toCSS ? this.key.toCSS(context) : this.key;\n\n        if (this.op) {\n            value += this.op;\n            value += (this.value.toCSS ? this.value.toCSS(context) : this.value);\n        }\n\n        if (this.cif) {\n            value = value + ' ' + this.cif;\n        }\n\n        return `[${value}]`;\n    }\n});\n\nexport default Attribute;\n"]}