{"version": 3, "file": "types.js", "sourceRoot": "", "sources": ["../../../src/less/functions/types.js"], "names": [], "mappings": ";;;AAAA,oEAAsC;AACtC,sFAAuD;AACvD,wEAA0C;AAC1C,gEAAkC;AAClC,kEAAoC;AACpC,wEAA0C;AAC1C,4DAA8B;AAC9B,wEAA0C;AAE1C,IAAM,GAAG,GAAG,UAAC,CAAC,EAAE,IAAI,IAAK,OAAA,CAAC,CAAC,YAAY,IAAI,CAAC,CAAC,CAAC,CAAC,iBAAO,CAAC,IAAI,CAAC,CAAC,CAAC,iBAAO,CAAC,KAAK,EAAlD,CAAkD,CAAC;AAC5E,IAAM,MAAM,GAAG,UAAC,CAAC,EAAE,IAAI;IACnB,IAAI,IAAI,KAAK,SAAS,EAAE;QACpB,MAAM,EAAE,IAAI,EAAE,UAAU,EAAE,OAAO,EAAE,iDAAiD,EAAE,CAAC;KAC1F;IACD,IAAI,GAAG,OAAO,IAAI,CAAC,KAAK,KAAK,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC;IAC1D,IAAI,OAAO,IAAI,KAAK,QAAQ,EAAE;QAC1B,MAAM,EAAE,IAAI,EAAE,UAAU,EAAE,OAAO,EAAE,yDAAyD,EAAE,CAAC;KAClG;IACD,OAAO,CAAC,CAAC,YAAY,mBAAS,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,iBAAO,CAAC,IAAI,CAAC,CAAC,CAAC,iBAAO,CAAC,KAAK,CAAC;AACtF,CAAC,CAAC;AAEF,kBAAe;IACX,SAAS,EAAE,UAAU,CAAC;QAClB,OAAO,GAAG,CAAC,CAAC,EAAE,0BAAe,CAAC,CAAC;IACnC,CAAC;IACD,OAAO,EAAE,UAAU,CAAC;QAChB,OAAO,GAAG,CAAC,CAAC,EAAE,eAAK,CAAC,CAAC;IACzB,CAAC;IACD,QAAQ,EAAE,UAAU,CAAC;QACjB,OAAO,GAAG,CAAC,CAAC,EAAE,mBAAS,CAAC,CAAC;IAC7B,CAAC;IACD,QAAQ,EAAE,UAAU,CAAC;QACjB,OAAO,GAAG,CAAC,CAAC,EAAE,gBAAM,CAAC,CAAC;IAC1B,CAAC;IACD,SAAS,EAAE,UAAU,CAAC;QAClB,OAAO,GAAG,CAAC,CAAC,EAAE,iBAAO,CAAC,CAAC;IAC3B,CAAC;IACD,KAAK,EAAE,UAAU,CAAC;QACd,OAAO,GAAG,CAAC,CAAC,EAAE,aAAG,CAAC,CAAC;IACvB,CAAC;IACD,OAAO,EAAE,UAAU,CAAC;QAChB,OAAO,MAAM,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC;IAC3B,CAAC;IACD,YAAY,EAAE,UAAU,CAAC;QACrB,OAAO,MAAM,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC;IAC1B,CAAC;IACD,IAAI,EAAE,UAAU,CAAC;QACb,OAAO,MAAM,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC;IAC3B,CAAC;IACD,MAAM,QAAA;IACN,IAAI,EAAE,UAAU,GAAG,EAAE,IAAI;QACrB,IAAI,CAAC,CAAC,GAAG,YAAY,mBAAS,CAAC,EAAE;YAC7B,MAAM,EAAE,IAAI,EAAE,UAAU;gBACpB,OAAO,EAAE,qDAA8C,GAAG,YAAY,mBAAS,CAAC,CAAC,CAAC,mCAAmC,CAAC,CAAC,CAAC,EAAE,CAAE,EAAE,CAAC;SACtI;QACD,IAAI,IAAI,EAAE;YACN,IAAI,IAAI,YAAY,iBAAO,EAAE;gBACzB,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC;aACrB;iBAAM;gBACH,IAAI,GAAG,IAAI,CAAC,KAAK,EAAE,CAAC;aACvB;SACJ;aAAM;YACH,IAAI,GAAG,EAAE,CAAC;SACb;QACD,OAAO,IAAI,mBAAS,CAAC,GAAG,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC;IAC1C,CAAC;IACD,UAAU,EAAE,UAAU,CAAC;QACnB,OAAO,IAAI,mBAAS,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC;IACjC,CAAC;CACJ,CAAC", "sourcesContent": ["import Keyword from '../tree/keyword';\nimport DetachedRuleset from '../tree/detached-ruleset';\nimport Dimension from '../tree/dimension';\nimport Color from '../tree/color';\nimport Quoted from '../tree/quoted';\nimport Anonymous from '../tree/anonymous';\nimport URL from '../tree/url';\nimport Operation from '../tree/operation';\n\nconst isa = (n, Type) => (n instanceof Type) ? Keyword.True : Keyword.False;\nconst isunit = (n, unit) => {\n    if (unit === undefined) {\n        throw { type: 'Argument', message: 'missing the required second argument to isunit.' };\n    }\n    unit = typeof unit.value === 'string' ? unit.value : unit;\n    if (typeof unit !== 'string') {\n        throw { type: 'Argument', message: 'Second argument to isunit should be a unit or a string.' };\n    }\n    return (n instanceof Dimension) && n.unit.is(unit) ? Keyword.True : Keyword.False;\n};\n\nexport default {\n    isruleset: function (n) {\n        return isa(n, DetachedRuleset);\n    },\n    iscolor: function (n) {\n        return isa(n, Color);\n    },\n    isnumber: function (n) {\n        return isa(n, Dimension);\n    },\n    isstring: function (n) {\n        return isa(n, Quoted);\n    },\n    iskeyword: function (n) {\n        return isa(n, Keyword);\n    },\n    isurl: function (n) {\n        return isa(n, URL);\n    },\n    ispixel: function (n) {\n        return isunit(n, 'px');\n    },\n    ispercentage: function (n) {\n        return isunit(n, '%');\n    },\n    isem: function (n) {\n        return isunit(n, 'em');\n    },\n    isunit,\n    unit: function (val, unit) {\n        if (!(val instanceof Dimension)) {\n            throw { type: 'Argument',\n                message: `the first argument to unit must be a number${val instanceof Operation ? '. Have you forgotten parenthesis?' : ''}` };\n        }\n        if (unit) {\n            if (unit instanceof Keyword) {\n                unit = unit.value;\n            } else {\n                unit = unit.toCSS();\n            }\n        } else {\n            unit = '';\n        }\n        return new Dimension(val.value, unit);\n    },\n    'get-unit': function (n) {\n        return new Anonymous(n.unit);\n    }\n};\n"]}