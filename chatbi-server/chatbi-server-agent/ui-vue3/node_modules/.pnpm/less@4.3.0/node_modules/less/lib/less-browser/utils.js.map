{"version": 3, "file": "utils.js", "sourceRoot": "", "sources": ["../../src/less-browser/utils.js"], "names": [], "mappings": ";;;AACA,SAAgB,SAAS,CAAC,IAAI;IAC1B,OAAO,IAAI,CAAC,OAAO,CAAC,oBAAoB,EAAE,EAAE,CAAC,CAAE,2BAA2B;SACrE,OAAO,CAAC,oBAAoB,EAAE,EAAE,CAAC,CAAQ,gCAAgC;SACzE,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC,CAAyB,gBAAgB;SAC3D,OAAO,CAAC,cAAc,EAAE,EAAE,CAAC,CAAgB,0BAA0B;SACrE,OAAO,CAAC,WAAW,EAAE,GAAG,CAAC,CAAiB,6BAA6B;SACvE,OAAO,CAAC,KAAK,EAAE,GAAG,CAAC,CAAC,CAAuB,yCAAyC;AAC7F,CAAC;AAPD,8BAOC;AAED,SAAgB,WAAW,CAAC,OAAO,EAAE,GAAG;IACpC,IAAI,CAAC,GAAG,EAAE;QAAC,OAAO;KAAC,CAAC,sCAAsC;IAC1D,KAAK,IAAM,GAAG,IAAI,GAAG,CAAC,OAAO,EAAE;QAC3B,IAAI,MAAM,CAAC,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,GAAG,CAAC,OAAO,EAAE,GAAG,CAAC,EAAE;YACxD,IAAI,GAAG,KAAK,KAAK,IAAI,GAAG,KAAK,iBAAiB,IAAI,GAAG,KAAK,UAAU,IAAI,GAAG,KAAK,gBAAgB,EAAE;gBAC9F,OAAO,CAAC,GAAG,CAAC,GAAG,GAAG,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC;aACnC;iBAAM;gBACH,IAAI;oBACA,OAAO,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,CAAC;iBAC/C;gBACD,OAAO,CAAC,EAAE,GAAE;aACf;SACJ;KACJ;AACL,CAAC;AAdD,kCAcC", "sourcesContent": ["\nexport function extractId(href) {\n    return href.replace(/^[a-z-]+:\\/+?[^/]+/, '')  // Remove protocol & domain\n        .replace(/[?&]livereload=\\w+/, '')        // Remove LiveReload cachebuster\n        .replace(/^\\//, '')                         // Remove root /\n        .replace(/\\.[a-zA-Z]+$/, '')                // Remove simple extension\n        .replace(/[^.\\w-]+/g, '-')                 // Replace illegal characters\n        .replace(/\\./g, ':');                       // Replace dots with colons(for valid id)\n}\n\nexport function addDataAttr(options, tag) {\n    if (!tag) {return;} // in case of tag is null or undefined\n    for (const opt in tag.dataset) {\n        if (Object.prototype.hasOwnProperty.call(tag.dataset, opt)) {\n            if (opt === 'env' || opt === 'dumpLineNumbers' || opt === 'rootpath' || opt === 'errorReporting') {\n                options[opt] = tag.dataset[opt];\n            } else {\n                try {\n                    options[opt] = JSON.parse(tag.dataset[opt]);\n                }\n                catch (_) {}\n            }\n        }\n    }\n}\n"]}