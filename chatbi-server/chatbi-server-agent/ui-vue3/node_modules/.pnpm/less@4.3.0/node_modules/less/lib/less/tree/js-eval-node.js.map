{"version": 3, "file": "js-eval-node.js", "sourceRoot": "", "sources": ["../../../src/less/tree/js-eval-node.js"], "names": [], "mappings": ";;;AAAA,wDAA0B;AAC1B,gEAAkC;AAElC,IAAM,UAAU,GAAG,cAAY,CAAC,CAAC;AAEjC,UAAU,CAAC,SAAS,GAAG,MAAM,CAAC,MAAM,CAAC,IAAI,cAAI,EAAE,EAAE;IAC7C,kBAAkB,YAAC,UAAU,EAAE,OAAO;QAClC,IAAI,MAAM,CAAC;QACX,IAAM,IAAI,GAAG,IAAI,CAAC;QAClB,IAAM,WAAW,GAAG,EAAE,CAAC;QAEvB,IAAI,CAAC,OAAO,CAAC,iBAAiB,EAAE;YAC5B,MAAM,EAAE,OAAO,EAAE,8DAA8D;gBAC3E,QAAQ,EAAE,IAAI,CAAC,QAAQ,EAAE,CAAC,QAAQ;gBAClC,KAAK,EAAE,IAAI,CAAC,QAAQ,EAAE,EAAE,CAAC;SAChC;QAED,UAAU,GAAG,UAAU,CAAC,OAAO,CAAC,gBAAgB,EAAE,UAAU,CAAC,EAAE,IAAI;YAC/D,OAAO,IAAI,CAAC,KAAK,CAAC,IAAI,kBAAQ,CAAC,WAAI,IAAI,CAAE,EAAE,IAAI,CAAC,QAAQ,EAAE,EAAE,IAAI,CAAC,QAAQ,EAAE,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC;QAChG,CAAC,CAAC,CAAC;QAEH,IAAI;YACA,UAAU,GAAG,IAAI,QAAQ,CAAC,kBAAW,UAAU,MAAG,CAAC,CAAC;SACvD;QAAC,OAAO,CAAC,EAAE;YACR,MAAM,EAAE,OAAO,EAAE,uCAAgC,CAAC,CAAC,OAAO,oBAAW,UAAU,MAAI;gBAC/E,QAAQ,EAAE,IAAI,CAAC,QAAQ,EAAE,CAAC,QAAQ;gBAClC,KAAK,EAAE,IAAI,CAAC,QAAQ,EAAE,EAAE,CAAC;SAChC;QAED,IAAM,SAAS,GAAG,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,SAAS,EAAE,CAAC;QAChD,KAAK,IAAM,CAAC,IAAI,SAAS,EAAE;YACvB,iDAAiD;YACjD,IAAI,SAAS,CAAC,cAAc,CAAC,CAAC,CAAC,EAAE;gBAC7B,WAAW,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,GAAG;oBACtB,KAAK,EAAE,SAAS,CAAC,CAAC,CAAC,CAAC,KAAK;oBACzB,IAAI,EAAE;wBACF,OAAO,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,KAAK,EAAE,CAAC;oBAC5C,CAAC;iBACJ,CAAC;aACL;SACJ;QAED,IAAI;YACA,MAAM,GAAG,UAAU,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;SACzC;QAAC,OAAO,CAAC,EAAE;YACR,MAAM,EAAE,OAAO,EAAE,wCAAiC,CAAC,CAAC,IAAI,eAAK,CAAC,CAAC,OAAO,CAAC,OAAO,CAAC,MAAM,EAAE,IAAI,CAAC,MAAG;gBAC3F,QAAQ,EAAE,IAAI,CAAC,QAAQ,EAAE,CAAC,QAAQ;gBAClC,KAAK,EAAE,IAAI,CAAC,QAAQ,EAAE,EAAE,CAAC;SAChC;QACD,OAAO,MAAM,CAAC;IAClB,CAAC;IAED,KAAK,YAAC,GAAG;QACL,IAAI,KAAK,CAAC,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC,EAAE;YACpD,OAAO,WAAI,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,UAAU,CAAC,IAAI,OAAO,CAAC,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,MAAG,CAAC;SAC9E;aAAM;YACH,OAAO,GAAG,CAAC,KAAK,EAAE,CAAC;SACtB;IACL,CAAC;CACJ,CAAC,CAAC;AAEH,kBAAe,UAAU,CAAC", "sourcesContent": ["import Node from './node';\nimport Variable from './variable';\n\nconst JsEvalNode = function() {};\n\nJsEvalNode.prototype = Object.assign(new Node(), {\n    evaluateJavaScript(expression, context) {\n        let result;\n        const that = this;\n        const evalContext = {};\n\n        if (!context.javascriptEnabled) {\n            throw { message: 'Inline JavaScript is not enabled. Is it set in your options?',\n                filename: this.fileInfo().filename,\n                index: this.getIndex() };\n        }\n\n        expression = expression.replace(/@\\{([\\w-]+)\\}/g, function (_, name) {\n            return that.jsify(new Variable(`@${name}`, that.getIndex(), that.fileInfo()).eval(context));\n        });\n\n        try {\n            expression = new Function(`return (${expression})`);\n        } catch (e) {\n            throw { message: `JavaScript evaluation error: ${e.message} from \\`${expression}\\`` ,\n                filename: this.fileInfo().filename,\n                index: this.getIndex() };\n        }\n\n        const variables = context.frames[0].variables();\n        for (const k in variables) {\n            // eslint-disable-next-line no-prototype-builtins\n            if (variables.hasOwnProperty(k)) {\n                evalContext[k.slice(1)] = {\n                    value: variables[k].value,\n                    toJS: function () {\n                        return this.value.eval(context).toCSS();\n                    }\n                };\n            }\n        }\n\n        try {\n            result = expression.call(evalContext);\n        } catch (e) {\n            throw { message: `JavaScript evaluation error: '${e.name}: ${e.message.replace(/[\"]/g, '\\'')}'` ,\n                filename: this.fileInfo().filename,\n                index: this.getIndex() };\n        }\n        return result;\n    },\n\n    jsify(obj) {\n        if (Array.isArray(obj.value) && (obj.value.length > 1)) {\n            return `[${obj.value.map(function (v) { return v.toCSS(); }).join(', ')}]`;\n        } else {\n            return obj.toCSS();\n        }\n    }\n});\n\nexport default JsEvalNode;\n"]}