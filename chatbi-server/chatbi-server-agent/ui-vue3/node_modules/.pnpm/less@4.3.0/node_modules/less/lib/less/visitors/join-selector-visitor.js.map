{"version": 3, "file": "join-selector-visitor.js", "sourceRoot": "", "sources": ["../../../src/less/visitors/join-selector-visitor.js"], "names": [], "mappings": ";;;AAAA,mCAAmC;AACnC;;GAEG;AACH,8DAAgC;AAEhC;IACI;QACI,IAAI,CAAC,QAAQ,GAAG,CAAC,EAAE,CAAC,CAAC;QACrB,IAAI,CAAC,QAAQ,GAAG,IAAI,iBAAO,CAAC,IAAI,CAAC,CAAC;IACtC,CAAC;IAED,iCAAG,GAAH,UAAI,IAAI;QACJ,OAAO,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;IACrC,CAAC;IAED,8CAAgB,GAAhB,UAAiB,QAAQ,EAAE,SAAS;QAChC,SAAS,CAAC,WAAW,GAAG,KAAK,CAAC;IAClC,CAAC;IAED,kDAAoB,GAApB,UAAqB,mBAAmB,EAAE,SAAS;QAC/C,SAAS,CAAC,WAAW,GAAG,KAAK,CAAC;IAClC,CAAC;IAED,0CAAY,GAAZ,UAAa,WAAW,EAAE,SAAS;QAC/B,IAAM,OAAO,GAAG,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,QAAQ,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;QACxD,IAAM,KAAK,GAAG,EAAE,CAAC;QACjB,IAAI,SAAS,CAAC;QAEd,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAE1B,IAAI,CAAC,WAAW,CAAC,IAAI,EAAE;YACnB,SAAS,GAAG,WAAW,CAAC,SAAS,CAAC;YAClC,IAAI,SAAS,EAAE;gBACX,SAAS,GAAG,SAAS,CAAC,MAAM,CAAC,UAAS,QAAQ,IAAI,OAAO,QAAQ,CAAC,WAAW,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;gBACpF,WAAW,CAAC,SAAS,GAAG,SAAS,CAAC,MAAM,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,SAAS,GAAG,IAAI,CAAC,CAAC;gBAC1E,IAAI,SAAS,EAAE;oBAAE,WAAW,CAAC,aAAa,CAAC,KAAK,EAAE,OAAO,EAAE,SAAS,CAAC,CAAC;iBAAE;aAC3E;YACD,IAAI,CAAC,SAAS,EAAE;gBAAE,WAAW,CAAC,KAAK,GAAG,IAAI,CAAC;aAAE;YAC7C,WAAW,CAAC,KAAK,GAAG,KAAK,CAAC;SAC7B;IACL,CAAC;IAED,6CAAe,GAAf,UAAgB,WAAW;QACvB,IAAI,CAAC,QAAQ,CAAC,MAAM,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,GAAG,CAAC,CAAC;IACpD,CAAC;IAED,wCAAU,GAAV,UAAW,SAAS,EAAE,SAAS;QAC3B,IAAM,OAAO,GAAG,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,QAAQ,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;QACxD,SAAS,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,GAAG,CAAC,OAAO,CAAC,MAAM,KAAK,CAAC,IAAI,OAAO,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC;IAC9E,CAAC;IAED,yCAAW,GAAX,UAAY,UAAU,EAAE,SAAS;QAC7B,IAAM,OAAO,GAAG,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,QAAQ,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;QAExD,IAAI,UAAU,CAAC,YAAY,IAAI,UAAU,CAAC,YAAY,CAAC,MAAM,EAAE;YAC3D,UAAU,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,IAAI,GAAG,CAAC,OAAO,CAAC,MAAM,KAAK,CAAC,IAAI,OAAO,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC;SACrF;aACI,IAAI,UAAU,CAAC,KAAK,IAAI,UAAU,CAAC,KAAK,CAAC,MAAM,EAAE;YAClD,UAAU,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,GAAG,CAAC,UAAU,CAAC,QAAQ,IAAI,OAAO,CAAC,MAAM,KAAK,CAAC,IAAI,IAAI,CAAC,CAAC;SACpF;IACL,CAAC;IACL,0BAAC;AAAD,CAAC,AAxDD,IAwDC;AAED,kBAAe,mBAAmB,CAAC", "sourcesContent": ["/* eslint-disable no-unused-vars */\n/**\n * @todo - Remove unused when JSDoc types are added for visitor methods\n */\nimport Visitor from './visitor';\n\nclass JoinSelectorVisitor {\n    constructor() {\n        this.contexts = [[]];\n        this._visitor = new Visitor(this);\n    }\n\n    run(root) {\n        return this._visitor.visit(root);\n    }\n\n    visitDeclaration(declNode, visitArgs) {\n        visitArgs.visitDeeper = false;\n    }\n\n    visitMixinDefinition(mixinDefinitionNode, visitArgs) {\n        visitArgs.visitDeeper = false;\n    }\n\n    visitRuleset(rulesetNode, visitArgs) {\n        const context = this.contexts[this.contexts.length - 1];\n        const paths = [];\n        let selectors;\n\n        this.contexts.push(paths);\n\n        if (!rulesetNode.root) {\n            selectors = rulesetNode.selectors;\n            if (selectors) {\n                selectors = selectors.filter(function(selector) { return selector.getIsOutput(); });\n                rulesetNode.selectors = selectors.length ? selectors : (selectors = null);\n                if (selectors) { rulesetNode.joinSelectors(paths, context, selectors); }\n            }\n            if (!selectors) { rulesetNode.rules = null; }\n            rulesetNode.paths = paths;\n        }\n    }\n\n    visitRulesetOut(rulesetNode) {\n        this.contexts.length = this.contexts.length - 1;\n    }\n\n    visitMedia(mediaNode, visitArgs) {\n        const context = this.contexts[this.contexts.length - 1];\n        mediaNode.rules[0].root = (context.length === 0 || context[0].multiMedia);\n    }\n\n    visitAtRule(atRuleNode, visitArgs) {\n        const context = this.contexts[this.contexts.length - 1];\n\n        if (atRuleNode.declarations && atRuleNode.declarations.length) {\n            atRuleNode.declarations[0].root = (context.length === 0 || context[0].multiMedia);\n        }\n        else if (atRuleNode.rules && atRuleNode.rules.length) {\n            atRuleNode.rules[0].root = (atRuleNode.isRooted || context.length === 0 || null);\n        }\n    }\n}\n\nexport default JoinSelectorVisitor;\n"]}