{"version": 3, "file": "lessc-helper.js", "sourceRoot": "", "sources": ["../../src/less-node/lessc-helper.js"], "names": [], "mappings": "AAAA,kBAAkB;AAClB,EAAE;AACF,kCAAkC;AAClC,IAAM,YAAY,GAAG;IAEjB,mBAAmB;IACnB,OAAO,EAAG,UAAS,GAAG,EAAE,KAAK;QACzB,IAAM,MAAM,GAAG;YACX,OAAO,EAAO,CAAC,CAAC,EAAI,CAAC,CAAC;YACtB,MAAM,EAAQ,CAAC,CAAC,EAAG,EAAE,CAAC;YACtB,SAAS,EAAK,CAAC,CAAC,EAAG,EAAE,CAAC;YACtB,WAAW,EAAG,CAAC,CAAC,EAAG,EAAE,CAAC;YACtB,QAAQ,EAAM,CAAC,EAAE,EAAE,EAAE,CAAC;YACtB,OAAO,EAAO,CAAC,EAAE,EAAE,EAAE,CAAC;YACtB,KAAK,EAAS,CAAC,EAAE,EAAE,EAAE,CAAC;YACtB,MAAM,EAAQ,CAAC,EAAE,EAAE,EAAE,CAAC;SACzB,CAAC;QACF,OAAO,iBAAQ,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,cAAI,GAAG,oBAAQ,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,MAAG,CAAC;IACtE,CAAC;IAED,6BAA6B;IAC7B,UAAU,EAAE;QACR,OAAO,CAAC,GAAG,CAAC,mEAAmE,CAAC,CAAC;QACjF,OAAO,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;QAChB,OAAO,CAAC,GAAG,CAAC,4EAA4E,CAAC,CAAC;QAC1F,OAAO,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;QAChB,OAAO,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC;QACxB,OAAO,CAAC,GAAG,CAAC,qEAAqE,CAAC,CAAC;QACnF,OAAO,CAAC,GAAG,CAAC,uGAAuG,CAAC,CAAC;QACrH,OAAO,CAAC,GAAG,CAAC,qFAAqF,CAAC,CAAC;QACnG,OAAO,CAAC,GAAG,CAAC,2DAA2D,CAAC,CAAC;QACzE,OAAO,CAAC,GAAG,CAAC,kEAAkE,CAAC,CAAC;QAChF,OAAO,CAAC,GAAG,CAAC,wEAAwE,CAAC,CAAC;QACtF,OAAO,CAAC,GAAG,CAAC,0DAA0D,CAAC,CAAC;QACxE,OAAO,CAAC,GAAG,CAAC,qEAAqE,CAAC,CAAC;QACnF,OAAO,CAAC,GAAG,CAAC,+DAA+D,CAAC,CAAC;QAC7E,OAAO,CAAC,GAAG,CAAC,8DAA8D,CAAC,CAAC;QAC5E,OAAO,CAAC,GAAG,CAAC,0EAA0E,CAAC,CAAC;QACxF,OAAO,CAAC,GAAG,CAAC,gEAAgE,CAAC,CAAC;QAC9E,OAAO,CAAC,GAAG,CAAC,4CAA4C,CAAC,CAAC;QAC1D,OAAO,CAAC,GAAG,CAAC,iGAAiG,CAAC,CAAC;QAC/G,OAAO,CAAC,GAAG,CAAC,gGAAgG,CAAC,CAAC;QAC9G,OAAO,CAAC,GAAG,CAAC,iGAAiG,CAAC,CAAC;QAC/G,OAAO,CAAC,GAAG,CAAC,8FAA8F,CAAC,CAAC;QAC5G,OAAO,CAAC,GAAG,CAAC,iHAAiH,CAAC,CAAC;QAC/H,OAAO,CAAC,GAAG,CAAC,4FAA4F,CAAC,CAAC;QAC1G,OAAO,CAAC,GAAG,CAAC,uDAAuD,CAAC,CAAC;QACrE,OAAO,CAAC,GAAG,CAAC,gGAAgG,CAAC,CAAC;QAC9G,OAAO,CAAC,GAAG,CAAC,6FAA6F,CAAC,CAAC;QAC3G,OAAO,CAAC,GAAG,CAAC,gFAAgF,CAAC,CAAC;QAC9F,OAAO,CAAC,GAAG,CAAC,2FAA2F,CAAC,CAAC;QACzG,OAAO,CAAC,GAAG,CAAC,sGAAsG,CAAC,CAAC;QACpH,OAAO,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;QAChB,OAAO,CAAC,GAAG,CAAC,gBAAgB,CAAC,CAAC;QAC9B,OAAO,CAAC,GAAG,CAAC,kFAAkF,CAAC,CAAC;QAChG,OAAO,CAAC,GAAG,CAAC,gFAAgF,CAAC,CAAC;QAC9F,OAAO,CAAC,GAAG,CAAC,uEAAuE,CAAC,CAAC;QACrF,OAAO,CAAC,GAAG,CAAC,4FAA4F,CAAC,CAAC;QAC1G,OAAO,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;QAChB,OAAO,CAAC,GAAG,CAAC,6FAA6F,CAAC,CAAC;QAC3G,OAAO,CAAC,GAAG,CAAC,4DAA4D,CAAC,CAAC;QAC1E,OAAO,CAAC,GAAG,CAAC,yFAAyF,CAAC,CAAC;QACvG,OAAO,CAAC,GAAG,CAAC,oFAAoF,CAAC,CAAC;QAClG,OAAO,CAAC,GAAG,CAAC,8FAA8F,CAAC,CAAC;QAC5G,OAAO,CAAC,GAAG,CAAC,qGAAqG,CAAC,CAAC;QACnH,OAAO,CAAC,GAAG,CAAC,uGAAuG,CAAC,CAAC;QACrH,OAAO,CAAC,GAAG,CAAC,oGAAoG,CAAC,CAAC;QAClH,OAAO,CAAC,GAAG,CAAC,mFAAmF,CAAC,CAAC;QACjG,OAAO,CAAC,GAAG,CAAC,0GAA0G,CAAC,CAAC;QACxH,OAAO,CAAC,GAAG,CAAC,0DAA0D,CAAC,CAAC;QACxE,OAAO,CAAC,GAAG,CAAC,4DAA4D,CAAC,CAAC;QAC1E,OAAO,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;QAChB,OAAO,CAAC,GAAG,CAAC,wDAAwD,CAAC,CAAC;QACtE,OAAO,CAAC,GAAG,CAAC,gEAAgE,CAAC,CAAC;QAC9E,OAAO,CAAC,GAAG,CAAC,6BAA6B,CAAC,CAAC;QAC3C,OAAO,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;QAChB,OAAO,CAAC,GAAG,CAAC,+DAA+D,CAAC,CAAC;QAC7E,OAAO,CAAC,GAAG,CAAC,+EAA+E,CAAC,CAAC;QAC7F,OAAO,CAAC,GAAG,CAAC,2EAA2E,CAAC,CAAC;QACzF,OAAO,CAAC,GAAG,CAAC,2EAA2E,CAAC,CAAC;QACzF,OAAO,CAAC,GAAG,CAAC,0EAA0E,CAAC,CAAC;QACxF,OAAO,CAAC,GAAG,CAAC,oEAAoE,CAAC,CAAC;QAClF,OAAO,CAAC,GAAG,CAAC,4EAA4E,CAAC,CAAC;QAC1F,OAAO,CAAC,GAAG,CAAC,gGAAgG,CAAC,CAAC;QAC9G,OAAO,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;QAChB,OAAO,CAAC,GAAG,CAAC,uDAAuD,CAAC,CAAC;QACrE,OAAO,CAAC,GAAG,CAAC,kCAAkC,CAAC,CAAC;IACpD,CAAC;CACJ,CAAC;AAEF,2BAA2B;AAC3B,iDAAiD;AACjD,KAAK,IAAM,CAAC,IAAI,YAAY,EAAE;IAAE,IAAI,YAAY,CAAC,cAAc,CAAC,CAAC,CAAC,EAAE;QAAE,OAAO,CAAC,CAAC,CAAC,GAAG,YAAY,CAAC,CAAC,CAAC,CAAC;KAAE;CAAC", "sourcesContent": ["// lessc_helper.js\n//\n//      helper functions for lessc\nconst lessc_helper = {\n\n    // Stylize a string\n    stylize : function(str, style) {\n        const styles = {\n            'reset'     : [0,   0],\n            'bold'      : [1,  22],\n            'inverse'   : [7,  27],\n            'underline' : [4,  24],\n            'yellow'    : [33, 39],\n            'green'     : [32, 39],\n            'red'       : [31, 39],\n            'grey'      : [90, 39]\n        };\n        return `\\x1b[${styles[style][0]}m${str}\\x1b[${styles[style][1]}m`;\n    },\n\n    // Print command line options\n    printUsage: function() {\n        console.log('usage: lessc [option option=parameter ...] <source> [destination]');\n        console.log('');\n        console.log('If source is set to `-\\' (dash or hyphen-minus), input is read from stdin.');\n        console.log('');\n        console.log('options:');\n        console.log('  -h, --help                   Prints help (this message) and exit.');\n        console.log('  --include-path=PATHS         Sets include paths. Separated by `:\\'. `;\\' also supported on windows.');\n        console.log('  -M, --depends                Outputs a makefile import dependency list to stdout.');\n        console.log('  --no-color                   Disables colorized output.');\n        console.log('  --ie-compat                  Enables IE8 compatibility checks.');\n        console.log('  --js                         Enables inline JavaScript in less files');\n        console.log('  -l, --lint                   Syntax check only (lint).');\n        console.log('  -s, --silent                 Suppresses output of error messages.');\n        console.log('  --quiet                      Suppresses output of warnings.');\n        console.log('  --strict-imports             Forces evaluation of imports.');\n        console.log('  --insecure                   Allows imports from insecure https hosts.');\n        console.log('  -v, --version                Prints version number and exit.');\n        console.log('  --verbose                    Be verbose.');\n        console.log('  --source-map[=FILENAME]      Outputs a v3 sourcemap to the filename (or output filename.map).');\n        console.log('  --source-map-rootpath=X      Adds this path onto the sourcemap filename and less file paths.');\n        console.log('  --source-map-basepath=X      Sets sourcemap base path, defaults to current working directory.');\n        console.log('  --source-map-include-source  Puts the less files into the map instead of referencing them.');\n        console.log('  --source-map-inline          Puts the map (and any less files) as a base64 data uri into the output css file.');\n        console.log('  --source-map-url=URL         Sets a custom URL to map file, for sourceMappingURL comment');\n        console.log('                               in generated CSS file.');\n        console.log('  --source-map-no-annotation   Excludes the sourceMappingURL comment from the output css file.');\n        console.log('  -rp, --rootpath=URL          Sets rootpath for url rewriting in relative imports and urls');\n        console.log('                               Works with or without the relative-urls option.');\n        console.log('  -ru=, --rewrite-urls=        Rewrites URLs to make them relative to the base less file.');\n        console.log('    all|local|off              \\'all\\' rewrites all URLs, \\'local\\' just those starting with a \\'.\\'');\n        console.log('');\n        console.log('  -m=, --math=');\n        console.log('     always                    Less will eagerly perform math operations always.');\n        console.log('     parens-division           Math performed except for division (/) operator');\n        console.log('     parens | strict           Math only performed inside parentheses');\n        console.log('     strict-legacy             Parens required in very strict terms (legacy --strict-math)');\n        console.log('');\n        console.log('  -su=on|off                   Allows mixed units, e.g. 1px+1em or 1px*1px which have units');\n        console.log('  --strict-units=on|off        that cannot be represented.');\n        console.log('  --global-var=\\'VAR=VALUE\\'     Defines a variable that can be referenced by the file.');\n        console.log('  --modify-var=\\'VAR=VALUE\\'     Modifies a variable already declared in the file.');\n        console.log('  --url-args=\\'QUERYSTRING\\'     Adds params into url tokens (e.g. 42, cb=42 or \\'a=1&b=2\\')');\n        console.log('  --plugin=PLUGIN=OPTIONS      Loads a plugin. You can also omit the --plugin= if the plugin begins');\n        console.log('                               less-plugin. E.g. the clean css plugin is called less-plugin-clean-css');\n        console.log('                               once installed (npm install less-plugin-clean-css), use either with');\n        console.log('                               --plugin=less-plugin-clean-css or just --clean-css');\n        console.log('                               specify options afterwards e.g. --plugin=less-plugin-clean-css=\"advanced\"');\n        console.log('                               or --clean-css=\"advanced\"');\n        console.log('  --disable-plugin-rule        Disallow @plugin statements');\n        console.log('');\n        console.log('-------------------------- Deprecated ----------------');\n        console.log('  -sm=on|off               Legacy parens-only math. Use --math');\n        console.log('  --strict-math=on|off     ');\n        console.log('');\n        console.log('  --line-numbers=TYPE      Outputs filename and line numbers.');\n        console.log('                           TYPE can be either \\'comments\\', which will output');\n        console.log('                           the debug info within comments, \\'mediaquery\\'');\n        console.log('                           that will output the information within a fake');\n        console.log('                           media query which is compatible with the SASS');\n        console.log('                           format, and \\'all\\' which will do both.');\n        console.log('  -x, --compress           Compresses output by removing some whitespaces.');\n        console.log('                           We recommend you use a dedicated minifer like less-plugin-clean-css');\n        console.log('');\n        console.log('Report bugs to: http://github.com/less/less.js/issues');\n        console.log('Home page: <http://lesscss.org/>');\n    }\n};\n\n// Exports helper functions\n// eslint-disable-next-line no-prototype-builtins\nfor (const h in lessc_helper) { if (lessc_helper.hasOwnProperty(h)) { exports[h] = lessc_helper[h]; }}\n"]}