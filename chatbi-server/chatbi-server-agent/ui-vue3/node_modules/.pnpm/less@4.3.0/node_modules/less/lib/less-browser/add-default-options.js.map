{"version": 3, "file": "add-default-options.js", "sourceRoot": "", "sources": ["../../src/less-browser/add-default-options.js"], "names": [], "mappings": ";;;AAAA,iCAAoC;AACpC,8DAAgC;AAEhC,mBAAe,UAAC,MAAM,EAAE,OAAO;IAE3B,yDAAyD;IACzD,IAAA,mBAAW,EAAC,OAAO,EAAE,iBAAO,CAAC,aAAa,CAAC,MAAM,CAAC,CAAC,CAAC;IAEpD,IAAI,OAAO,CAAC,cAAc,KAAK,SAAS,EAAE;QACtC,OAAO,CAAC,cAAc,GAAG,wDAAwD,CAAC,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;KACpH;IAED,8CAA8C;IAC9C,EAAE;IACF,sDAAsD;IACtD,2DAA2D;IAC3D,mDAAmD;IACnD,EAAE;IACF,OAAO,CAAC,KAAK,GAAG,OAAO,CAAC,KAAK,IAAI,KAAK,CAAC;IACvC,OAAO,CAAC,SAAS,GAAG,OAAO,CAAC,SAAS,IAAI,KAAK,CAAC;IAE/C,+BAA+B;IAC/B,OAAO,CAAC,IAAI,GAAG,OAAO,CAAC,IAAI,IAAI,CAAC,OAAO,CAAC,cAAc,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC;IAEtE,OAAO,CAAC,GAAG,GAAG,OAAO,CAAC,GAAG,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,QAAQ,IAAI,WAAW;QACjE,MAAM,CAAC,QAAQ,CAAC,QAAQ,IAAI,SAAS;QACrC,MAAM,CAAC,QAAQ,CAAC,QAAQ,IAAI,WAAW;QACvC,CAAC,MAAM,CAAC,QAAQ,CAAC,IAAI;YACjB,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC;QACpC,OAAO,CAAC,cAAc,CAAmB,CAAC,CAAC,aAAa;QACxD,CAAC,CAAC,YAAY,CAAC,CAAC;IAEpB,IAAM,eAAe,GAAG,4CAA4C,CAAC,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;IAChG,IAAI,eAAe,EAAE;QACjB,OAAO,CAAC,eAAe,GAAG,eAAe,CAAC,CAAC,CAAC,CAAC;KAChD;IAED,IAAI,OAAO,CAAC,YAAY,KAAK,SAAS,EAAE;QACpC,OAAO,CAAC,YAAY,GAAG,IAAI,CAAC;KAC/B;IAED,IAAI,OAAO,CAAC,OAAO,KAAK,SAAS,EAAE;QAC/B,OAAO,CAAC,OAAO,GAAG,IAAI,CAAC;KAC1B;IAED,IAAI,OAAO,CAAC,YAAY,EAAE;QACtB,OAAO,CAAC,WAAW,GAAG,KAAK,CAAC;KAC/B;AACL,CAAC,EAAC", "sourcesContent": ["import {addDataAttr} from './utils';\nimport browser from './browser';\n\nexport default (window, options) => {\n\n    // use options from the current script tag data attribues\n    addDataAttr(options, browser.currentScript(window));\n\n    if (options.isFileProtocol === undefined) {\n        options.isFileProtocol = /^(file|(chrome|safari)(-extension)?|resource|qrc|app):/.test(window.location.protocol);\n    }\n\n    // Load styles asynchronously (default: false)\n    //\n    // This is set to `false` by default, so that the body\n    // doesn't start loading before the stylesheets are parsed.\n    // Setting this to `true` can result in flickering.\n    //\n    options.async = options.async || false;\n    options.fileAsync = options.fileAsync || false;\n\n    // Interval between watch polls\n    options.poll = options.poll || (options.isFileProtocol ? 1000 : 1500);\n\n    options.env = options.env || (window.location.hostname == '127.0.0.1' ||\n        window.location.hostname == '0.0.0.0'   ||\n        window.location.hostname == 'localhost' ||\n        (window.location.port &&\n            window.location.port.length > 0)      ||\n        options.isFileProtocol                   ? 'development'\n        : 'production');\n\n    const dumpLineNumbers = /!dumpLineNumbers:(comments|mediaquery|all)/.exec(window.location.hash);\n    if (dumpLineNumbers) {\n        options.dumpLineNumbers = dumpLineNumbers[1];\n    }\n\n    if (options.useFileCache === undefined) {\n        options.useFileCache = true;\n    }\n\n    if (options.onReady === undefined) {\n        options.onReady = true;\n    }\n\n    if (options.relativeUrls) {\n        options.rewriteUrls = 'all';\n    }\n};\n"]}