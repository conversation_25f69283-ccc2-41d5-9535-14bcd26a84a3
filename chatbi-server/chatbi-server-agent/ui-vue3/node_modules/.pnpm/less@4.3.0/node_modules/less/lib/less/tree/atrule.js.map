{"version": 3, "file": "atrule.js", "sourceRoot": "", "sources": ["../../../src/less/tree/atrule.js"], "names": [], "mappings": ";;;AAAA,wDAA0B;AAC1B,gEAAkC;AAClC,8DAAgC;AAChC,kEAAoC;AACpC,4EAAuD;AAEvD,IAAM,MAAM,GAAG,UACX,IAAI,EACJ,KAAK,EACL,KAAK,EACL,KAAK,EACL,eAAe,EACf,SAAS,EACT,QAAQ,EACR,cAAc;IARH,iBA0Dd;IAhDG,IAAI,CAAC,CAAC;IACN,IAAI,SAAS,GAAG,CAAC,IAAI,kBAAQ,CAAC,EAAE,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,oBAAoB,EAAE,CAAC;IAEnG,IAAI,CAAC,IAAI,GAAI,IAAI,CAAC;IAClB,IAAI,CAAC,KAAK,GAAG,CAAC,KAAK,YAAY,cAAI,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,mBAAS,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC;IACtF,IAAI,KAAK,EAAE;QACP,IAAI,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE;YACtB,IAAM,eAAe,GAAG,IAAI,CAAC,iBAAiB,CAAC,KAAK,CAAC,CAAC;YAEtD,IAAI,wBAAsB,GAAG,IAAI,CAAC;YAClC,KAAK,CAAC,OAAO,CAAC,UAAA,IAAI;gBACd,IAAI,IAAI,CAAC,IAAI,KAAK,SAAS,IAAI,IAAI,CAAC,KAAK;oBAAE,wBAAsB,GAAG,wBAAsB,IAAI,KAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC;YAC3I,CAAC,CAAC,CAAC;YAEH,IAAI,eAAe,IAAI,CAAC,QAAQ,EAAE;gBAC9B,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC;gBACxB,IAAI,CAAC,YAAY,GAAG,KAAK,CAAC;aAC7B;iBAAM,IAAI,wBAAsB,IAAI,KAAK,CAAC,MAAM,KAAK,CAAC,IAAI,CAAC,QAAQ,IAAI,CAAC,KAAK,EAAE;gBAC5E,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC;gBACxB,IAAI,CAAC,YAAY,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC;aAC/D;iBAAM;gBACH,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;aACtB;SACJ;aAAM;YACH,IAAM,eAAe,GAAG,IAAI,CAAC,iBAAiB,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;YAE5D,IAAI,eAAe,IAAI,CAAC,QAAQ,IAAI,CAAC,KAAK,EAAE;gBACxC,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC;gBACxB,IAAI,CAAC,YAAY,GAAG,KAAK,CAAC,KAAK,CAAC;aACnC;iBAAM;gBACH,IAAI,CAAC,KAAK,GAAG,CAAC,KAAK,CAAC,CAAC;gBACrB,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,SAAS,GAAG,CAAC,IAAI,kBAAQ,CAAC,EAAE,EAAE,IAAI,EAAE,IAAI,EAAE,KAAK,EAAE,eAAe,CAAC,CAAC,CAAC,oBAAoB,EAAE,CAAC;aAC3G;SACJ;QACD,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE;YACnB,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;gBACpC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,YAAY,GAAG,IAAI,CAAC;aACrC;SACJ;QACD,IAAI,CAAC,SAAS,CAAC,SAAS,EAAE,IAAI,CAAC,CAAC;QAChC,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC;KACpC;IACD,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC;IACpB,IAAI,CAAC,SAAS,GAAG,eAAe,CAAC;IACjC,IAAI,CAAC,SAAS,GAAG,SAAS,CAAC;IAC3B,IAAI,CAAC,QAAQ,GAAG,QAAQ,IAAI,KAAK,CAAC;IAClC,IAAI,CAAC,kBAAkB,CAAC,cAAc,CAAC,CAAC;IACxC,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;AAC1B,CAAC,CAAA;AAED,MAAM,CAAC,SAAS,GAAG,MAAM,CAAC,MAAM,CAAC,IAAI,cAAI,EAAE,sCACvC,IAAI,EAAE,QAAQ,IAEX,wBAAuB,KAE1B,iBAAiB,YAAC,KAAK,EAAE,SAAiB;QAAjB,0BAAA,EAAA,iBAAiB;QACtC,IAAI,CAAC,SAAS,EAAE;YACZ,OAAO,KAAK,CAAC,MAAM,CAAC,UAAU,IAAI,IAAI,OAAO,CAAC,IAAI,CAAC,IAAI,KAAK,aAAa,IAAI,IAAI,CAAC,IAAI,KAAK,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAA,CAAA,CAAC,CAAC,CAAC,MAAM,KAAK,KAAK,CAAC,MAAM,CAAC;SAClJ;aAAM;YACH,OAAO,KAAK,CAAC,MAAM,CAAC,UAAU,IAAI,IAAI,OAAO,CAAC,IAAI,CAAC,IAAI,KAAK,aAAa,IAAI,IAAI,CAAC,IAAI,KAAK,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,KAAK,KAAK,CAAC,MAAM,CAAC;SACrI;IACL,CAAC,EAED,MAAM,YAAC,OAAO;QACV,IAAM,KAAK,GAAG,IAAI,CAAC,KAAK,EAAE,KAAK,GAAG,IAAI,CAAC,KAAK,EAAE,YAAY,GAAG,IAAI,CAAC,YAAY,CAAC;QAE/E,IAAI,KAAK,EAAE;YACP,IAAI,CAAC,KAAK,GAAG,OAAO,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC;SAC1C;aAAM,IAAI,YAAY,EAAE;YACrB,IAAI,CAAC,YAAY,GAAG,OAAO,CAAC,UAAU,CAAC,YAAY,CAAC,CAAC;SACxD;QACD,IAAI,KAAK,EAAE;YACP,IAAI,CAAC,KAAK,GAAG,OAAO,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;SACrC;IACL,CAAC,EAED,aAAa;QACT,OAAO,IAAI,CAAC,KAAK,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,CAAC;IAC3C,CAAC,EAED,SAAS;QACL,OAAO,UAAU,KAAK,IAAI,CAAC,IAAI,CAAC;IACpC,CAAC,EAED,MAAM,YAAC,OAAO,EAAE,MAAM;QAClB,IAAM,KAAK,GAAG,IAAI,CAAC,KAAK,EAAE,KAAK,GAAG,IAAI,CAAC,KAAK,IAAI,IAAI,CAAC,YAAY,CAAC;QAClE,MAAM,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,QAAQ,EAAE,EAAE,IAAI,CAAC,QAAQ,EAAE,CAAC,CAAC;QACxD,IAAI,KAAK,EAAE;YACP,MAAM,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;YAChB,KAAK,CAAC,MAAM,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC;SACjC;QACD,IAAI,IAAI,CAAC,WAAW,EAAE;YAClB,IAAI,CAAC,aAAa,CAAC,OAAO,EAAE,MAAM,EAAE,IAAI,CAAC,YAAY,CAAC,CAAC;SAC1D;aAAM,IAAI,KAAK,EAAE;YACd,IAAI,CAAC,aAAa,CAAC,OAAO,EAAE,MAAM,EAAE,KAAK,CAAC,CAAC;SAC9C;aAAM;YACH,MAAM,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;SACnB;IACL,CAAC,EAED,IAAI,YAAC,OAAO;QACR,IAAI,eAAe,EAAE,iBAAiB,EAAE,KAAK,GAAG,IAAI,CAAC,KAAK,EAAE,KAAK,GAAG,IAAI,CAAC,KAAK,IAAI,IAAI,CAAC,YAAY,CAAC;QAEpG,6DAA6D;QAC7D,qCAAqC;QACrC,eAAe,GAAG,OAAO,CAAC,SAAS,CAAC;QACpC,iBAAiB,GAAG,OAAO,CAAC,WAAW,CAAC;QACxC,qCAAqC;QACrC,OAAO,CAAC,SAAS,GAAG,EAAE,CAAC;QACvB,OAAO,CAAC,WAAW,GAAG,EAAE,CAAC;QAEzB,IAAI,KAAK,EAAE;YACP,KAAK,GAAG,KAAK,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;SAC/B;QAED,IAAI,KAAK,EAAE;YACP,KAAK,GAAG,IAAI,CAAC,QAAQ,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC;SACzC;QACD,IAAI,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,IAAI,KAAK,CAAC,CAAC,CAAC,CAAC,KAAK,IAAI,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,IAAI,KAAK,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,MAAM,EAAE;YAClG,IAAM,wBAAwB,GAAG,IAAI,CAAC,iBAAiB,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC;YAC9E,IAAI,wBAAwB,IAAI,CAAC,IAAI,CAAC,QAAQ,IAAI,CAAC,KAAK,EAAE;gBACtD,IAAI,UAAU,GAAG,OAAO,CAAC,aAAa,CAAC,IAAI,CAAC,QAAQ,CAAC,YAAY,CAAC,SAAS,CAAC,WAAW,CAAC;gBACxF,UAAU,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC;gBAC3B,KAAK,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC;gBACvB,KAAK,CAAC,OAAO,CAAC,UAAA,IAAI,IAAI,OAAA,IAAI,CAAC,KAAK,GAAG,KAAK,EAAlB,CAAkB,CAAC,CAAC;aAC7C;SACJ;QACD,IAAI,IAAI,CAAC,WAAW,IAAI,KAAK,EAAE;YAC3B,KAAK,CAAC,CAAC,CAAC,CAAC,gBAAgB,GAAG,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,gBAAgB,CAAC,OAAO,EAAE,CAAC;YACzE,KAAK,GAAE,KAAK,CAAC,GAAG,CAAC,UAAU,IAAI,IAAI,OAAO,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;SACpE;QAED,qCAAqC;QACrC,OAAO,CAAC,SAAS,GAAG,eAAe,CAAC;QACpC,OAAO,CAAC,WAAW,GAAG,iBAAiB,CAAC;QACxC,OAAO,IAAI,MAAM,CAAC,IAAI,CAAC,IAAI,EAAE,KAAK,EAAE,KAAK,EAAE,IAAI,CAAC,QAAQ,EAAE,EAAE,IAAI,CAAC,QAAQ,EAAE,EAAE,IAAI,CAAC,SAAS,EAAE,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC,cAAc,EAAE,CAAC,CAAC;IACvI,CAAC,EAED,QAAQ,YAAC,OAAO,EAAE,KAAK;QACnB,IAAI,cAAc,GAAG,CAAC,CAAC;QACvB,IAAI,gBAAgB,GAAG,CAAC,CAAC;QACzB,IAAI,YAAY,GAAG,IAAI,CAAC;QACxB,IAAI,aAAa,GAAG,KAAK,CAAC;QAE1B,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE;YACnB,KAAK,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC;SACpC;QAED,IAAI,kBAAkB,GAAG,EAAE,CAAC;QAC5B,IAAI,OAAO,CAAC,MAAM,CAAC,MAAM,GAAG,CAAC,EAAE;oCAClB,KAAK;gBACV,IAAM,KAAK,GAAG,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;gBACpC,IACI,KAAK,CAAC,IAAI,KAAK,SAAS;oBACxB,KAAK,CAAC,KAAK;oBACX,KAAK,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,EACxB;oBACE,IAAI,KAAK,IAAI,CAAC,KAAK,CAAC,IAAI,IAAI,KAAK,CAAC,SAAS,IAAI,KAAK,CAAC,SAAS,CAAC,MAAM,GAAG,CAAC,EAAE;wBACvE,kBAAkB,GAAG,kBAAkB,CAAC,MAAM,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC;qBACnE;iBACJ;gBACD,IAAI,kBAAkB,CAAC,MAAM,GAAG,CAAC,EAAE;oBAC/B,IAAI,OAAK,GAAG,EAAE,CAAC;oBACf,IAAM,MAAM,GAAG,EAAE,GAAG,EAAE,UAAU,CAAC,IAAI,OAAK,IAAI,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;oBACrD,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,kBAAkB,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;wBAChD,kBAAkB,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC;qBACjD;oBACD,IAAI,MAAM,CAAC,IAAI,CAAC,OAAK,CAAC,OAAO,CAAC,MAAM,EAAE,EAAE,CAAC,CAAC,EAAE;wBACxC,YAAY,GAAG,KAAK,CAAC;wBACrB,gBAAgB,EAAE,CAAC;qBACtB;yBAAM;wBACH,aAAa,GAAG,KAAK,CAAC;wBACtB,cAAc,EAAE,CAAC;qBACpB;iBACJ;;YAxBL,KAAK,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,OAAO,CAAC,MAAM,CAAC,MAAM,EAAE,KAAK,EAAE;wBAAjD,KAAK;aAyBb;SACJ;QAED,IAAM,eAAe,GAAG,cAAc,GAAG,CAAC,IAAI,gBAAgB,GAAG,CAAC,IAAI,CAAC,aAAa,IAAI,CAAC,YAAY,CAAC;QACtG,IACI,CAAC,IAAI,CAAC,QAAQ,IAAI,cAAc,GAAG,CAAC,IAAI,gBAAgB,KAAK,CAAC,IAAI,CAAC,aAAa,IAAI,YAAY,CAAC;eAC9F,CAAC,eAAe,EACrB;YACE,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,GAAG,IAAI,CAAC;SACxB;QACD,OAAO,KAAK,CAAC;IACjB,CAAC,EAED,QAAQ,YAAC,IAAI;QACT,IAAI,IAAI,CAAC,KAAK,EAAE;YACZ,8FAA8F;YAC9F,OAAO,iBAAO,CAAC,SAAS,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC;SAC/D;IACL,CAAC,EAED,IAAI;QACA,IAAI,IAAI,CAAC,KAAK,EAAE;YACZ,8FAA8F;YAC9F,OAAO,iBAAO,CAAC,SAAS,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,SAAS,CAAC,CAAC;SACjE;IACL,CAAC,EAED,QAAQ;QACJ,IAAI,IAAI,CAAC,KAAK,EAAE;YACZ,8FAA8F;YAC9F,OAAO,iBAAO,CAAC,SAAS,CAAC,QAAQ,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;SAC1D;IACL,CAAC,EAED,aAAa,YAAC,OAAO,EAAE,MAAM,EAAE,KAAK;QAChC,IAAM,OAAO,GAAG,KAAK,CAAC,MAAM,CAAC;QAC7B,IAAI,CAAC,CAAC;QACN,OAAO,CAAC,QAAQ,GAAG,CAAC,OAAO,CAAC,QAAQ,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC;QAE9C,aAAa;QACb,IAAI,OAAO,CAAC,QAAQ,EAAE;YAClB,MAAM,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;YAChB,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,OAAO,EAAE,CAAC,EAAE,EAAE;gBAC1B,KAAK,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC;aACpC;YACD,MAAM,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;YAChB,OAAO,CAAC,QAAQ,EAAE,CAAC;YACnB,OAAO;SACV;QAED,iBAAiB;QACjB,IAAM,SAAS,GAAG,YAAK,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAE,EAAE,UAAU,GAAG,UAAG,SAAS,OAAI,CAAC;QAC3F,IAAI,CAAC,OAAO,EAAE;YACV,MAAM,CAAC,GAAG,CAAC,YAAK,SAAS,MAAG,CAAC,CAAC;SACjC;aAAM;YACH,MAAM,CAAC,GAAG,CAAC,YAAK,UAAU,CAAE,CAAC,CAAC;YAC9B,KAAK,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC;YACjC,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,OAAO,EAAE,CAAC,EAAE,EAAE;gBAC1B,MAAM,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC;gBACvB,KAAK,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC;aACpC;YACD,MAAM,CAAC,GAAG,CAAC,UAAG,SAAS,MAAG,CAAC,CAAC;SAC/B;QAED,OAAO,CAAC,QAAQ,EAAE,CAAC;IACvB,CAAC,IACH,CAAC;AAEH,kBAAe,MAAM,CAAC", "sourcesContent": ["import Node from './node';\nimport Selector from './selector';\nimport Ruleset from './ruleset';\nimport Anonymous from './anonymous';\nimport NestableAtRulePrototype from './nested-at-rule';\n\nconst AtRule = function(\n    name,\n    value,\n    rules,\n    index,\n    currentFileInfo,\n    debugInfo,\n    isRooted,\n    visibilityInfo\n) {\n    let i;\n    var selectors = (new Selector([], null, null, this._index, this._fileInfo)).createEmptySelectors();\n\n    this.name  = name;\n    this.value = (value instanceof Node) ? value : (value ? new Anonymous(value) : value);\n    if (rules) {\n        if (Array.isArray(rules)) {\n            const allDeclarations = this.declarationsBlock(rules);\n           \n            let allRulesetDeclarations = true;\n            rules.forEach(rule => {\n                if (rule.type === 'Ruleset' && rule.rules) allRulesetDeclarations = allRulesetDeclarations && this.declarationsBlock(rule.rules, true);\n            });\n\n            if (allDeclarations && !isRooted) {\n                this.simpleBlock = true;\n                this.declarations = rules;\n            } else if (allRulesetDeclarations && rules.length === 1 && !isRooted && !value) {\n                this.simpleBlock = true;\n                this.declarations = rules[0].rules ? rules[0].rules : rules;\n            } else {\n                this.rules = rules;\n            }\n        } else {\n            const allDeclarations = this.declarationsBlock(rules.rules);\n            \n            if (allDeclarations && !isRooted && !value) {\n                this.simpleBlock = true;\n                this.declarations = rules.rules;\n            } else {\n                this.rules = [rules];\n                this.rules[0].selectors = (new Selector([], null, null, index, currentFileInfo)).createEmptySelectors();\n            }\n        }\n        if (!this.simpleBlock) {\n            for (i = 0; i < this.rules.length; i++) {\n                this.rules[i].allowImports = true;\n            }\n        }\n        this.setParent(selectors, this);\n        this.setParent(this.rules, this);\n    }\n    this._index = index;\n    this._fileInfo = currentFileInfo;\n    this.debugInfo = debugInfo;\n    this.isRooted = isRooted || false;\n    this.copyVisibilityInfo(visibilityInfo);\n    this.allowRoot = true;\n}\n\nAtRule.prototype = Object.assign(new Node(), {\n    type: 'AtRule',\n\n    ...NestableAtRulePrototype,\n\n    declarationsBlock(rules, mergeable = false) {\n        if (!mergeable) {\n            return rules.filter(function (node) { return (node.type === 'Declaration' || node.type === 'Comment') && !node.merge}).length === rules.length;\n        } else {\n            return rules.filter(function (node) { return (node.type === 'Declaration' || node.type === 'Comment'); }).length === rules.length;\n        }\n    },\n\n    accept(visitor) {\n        const value = this.value, rules = this.rules, declarations = this.declarations;\n\n        if (rules) {\n            this.rules = visitor.visitArray(rules);\n        } else if (declarations) {\n            this.declarations = visitor.visitArray(declarations);   \n        }\n        if (value) {\n            this.value = visitor.visit(value);\n        }\n    },\n\n    isRulesetLike() {\n        return this.rules || !this.isCharset();\n    },\n\n    isCharset() {\n        return '@charset' === this.name;\n    },\n\n    genCSS(context, output) {\n        const value = this.value, rules = this.rules || this.declarations;\n        output.add(this.name, this.fileInfo(), this.getIndex());\n        if (value) {\n            output.add(' ');\n            value.genCSS(context, output);\n        }\n        if (this.simpleBlock) {\n            this.outputRuleset(context, output, this.declarations);\n        } else if (rules) {\n            this.outputRuleset(context, output, rules);\n        } else {\n            output.add(';');\n        }\n    },\n\n    eval(context) {\n        let mediaPathBackup, mediaBlocksBackup, value = this.value, rules = this.rules || this.declarations;\n \n        // media stored inside other atrule should not bubble over it\n        // backpup media bubbling information\n        mediaPathBackup = context.mediaPath;\n        mediaBlocksBackup = context.mediaBlocks;\n        // deleted media bubbling information\n        context.mediaPath = [];\n        context.mediaBlocks = [];\n\n        if (value) {\n            value = value.eval(context);\n        }\n\n        if (rules) {\n            rules = this.evalRoot(context, rules);\n        }\n        if (Array.isArray(rules) && rules[0].rules && Array.isArray(rules[0].rules) && rules[0].rules.length) {\n            const allMergeableDeclarations = this.declarationsBlock(rules[0].rules, true);\n            if (allMergeableDeclarations && !this.isRooted && !value) {\n                var mergeRules = context.pluginManager.less.visitors.ToCSSVisitor.prototype._mergeRules;\n                mergeRules(rules[0].rules);\n                rules = rules[0].rules;\n                rules.forEach(rule => rule.merge = false);\n            }\n        }\n        if (this.simpleBlock && rules) {\n            rules[0].functionRegistry = context.frames[0].functionRegistry.inherit();\n            rules= rules.map(function (rule) { return rule.eval(context); });\n        }\n\n        // restore media bubbling information\n        context.mediaPath = mediaPathBackup;\n        context.mediaBlocks = mediaBlocksBackup;\n        return new AtRule(this.name, value, rules, this.getIndex(), this.fileInfo(), this.debugInfo, this.isRooted, this.visibilityInfo());\n    },\n\n    evalRoot(context, rules) {\n        let ampersandCount = 0;\n        let noAmpersandCount = 0;\n        let noAmpersands = true;\n        let allAmpersands = false;\n\n        if (!this.simpleBlock) {\n            rules = [rules[0].eval(context)];\n        }\n\n        let precedingSelectors = [];\n        if (context.frames.length > 0) {\n            for (let index = 0; index < context.frames.length; index++) {\n                const frame = context.frames[index];\n                if (\n                    frame.type === 'Ruleset' &&\n                    frame.rules &&\n                    frame.rules.length > 0\n                ) {\n                    if (frame && !frame.root && frame.selectors && frame.selectors.length > 0) {\n                        precedingSelectors = precedingSelectors.concat(frame.selectors);\n                    }\n                }\n                if (precedingSelectors.length > 0) {\n                    let value = '';\n                    const output = { add: function (s) { value += s; } };\n                    for (let i = 0; i < precedingSelectors.length; i++) {\n                        precedingSelectors[i].genCSS(context, output);\n                    }\n                    if (/^&+$/.test(value.replace(/\\s+/g, ''))) {\n                        noAmpersands = false;\n                        noAmpersandCount++;\n                    } else {\n                        allAmpersands = false;\n                        ampersandCount++;\n                    }\n                }\n            }\n        }\n\n        const mixedAmpersands = ampersandCount > 0 && noAmpersandCount > 0 && !allAmpersands && !noAmpersands;\n        if (\n            (this.isRooted && ampersandCount > 0 && noAmpersandCount === 0 && !allAmpersands && noAmpersands)\n            || !mixedAmpersands\n        ) {\n            rules[0].root = true;\n        }\n        return rules;\n    },\n\n    variable(name) {\n        if (this.rules) {\n            // assuming that there is only one rule at this point - that is how parser constructs the rule\n            return Ruleset.prototype.variable.call(this.rules[0], name);\n        }\n    },\n\n    find() {\n        if (this.rules) {\n            // assuming that there is only one rule at this point - that is how parser constructs the rule\n            return Ruleset.prototype.find.apply(this.rules[0], arguments);\n        }\n    },\n\n    rulesets() {\n        if (this.rules) {\n            // assuming that there is only one rule at this point - that is how parser constructs the rule\n            return Ruleset.prototype.rulesets.apply(this.rules[0]);\n        }\n    },\n\n    outputRuleset(context, output, rules) {\n        const ruleCnt = rules.length;\n        let i;\n        context.tabLevel = (context.tabLevel | 0) + 1;\n\n        // Compressed\n        if (context.compress) {\n            output.add('{');\n            for (i = 0; i < ruleCnt; i++) {\n                rules[i].genCSS(context, output);\n            }\n            output.add('}');\n            context.tabLevel--;\n            return;\n        }\n\n        // Non-compressed\n        const tabSetStr = `\\n${Array(context.tabLevel).join('  ')}`, tabRuleStr = `${tabSetStr}  `;\n        if (!ruleCnt) {\n            output.add(` {${tabSetStr}}`);\n        } else {\n            output.add(` {${tabRuleStr}`);\n            rules[0].genCSS(context, output);\n            for (i = 1; i < ruleCnt; i++) {\n                output.add(tabRuleStr);\n                rules[i].genCSS(context, output);\n            }\n            output.add(`${tabSetStr}}`);\n        }\n\n        context.tabLevel--;\n    }\n});\n\nexport default AtRule;\n"]}