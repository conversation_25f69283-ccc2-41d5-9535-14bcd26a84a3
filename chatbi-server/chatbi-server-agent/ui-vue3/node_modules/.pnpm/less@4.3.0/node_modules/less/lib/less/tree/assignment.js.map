{"version": 3, "file": "assignment.js", "sourceRoot": "", "sources": ["../../../src/less/tree/assignment.js"], "names": [], "mappings": ";;;AAAA,wDAA0B;AAE1B,IAAM,UAAU,GAAG,UAAS,GAAG,EAAE,GAAG;IAChC,IAAI,CAAC,GAAG,GAAG,GAAG,CAAC;IACf,IAAI,CAAC,KAAK,GAAG,GAAG,CAAC;AACrB,CAAC,CAAA;AAED,UAAU,CAAC,SAAS,GAAG,MAAM,CAAC,MAAM,CAAC,IAAI,cAAI,EAAE,EAAE;IAC7C,IAAI,EAAE,YAAY;IAElB,MAAM,YAAC,OAAO;QACV,IAAI,CAAC,KAAK,GAAG,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;IAC3C,CAAC;IAED,IAAI,YAAC,OAAO;QACR,IAAI,IAAI,CAAC,KAAK,CAAC,IAAI,EAAE;YACjB,OAAO,IAAI,UAAU,CAAC,IAAI,CAAC,GAAG,EAAE,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC;SAC7D;QACD,OAAO,IAAI,CAAC;IAChB,CAAC;IAED,MAAM,YAAC,OAAO,EAAE,MAAM;QAClB,MAAM,CAAC,GAAG,CAAC,UAAG,IAAI,CAAC,GAAG,MAAG,CAAC,CAAC;QAC3B,IAAI,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE;YACnB,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC;SACtC;aAAM;YACH,MAAM,CAAC,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;SAC1B;IACL,CAAC;CACJ,CAAC,CAAC;AAEH,kBAAe,UAAU,CAAC", "sourcesContent": ["import Node from './node';\n\nconst Assignment = function(key, val) {\n    this.key = key;\n    this.value = val;\n}\n\nAssignment.prototype = Object.assign(new Node(), {\n    type: 'Assignment',\n\n    accept(visitor) {\n        this.value = visitor.visit(this.value);\n    },\n\n    eval(context) {\n        if (this.value.eval) {\n            return new Assignment(this.key, this.value.eval(context));\n        }\n        return this;\n    },\n\n    genCSS(context, output) {\n        output.add(`${this.key}=`);\n        if (this.value.genCSS) {\n            this.value.genCSS(context, output);\n        } else {\n            output.add(this.value);\n        }\n    }\n});\n\nexport default Assignment;\n"]}