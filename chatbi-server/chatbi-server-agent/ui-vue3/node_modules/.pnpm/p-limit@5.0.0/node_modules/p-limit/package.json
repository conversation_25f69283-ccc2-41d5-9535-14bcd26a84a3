{"name": "p-limit", "version": "5.0.0", "description": "Run multiple promise-returning & async functions with limited concurrency", "license": "MIT", "repository": "sindresorhus/p-limit", "funding": "https://github.com/sponsors/sindresorhus", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com"}, "type": "module", "exports": {"types": "./index.d.ts", "default": "./index.js"}, "imports": {"#async_hooks": {"node": "async_hooks", "default": "./async-hooks-stub.js"}}, "engines": {"node": ">=18"}, "scripts": {"test": "xo && ava && tsd"}, "files": ["index.js", "index.d.ts", "async-hooks-stub.js"], "keywords": ["promise", "limit", "limited", "concurrency", "throttle", "throat", "rate", "batch", "ratelimit", "task", "queue", "async", "await", "promises", "bluebird"], "dependencies": {"yocto-queue": "^1.0.0"}, "devDependencies": {"ava": "^5.3.1", "delay": "^6.0.0", "in-range": "^3.0.0", "random-int": "^3.0.0", "time-span": "^5.1.0", "tsd": "^0.29.0", "xo": "^0.56.0"}}