{"name": "d3-geo", "version": "3.1.1", "description": "Shapes and calculators for spherical coordinates.", "homepage": "https://d3js.org/d3-geo/", "repository": {"type": "git", "url": "https://github.com/d3/d3-geo.git"}, "keywords": ["d3", "d3-module", "geo", "maps", "cartography"], "license": "ISC", "author": {"name": "<PERSON>", "url": "https://bost.ocks.org/mike"}, "type": "module", "files": ["dist/**/*.js", "src/**/*.js"], "module": "src/index.js", "main": "src/index.js", "jsdelivr": "dist/d3-geo.min.js", "unpkg": "dist/d3-geo.min.js", "exports": {"umd": "./dist/d3-geo.min.js", "default": "./src/index.js"}, "sideEffects": false, "dependencies": {"d3-array": "2.5.0 - 3"}, "devDependencies": {"@rollup/plugin-terser": "0.4", "canvas": "2", "d3-format": "1 - 3", "eslint": "8", "mocha": "10", "pixelmatch": "5", "pngjs": "6", "rollup": "3", "topojson-client": "3", "world-atlas": "1"}, "scripts": {"test": "mocha 'test/**/*-test.js' && eslint src test", "prepublishOnly": "rm -rf dist && rollup -c", "postpublish": "git push && git push --tags && cd ../d3.github.com && git pull && cp ../${npm_package_name}/dist/${npm_package_name}.js ${npm_package_name}.v${npm_package_version%%.*}.js && cp ../${npm_package_name}/dist/${npm_package_name}.min.js ${npm_package_name}.v${npm_package_version%%.*}.min.js && git add ${npm_package_name}.v${npm_package_version%%.*}.js ${npm_package_name}.v${npm_package_version%%.*}.min.js && git commit -m \"${npm_package_name} ${npm_package_version}\" && git push && cd -"}, "engines": {"node": ">=12"}}