{"name": "nanopop", "version": "2.4.2", "description": "Ultra tiny, opinionated, positioning engine.", "author": "<PERSON> <<EMAIL>>", "license": "MIT", "packageManager": "pnpm@8.0.0", "main": "./dist/nanopop.umd.js", "module": "./dist/nanopop.mjs", "types": "./dist/src/index.d.ts", "exports": {".": {"import": "./dist/nanopop.mjs", "require": "./dist/nanopop.umd.js", "types": "./dist/src/index.d.ts"}}, "keywords": ["positioning", "popper", "utility", "positioning engine"], "files": ["src", "dist"], "scripts": {"dev": "vite", "build": "vite build", "serve": "vite preview --port 3005 --outDir .", "lint": "eslint ./{src,tests}/**/*.ts", "lint:fix": "npm run lint -- --fix", "test": "playwright test", "test:ci": "npm run lint:fix && npm run build && npm test"}, "homepage": "https://github.com/Simonwep/nanopop#readme", "bugs": "https://github.com/Simonwep/nanopop/issues", "repository": {"type": "git", "url": "https://github.com/Simonwep/nanopop.git"}, "devDependencies": {"@playwright/test": "1.41.0", "@typescript-eslint/eslint-plugin": "6.19.0", "@typescript-eslint/parser": "6.19.0", "eslint": "8.56.0", "typescript": "5.3.3", "vite": "5.0.11", "vite-plugin-banner": "0.7.1", "vite-plugin-dts": "3.7.1"}}