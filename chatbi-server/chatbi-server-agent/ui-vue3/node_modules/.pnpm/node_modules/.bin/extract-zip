#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*) basedir=`cygpath -w "$basedir"`;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/Users/<USER>/projects/chatbi/chatbi-server/chatbi-server-agent/ui-vue3/node_modules/.pnpm/extract-zip@2.0.1_supports-color@8.1.1/node_modules/extract-zip/node_modules:/Users/<USER>/projects/chatbi/chatbi-server/chatbi-server-agent/ui-vue3/node_modules/.pnpm/extract-zip@2.0.1_supports-color@8.1.1/node_modules:/Users/<USER>/projects/chatbi/chatbi-server/chatbi-server-agent/ui-vue3/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/Users/<USER>/projects/chatbi/chatbi-server/chatbi-server-agent/ui-vue3/node_modules/.pnpm/extract-zip@2.0.1_supports-color@8.1.1/node_modules/extract-zip/node_modules:/Users/<USER>/projects/chatbi/chatbi-server/chatbi-server-agent/ui-vue3/node_modules/.pnpm/extract-zip@2.0.1_supports-color@8.1.1/node_modules:/Users/<USER>/projects/chatbi/chatbi-server/chatbi-server-agent/ui-vue3/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../extract-zip/cli.js" "$@"
else
  exec node  "$basedir/../extract-zip/cli.js" "$@"
fi
