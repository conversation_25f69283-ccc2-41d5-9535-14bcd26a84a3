{"name": "merge2", "description": "Merge multiple streams into one stream in sequence or parallel.", "authors": ["<PERSON> <<EMAIL>>"], "license": "MIT", "version": "1.4.1", "main": "./index.js", "repository": {"type": "git", "url": "**************:teambition/merge2.git"}, "homepage": "https://github.com/teambition/merge2", "keywords": ["merge2", "multiple", "sequence", "parallel", "merge", "stream", "merge stream", "sync"], "engines": {"node": ">= 8"}, "dependencies": {}, "devDependencies": {"standard": "^14.3.4", "through2": "^3.0.1", "thunks": "^4.9.6", "tman": "^1.10.0", "to-through": "^2.0.0"}, "scripts": {"test": "standard && tman"}, "files": ["README.md", "index.js"]}