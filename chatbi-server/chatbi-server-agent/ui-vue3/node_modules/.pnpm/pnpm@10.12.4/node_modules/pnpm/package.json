{"name": "pnpm", "version": "10.12.4", "description": "Fast, disk space efficient package manager", "keywords": ["pnpm", "pnpm10", "dependencies", "dependency manager", "efficient", "fast", "hardlinks", "install", "installer", "link", "lockfile", "modules", "monorepo", "multi-package", "npm", "package manager", "package.json", "packages", "prune", "rapid", "remove", "shrinkwrap", "symlinks", "uninstall", "workspace"], "license": "MIT", "funding": "https://opencollective.com/pnpm", "repository": {"type": "git", "url": "git+https://github.com/pnpm/pnpm.git", "directory": "pnpm"}, "homepage": "https://pnpm.io", "bugs": {"url": "https://github.com/pnpm/pnpm/issues"}, "main": "bin/pnpm.cjs", "exports": {".": "./package.json"}, "files": ["dist", "bin"], "bin": {"pnpm": "bin/pnpm.cjs", "pnpx": "bin/pnpx.cjs"}, "directories": {"test": "test"}, "unpkg": "dist/pnpm.cjs", "__dependencies": {"v8-compile-cache": "2.4.0"}, "__optionalDependencies": {"node-gyp": "^11.1.0"}, "__devDependencies": {"@pnpm/assert-project": "workspace:*", "@pnpm/byline": "catalog:", "@pnpm/cache.commands": "workspace:*", "@pnpm/cli-meta": "workspace:*", "@pnpm/cli-utils": "workspace:*", "@pnpm/client": "workspace:*", "@pnpm/command": "workspace:*", "@pnpm/common-cli-options-help": "workspace:*", "@pnpm/config": "workspace:*", "@pnpm/constants": "workspace:*", "@pnpm/core-loggers": "workspace:*", "@pnpm/crypto.hash": "workspace:*", "@pnpm/default-reporter": "workspace:*", "@pnpm/dependency-path": "workspace:*", "@pnpm/env.path": "workspace:*", "@pnpm/error": "workspace:*", "@pnpm/exec.build-commands": "workspace:*", "@pnpm/filter-workspace-packages": "workspace:*", "@pnpm/find-workspace-dir": "workspace:*", "@pnpm/lockfile.types": "workspace:*", "@pnpm/logger": "workspace:*", "@pnpm/modules-yaml": "workspace:*", "@pnpm/nopt": "catalog:", "@pnpm/parse-cli-args": "workspace:*", "@pnpm/plugin-commands-audit": "workspace:*", "@pnpm/plugin-commands-completion": "workspace:*", "@pnpm/plugin-commands-config": "workspace:*", "@pnpm/plugin-commands-deploy": "workspace:*", "@pnpm/plugin-commands-doctor": "workspace:*", "@pnpm/plugin-commands-env": "workspace:*", "@pnpm/plugin-commands-init": "workspace:*", "@pnpm/plugin-commands-installation": "workspace:*", "@pnpm/plugin-commands-licenses": "workspace:*", "@pnpm/plugin-commands-listing": "workspace:*", "@pnpm/plugin-commands-outdated": "workspace:*", "@pnpm/plugin-commands-patching": "workspace:*", "@pnpm/plugin-commands-publishing": "workspace:*", "@pnpm/plugin-commands-rebuild": "workspace:*", "@pnpm/plugin-commands-script-runners": "workspace:*", "@pnpm/plugin-commands-server": "workspace:*", "@pnpm/plugin-commands-setup": "workspace:*", "@pnpm/plugin-commands-store": "workspace:*", "@pnpm/plugin-commands-store-inspecting": "workspace:*", "@pnpm/prepare": "workspace:*", "@pnpm/read-package-json": "workspace:*", "@pnpm/read-project-manifest": "workspace:*", "@pnpm/registry-mock": "catalog:", "@pnpm/run-npm": "workspace:*", "@pnpm/store.cafs": "workspace:*", "@pnpm/tabtab": "catalog:", "@pnpm/test-fixtures": "workspace:*", "@pnpm/test-ipc-server": "workspace:*", "@pnpm/tools.path": "workspace:*", "@pnpm/tools.plugin-commands-self-updater": "workspace:*", "@pnpm/types": "workspace:*", "@pnpm/worker": "workspace:*", "@pnpm/workspace.find-packages": "workspace:*", "@pnpm/workspace.pkgs-graph": "workspace:*", "@pnpm/workspace.read-manifest": "workspace:*", "@pnpm/workspace.state": "workspace:*", "@pnpm/write-project-manifest": "workspace:*", "@types/cross-spawn": "catalog:", "@types/is-windows": "catalog:", "@types/pnpm__byline": "catalog:", "@types/ramda": "catalog:", "@types/semver": "catalog:", "@zkochan/retry": "catalog:", "@zkochan/rimraf": "catalog:", "chalk": "catalog:", "ci-info": "catalog:", "cross-spawn": "catalog:", "deep-require-cwd": "catalog:", "delay": "catalog:", "dir-is-case-sensitive": "catalog:", "esbuild": "catalog:", "execa": "catalog:", "exists-link": "catalog:", "is-windows": "catalog:", "load-json-file": "catalog:", "loud-rejection": "catalog:", "normalize-newline": "catalog:", "p-any": "catalog:", "p-defer": "catalog:", "path-name": "catalog:", "pidtree": "catalog:", "ps-list": "catalog:", "ramda": "catalog:", "read-yaml-file": "catalog:", "render-help": "catalog:", "semver": "catalog:", "split-cmd": "catalog:", "symlink-dir": "catalog:", "tempy": "catalog:", "tree-kill": "catalog:", "write-json-file": "catalog:", "write-pkg": "catalog:", "write-yaml-file": "catalog:"}, "engines": {"node": ">=18.12"}, "jest": {"preset": "@pnpm/jest-config/with-registry"}, "preferGlobal": true, "publishConfig": {"tag": "next-10", "executableFiles": ["./dist/node-gyp-bin/node-gyp", "./dist/node-gyp-bin/node-gyp.cmd", "./dist/node_modules/node-gyp/bin/node-gyp.js"]}, "scripts": {"bundle": "ts-node bundle.ts", "start": "tsc --watch", "lint": "eslint \"src/**/*.ts\" \"test/**/*.ts\"", "pretest:e2e": "rimraf node_modules/.bin/pnpm", "_test": "jest", "test": "pnpm run compile && pnpm run _test", "_compile": "tsc --build", "compile": "tsc --build && pnpm run lint --fix && rimraf dist bin/nodes && pnpm run bundle && shx cp -r node-gyp-bin dist/node-gyp-bin && shx cp -r node_modules/@pnpm/tabtab/lib/templates dist/templates && shx cp -r node_modules/ps-list/vendor dist/vendor && shx cp pnpmrc dist/pnpmrc"}}