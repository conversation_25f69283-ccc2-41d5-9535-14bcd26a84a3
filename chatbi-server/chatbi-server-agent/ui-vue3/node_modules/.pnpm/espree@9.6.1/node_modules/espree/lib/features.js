/**
 * @fileoverview The list of feature flags supported by the parser and their default
 *      settings.
 * <AUTHOR>
 */

//------------------------------------------------------------------------------
// Requirements
//------------------------------------------------------------------------------

// None!

//------------------------------------------------------------------------------
// Public
//------------------------------------------------------------------------------

export default {

    // React JSX parsing
    jsx: false,

    // allow return statement in global scope
    globalReturn: false,

    // allow implied strict mode
    impliedStrict: false
};
