#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*) basedir=`cygpath -w "$basedir"`;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/Users/<USER>/projects/chatbi/chatbi-server/chatbi-server-agent/ui-vue3/node_modules/.pnpm/he@1.2.0/node_modules/he/bin/node_modules:/Users/<USER>/projects/chatbi/chatbi-server/chatbi-server-agent/ui-vue3/node_modules/.pnpm/he@1.2.0/node_modules/he/node_modules:/Users/<USER>/projects/chatbi/chatbi-server/chatbi-server-agent/ui-vue3/node_modules/.pnpm/he@1.2.0/node_modules:/Users/<USER>/projects/chatbi/chatbi-server/chatbi-server-agent/ui-vue3/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/Users/<USER>/projects/chatbi/chatbi-server/chatbi-server-agent/ui-vue3/node_modules/.pnpm/he@1.2.0/node_modules/he/bin/node_modules:/Users/<USER>/projects/chatbi/chatbi-server/chatbi-server-agent/ui-vue3/node_modules/.pnpm/he@1.2.0/node_modules/he/node_modules:/Users/<USER>/projects/chatbi/chatbi-server/chatbi-server-agent/ui-vue3/node_modules/.pnpm/he@1.2.0/node_modules:/Users/<USER>/projects/chatbi/chatbi-server/chatbi-server-agent/ui-vue3/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../../bin/he" "$@"
else
  exec node  "$basedir/../../bin/he" "$@"
fi
