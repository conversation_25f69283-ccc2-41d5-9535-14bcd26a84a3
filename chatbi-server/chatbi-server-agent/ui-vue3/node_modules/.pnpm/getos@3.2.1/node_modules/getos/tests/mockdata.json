[{"desc": "OS X", "platform": "darwin", "expected": {"os": "darwin"}}, {"desc": "Windows", "platform": "win32", "expected": {"os": "win32"}}, {"desc": "Ubuntu 14.10", "platform": "linux", "file": {"/etc/lsb-release": "DISTRIB_ID=Ubuntu\nDISTRIB_RELEASE=14.10\nDISTRIB_CODENAME=utopic\nDISTRIB_DESCRIPTION=\"Ubuntu 14.10\"\n"}, "expected": {"codename": "utopic", "dist": "Ubuntu", "os": "linux", "release": "14.10"}}, {"desc": "Ubuntu 14.04", "platform": "linux", "file": {"/etc/lsb-release": "DISTRIB_ID=Ubuntu\nDISTRIB_RELEASE=14.04\nDISTRIB_CODENAME=trusty\nDISTRIB_DESCRIPTION=\"Ubuntu 14.04.2 LTS\"\n"}, "expected": {"codename": "trusty", "dist": "Ubuntu", "os": "linux", "release": "14.04"}}, {"desc": "Ubuntu 12.04", "platform": "linux", "file": {"/etc/lsb-release": "DISTRIB_ID=Ubuntu\nDISTRIB_RELEASE=12.04\nDISTRIB_CODENAME=precise\nDISTRIB_DESCRIPTION=\"Ubuntu 12.04.5 LTS\"\n"}, "expected": {"codename": "precise", "dist": "Ubuntu", "os": "linux", "release": "12.04"}}, {"desc": "Fedora 21", "platform": "linux", "file": {"/etc/fedora-release": "Fedora release 21 (Twenty One)\n"}, "expected": {"codename": "Twenty One", "dist": "<PERSON><PERSON>", "os": "linux", "release": "21"}}, {"desc": "CentOS 5.11", "platform": "linux", "file": {"/etc/redhat-release": "CentOS release 5.11 (Final)\n"}, "expected": {"codename": "final", "dist": "CentOS", "os": "linux", "release": "5.11"}}, {"desc": "CentOS 6.6", "platform": "linux", "file": {"/etc/redhat-release": "CentOS release 6.6 (Final)\n"}, "expected": {"codename": "final", "dist": "CentOS", "os": "linux", "release": "6.6"}}, {"desc": "CentOS 7.1", "platform": "linux", "file": {"/etc/redhat-release": "CentOS Linux release 7.1.1503 (Core)\n"}, "expected": {"codename": "core", "dist": "CentOS", "os": "linux", "release": "7.1.1503"}}, {"desc": "RHEL 6.5", "platform": "linux", "file": {"/etc/redhat-release": "Red Hat Enterprise Linux Server release 6.5 (Santiago)\n"}, "expected": {"codename": "santiago", "dist": "Red Hat Linux", "os": "linux", "release": "6.5"}}, {"desc": "RHEL 6.6", "platform": "linux", "file": {"/etc/redhat-release": "Red Hat Enterprise Linux Server release 6.6 (Santiago)\n"}, "expected": {"codename": "santiago", "dist": "Red Hat Linux", "os": "linux", "release": "6.6"}}, {"desc": "RHEL 7.0", "platform": "linux", "file": {"/etc/redhat-release": "Red Hat Enterprise Linux Server release 7.0 (<PERSON><PERSON>)\n"}, "expected": {"codename": "ma<PERSON>o", "dist": "Red Hat Linux", "os": "linux", "release": "7.0"}}, {"desc": "SUSE Linux", "platform": "linux", "file": {"/etc/SuSE-release": "SUSE Linux Enterprise Server 11 (x86_64)\nVERSION = 11\nPATCHLEVEL = 4\n"}, "expected": {"dist": "SUSE Linux", "os": "linux", "release": "11"}}, {"desc": "Alpine Linux", "platform": "linux", "file": {"/etc/alpine-release": "3.3\n", "/etc/os-release": "NAME=\"Alpine Linux\"\nID=alpine\nVERSION_ID=3.9.2\nPRETTY_NAME=\"Alpine Linux v3.9\"\nHOME_URL=\"https://alpinelinux.org/\"\nBUG_REPORT_URL=\"https://bugs.alpinelinux.org/\"\n"}, "expected": {"dist": "Alpine Linux", "os": "linux", "release": "3.3"}}, {"desc": "Amazon Linux", "platform": "linux", "file": {"/etc/system-release": "Amazon Linux AMI release 2016.03"}, "expected": {"dist": "Amazon Linux", "os": "linux", "release": "2016.03"}}, {"desc": "Arch Linux", "platform": "linux", "file": {"/etc/lsb-release": "LSB_VERSION=1.4\nDISTRIB_ID=Arch\nDISTRIB_RELEASE=rolling\nDISTRIB_DESCRIPTION=\"Arch Linux\""}, "expected": {"dist": "Arch Linux", "os": "linux", "release": "rolling"}}, {"desc": "Linux Mint", "platform": "linux", "file": {"/etc/lsb-release": "DISTRIB_ID=LinuxMint\nDISTRIB_RELEASE=18\nDISTRIB_CODENAME=sarah\nDISTRIB_DESCRIPTION=\"Linux Mint 18 Sarah\""}, "expected": {"dist": "Linux Mint", "os": "linux", "codename": "sarah", "release": "18"}}, {"desc": "Raspbian 8", "platform": "linux", "file": {"/etc/os-release": "PRETTY_NAME=\"Raspbian GNU/Linux 8 (jessie)\"\nNAME=\"Raspbian GNU/Linux\"\nVERSION_ID=\"8\"\nVERSION=\"8 (jessie)\"\nID=raspbian\nHOME_URL=\"http://www.raspbian.org/\"\nSUPPORT_URL=\"http://www.raspbian.org/RaspbianForums\"\nBUG_REPORT_URL=\"http://www.raspbian.org/RaspbianBugs\""}, "expected": {"codename": "jessie", "dist": "Raspbian", "os": "linux", "release": "8"}}, {"desc": "Manjaro Linux", "platform": "linux", "file": {"/etc/lsb-release": "DISTRIB_ID=ManjaroLinux\nDISTRIB_RELEASE=17.1.10\nDISTRIB_CODENAME=Hakoila\nDISTRIB_DESCRIPTION=\"Manjaro Linux\""}, "expected": {"codename": "hakoila", "dist": "Manjaro Linux", "os": "linux", "release": "17.1.10"}}, {"desc": "KDE neon", "platform": "linux", "file": {"/etc/lsb-release": "DISTRIB_ID=neon\nDISTRIB_RELEASE=18.04\nDISTRIB_CODENAME=bionic\nDISTRIB_DESCRIPTION=\"KDE neon User Edition 5.16\""}, "expected": {"codename": "bionic", "dist": "KDE neon", "os": "linux", "release": "18.04"}}, {"desc": "NixOS", "platform": "linux", "file": {"/etc/NIXOS": " "}, "expected": {"dist": "NixOS", "os": "linux"}}]