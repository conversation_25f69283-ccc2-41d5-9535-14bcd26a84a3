{"/etc/fedora-release": ["<PERSON><PERSON>"], "/etc/redhat-release": ["RHEL", "RHAS", "Red Hat Linux", "Scientific Linux", "ScientificSL", "ScientificCERNSLC", "ScientificFermiLTS", "ScientificSLF", "CentOS"], "/etc/redhat_version": ["RHEL", "RHAS", "Red Hat Linux", "Scientific Linux", "ScientificSL", "ScientificCERNSLC", "ScientificFermiLTS", "ScientificSLF"], "/etc/SuSE-release": ["SUSE Linux"], "/etc/lsb-release": ["Ubuntu", "Chakra", "IYCC", "Linux Mint", "elementary OS", "Arch Linux", "Manjaro Linux", "KDE neon", "<PERSON><PERSON><PERSON>"], "/etc/debian_version": ["Debian"], "/etc/debian_release": ["Debian"], "/etc/arch-release": ["Arch Linux"], "/etc/NIXOS": ["NixOS"], "/etc/annvix-release": ["Annvix"], "/etc/arklinux-release": ["Arklinux"], "/etc/aurox-release": ["Aurox Linux"], "/etc/blackcat-release": ["BlackCat"], "/etc/cobalt-release": ["Cobalt"], "/etc/conectiva-release": ["Conectiva"], "/etc/eos-version": ["FreeEOS"], "/etc/gentoo-release": ["Gentoo Linux"], "/etc/hlfs-release": ["HLFS"], "/etc/hlfs_version": ["HFLS"], "/etc/immunix-release": ["Immunix"], "/knoppix_version": ["Knoppix"], "/etc/lfs-release": ["Linux-From-Scratch"], "/etc/lfs_version": ["Linux-From-Scratch"], "/etc/linuxppc-release": ["Linux-PPC"], "/etc/mageia-release": ["Mageia"], "/etc/mandriva-release": ["Mandriva Linux", "Mandrake Linux"], "/etc/mandakelinux-release": ["Mandriva Linux", "Mandrake Linux"], "/etc/mandrake-release": ["Mandrake", "Mandriva Linux", "Mandrake Linux"], "/etc/mklinux-release": ["MkLinux"], "/etc/nld-release": ["Novell Linux Desktop"], "/etc/pld-release": ["PLD Linux"], "/etc/rubix-version": ["Rubix"], "/etc/slackware-version": ["Slackware"], "/etc/slackware-release": ["Slackware"], "/etc/e-smith-release": ["SME Server"], "/etc/release": ["Solaris SPARC"], "/etc/sun-release": ["Sun JDS"], "/etc/novell-release": ["SUSE Linux"], "/etc/sles-release": ["SUSE Linux ES9"], "/etc/synoinfo.conf": ["Synology"], "/etc/tinysofa-release": ["Tiny <PERSON>"], "/etc/trustix-release": ["Trustix"], "/etc/trustix-version": ["Trustix"], "/etc/turbolinux-release": ["TurboLinux"], "/etc/ultrapenguin-release": ["Ultra<PERSON>enguin"], "/etc/UnitedLinux-release": ["UnitedLinux"], "/etc/va-release": ["VA-Linux/RH-VALE"], "/etc/yellowdog-release": ["Yellow Dog"], "/etc/alpine-release": ["Alpine Linux"], "/etc/system-release": ["Amazon Linux"], "/etc/os-release": ["Raspbian"]}