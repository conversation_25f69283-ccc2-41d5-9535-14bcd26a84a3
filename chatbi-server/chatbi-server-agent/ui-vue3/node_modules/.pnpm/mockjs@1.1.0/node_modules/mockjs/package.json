{"name": "mockjs", "title": "Mock.js", "description": "生成随机数据 & 拦截 Ajax 请求", "version": "1.1.0", "homepage": "http://mockjs.com/", "keywords": ["mock", "mockJSON", "mockAjax"], "author": "<EMAIL>", "dependencies": {"commander": "*"}, "devDependencies": {"gulp": "^3.9.0", "gulp-connect": "^5.7.0", "gulp-coveralls": "^0.1.4", "gulp-istanbul": "^0.10.4", "gulp-jshint": "^2.1.0", "gulp-mocha": "^2.2.0", "gulp-mocha-phantomjs": "^0.10.1", "jshint": "^2.8.0", "jshint-stylish": "^2.1.0", "webpack": "^1.12.9"}, "repository": {"type": "git", "url": "git://github.com/nuysoft/Mock.git"}, "main": "./dist/mock.js", "scripts": {"test": "gulp mocha", "coveralls": "gulp coveralls"}, "bin": {"random": "bin/random"}, "licenses": [{"type": "MIT", "url": "https://github.com/nuysoft/Mock/blob/master/LICENSE"}], "spm": {"main": "dist/mock.js"}}