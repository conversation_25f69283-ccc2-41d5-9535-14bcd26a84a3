#!/usr/bin/env node
var needle = require('./../lib/needle');

function exit(code, str) {
  console.log(str) || process.exit(code);
}

function usage() {
  var out = ['Usage: needle [get|head|post|put|delete] url [query]'];
  out.push('Examples: \n needle get google.com\n needle post server.com/api foo=bar');
  exit(1, out.join('\n'))
}

if (process.argv[2] == '-v' || process.argv[2] == '--version')
  exit(0, needle.version);
else if (process.argv[2] == null)
  usage();

var method   = process.argv[2],
    url      = process.argv[3],
    options  = { compressed: true, parse_response: true, follow_max: 5, timeout: 10000 };

if (!needle[method]) {
  url    = method;
  method = 'get';
}

var callback = function(err, resp) {
  if (err) return exit(1, "Error: " + err.message);

  if (process.argv.indexOf('-i') != -1)
    console.log(resp.headers) || console.log('');

  console.log(resp.body.toString());
};

if (method == 'post' || method == 'put')
  needle[method](url, process.argv[4], options, callback);
else
  needle[method](url, options, callback);
