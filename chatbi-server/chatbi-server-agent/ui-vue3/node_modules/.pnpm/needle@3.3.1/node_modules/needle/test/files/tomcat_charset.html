<?xml version="1.0" encoding="EUC-JP"?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" xml:lang="ja" lang="ja">
<head><script src="//archive.org/includes/analytics.js?v=cf34f82" type="text/javascript"></script>
<script type="text/javascript">window.addEventListener('DOMContentLoaded',function(){var v=archive_analytics.values;v.service='wb';v.server_name='wwwb-app224.us.archive.org';v.server_ms=381;archive_analytics.send_pageview({});});</script>
<script type="text/javascript" src="/_static/js/bundle-playback.js?v=poeZ53Bz" charset="utf-8"></script>
<script type="text/javascript" src="/_static/js/wombat.js?v=UHAOicsW" charset="utf-8"></script>
<script type="text/javascript">
  __wm.init("https://web.archive.org/web");
  __wm.wombat("http://www.nina.jp:80/server/slackware/webapp/tomcat_charset.html","**************","https://web.archive.org/","web","/_static/",
	      "1538598547");
</script>
<link rel="stylesheet" type="text/css" href="/_static/css/banner-styles.css?v=fantwOh2" />
<link rel="stylesheet" type="text/css" href="/_static/css/iconochive.css?v=qtvMKcIJ" />
<!-- End Wayback Rewrite JS Include -->

<meta http-equiv="content-type" content="text/html"/>
<meta http-equiv="content-style-type" content="text/css"/>
<title>TomcatでUTF-8/EUCを使う</title>
<link rel="stylesheet" type="text/css" href="/web/**************cs_/http://www.nina.jp/html.css"/>
<link rel="shortcut icon" href="https://web.archive.org/web/**************im_/http://www.nina.jp/img/nina.ico"/>
</head>
<body><!-- BEGIN WAYBACK TOOLBAR INSERT -->
<style type="text/css">
body {
  margin-top:0 !important;
  padding-top:0 !important;
  /*min-width:800px !important;*/
}
</style>
<script>__wm.rw(0);</script>
<div id="wm-ipp-base" lang="en" style="display:none;direction:ltr;">
<div id="wm-ipp" style="position:fixed;left:0;top:0;right:0;">
<div id="donato" style="position:relative;width:100%;">
  <div id="donato-base">
    <iframe id="donato-if" src="https://archive.org/includes/donate.php?as_page=1&amp;platform=wb&amp;referer=https%3A//web.archive.org/web/**************/http%3A//www.nina.jp/server/slackware/webapp/tomcat_charset.html"
	    scrolling="no" frameborder="0" style="width:100%; height:100%">
    </iframe>
  </div>
</div><div id="wm-ipp-inside">
  <div id="wm-toolbar" style="position:relative;display:flex;flex-flow:row nowrap;justify-content:space-between;">
    <div id="wm-logo" style="/*width:110px;*/padding-top:12px;">
      <a href="/web/" title="Wayback Machine home page"><img src="/_static/images/toolbar/wayback-toolbar-logo-200.png" srcset="/_static/images/toolbar/wayback-toolbar-logo-100.png, /_static/images/toolbar/wayback-toolbar-logo-150.png 1.5x, /_static/images/toolbar/wayback-toolbar-logo-200.png 2x" alt="Wayback Machine" style="width:100px" border="0" /></a>
    </div>
    <div class="c" style="display:flex;flex-flow:column nowrap;justify-content:space-between;flex:1;">
      <form class="u" style="display:flex;flex-direction:row;flex-wrap:nowrap;" target="_top" method="get" action="/web/submit" name="wmtb" id="wmtb"><input type="text" name="url" id="wmtbURL" value="http://www.nina.jp/server/slackware/webapp/tomcat_charset.html" onfocus="this.focus();this.select();" style="flex:1;"/><input type="hidden" name="type" value="replay" /><input type="hidden" name="date" value="**************" /><input type="submit" value="Go" />
      </form>
      <div style="display:flex;flex-flow:row nowrap;align-items:flex-end;">
		<div class="s" id="wm-nav-captures">
	  	  <a class="t" href="/web/***************/http://www.nina.jp/server/slackware/webapp/tomcat_charset.html" title="See a list of every capture for this URL">5 captures</a>
	  <div class="r" title="Timespan for captures of this URL">10 Jan 2012 - 03 Oct 2018</div>
	  </div>
	<div class="k" style="flex:1;">
	  <a href="" id="wm-graph-anchor">
	    <div id="wm-ipp-sparkline" title="Explore captures for this URL" style="position: relative">
	      <canvas id="wm-sparkline-canvas" width="675" height="27" border="0"></canvas>
	    </div>
	  </a>
	</div>
      </div>
    </div>
    <div class="n">
      <table>
	<tbody>
	  <!-- NEXT/PREV MONTH NAV AND MONTH INDICATOR -->
	  <tr class="m">
	    <td class="b" nowrap="nowrap"><a href="https://web.archive.org/web/20160912162501/http://www.nina.jp:80/server/slackware/webapp/tomcat_charset.html" title="12 Sep 2016"><strong>Sep</strong></a></td>
	    <td class="c" id="displayMonthEl" title="You are here: 20:29:07 Oct 03, 2018">OCT</td>
	    <td class="f" nowrap="nowrap">Nov</td>
	  </tr>
	  <!-- NEXT/PREV CAPTURE NAV AND DAY OF MONTH INDICATOR -->
	  <tr class="d">
	    <td class="b" nowrap="nowrap"><a href="https://web.archive.org/web/20160912162501/http://www.nina.jp:80/server/slackware/webapp/tomcat_charset.html" title="16:25:01 Sep 12, 2016"><img src="/_static/images/toolbar/wm_tb_prv_on.png" alt="Previous capture" width="14" height="16" border="0" /></a></td>
	    <td class="c" id="displayDayEl" style="width:34px;font-size:22px;white-space:nowrap;" title="You are here: 20:29:07 Oct 03, 2018">03</td>
	    <td class="f" nowrap="nowrap"><img src="/_static/images/toolbar/wm_tb_nxt_off.png" alt="Next capture" width="14" height="16" border="0" /></td>
	  </tr>
	  <!-- NEXT/PREV YEAR NAV AND YEAR INDICATOR -->
	  <tr class="y">
	    <td class="b" nowrap="nowrap"><a href="https://web.archive.org/web/20160912162501/http://www.nina.jp:80/server/slackware/webapp/tomcat_charset.html" title="12 Sep 2016"><strong>2016</strong></a></td>
	    <td class="c" id="displayYearEl" title="You are here: 20:29:07 Oct 03, 2018">2018</td>
	    <td class="f" nowrap="nowrap">2019</td>
	  </tr>
	</tbody>
      </table>
    </div>
    <div class="r" style="display:flex;flex-flow:column nowrap;align-items:flex-end;justify-content:space-between;">
      <div id="wm-btns" style="text-align:right;height:23px;">
        	<span class="xxs">
          <div id="wm-save-snapshot-success">success</div>
          <div id="wm-save-snapshot-fail">fail</div>
          <a id="wm-save-snapshot-open" href="#"
	     title="Share via My Web Archive" >
            <span class="iconochive-web"></span>
          </a>
          <a href="https://archive.org/account/login.php" title="Sign In" id="wm-sign-in">
            <span class="iconochive-person"></span>
          </a>
          <span id="wm-save-snapshot-in-progress" class="iconochive-web"></span>
	</span>
        	<a class="xxs" href="http://faq.web.archive.org/" title="Get some help using the Wayback Machine" style="top:-6px;"><span class="iconochive-question" style="color:rgb(87,186,244);font-size:160%;"></span></a>
	<a id="wm-tb-close" href="#close" style="top:-2px;" title="Close the toolbar"><span class="iconochive-remove-circle" style="color:#888888;font-size:240%;"></span></a>
      </div>
      <div id="wm-share" class="xxs">
        <a href="/web/**************/http://web.archive.org/screenshot/http://www.nina.jp/server/slackware/webapp/tomcat_charset.html"
           id="wm-screenshot"
           title="screenshot">
          <span class="wm-icon-screen-shot"></span>
        </a>
        <a href="#" id="wm-video" title="video">
          <span class="iconochive-movies"></span>
        </a>
	<a id="wm-share-facebook" href="#" data-url="https://web.archive.org/web/**************/http://www.nina.jp:80/server/slackware/webapp/tomcat_charset.html" title="Share on Facebook" style="margin-right:5px;" target="_blank"><span class="iconochive-facebook" style="color:#3b5998;font-size:160%;"></span></a>
	<a id="wm-share-twitter" href="#" data-url="https://web.archive.org/web/**************/http://www.nina.jp:80/server/slackware/webapp/tomcat_charset.html" title="Share on Twitter" style="margin-right:5px;" target="_blank"><span class="iconochive-twitter" style="color:#1dcaff;font-size:160%;"></span></a>
      </div>
      <div style="padding-right:2px;text-align:right;white-space:nowrap;">
	<a id="wm-expand" class="wm-btn wm-closed" href="#expand" onclick="__wm.ex(event);return false;"><span id="wm-expand-icon" class="iconochive-down-solid"></span> <span class="xxs" style="font-size:80%;">About this capture</span></a>
      </div>
    </div>
  </div>
    <div id="wm-capinfo" style="border-top:1px solid #777;display:none; overflow: hidden">
                    <div id="wm-capinfo-collected-by">
    <div style="background-color:#666;color:#fff;font-weight:bold;text-align:center">COLLECTED BY</div>
    <div style="padding:3px;position:relative" id="wm-collected-by-content">
            <div style="display:inline-block;vertical-align:top;width:50%;">
			<span class="c-logo" style="background-image:url(https://archive.org/services/img/alexacrawls);"></span>
		Organization: <a style="color:#33f;" href="https://archive.org/details/alexacrawls" target="_new"><span class="wm-title">Alexa Crawls</span></a>
		<div style="max-height:75px;overflow:hidden;position:relative;">
	  <div style="position:absolute;top:0;left:0;width:100%;height:75px;background:linear-gradient(to bottom,rgba(255,255,255,0) 0%,rgba(255,255,255,0) 90%,rgba(255,255,255,255) 100%);"></div>
	  Starting in 1996, <a href="http://www.alexa.com/">Alexa Internet</a> has been donating their crawl data to the Internet Archive.  Flowing in every day, these data are added to the <a href="http://web.archive.org/">Wayback Machine</a> after an embargo period.
	</div>
	      </div>
      <div style="display:inline-block;vertical-align:top;width:49%;">
			<span class="c-logo" style="background-image:url(https://archive.org/services/img/alexacrawls)"></span>
		<div>Collection: <a style="color:#33f;" href="https://archive.org/details/alexacrawls" target="_new"><span class="wm-title">Alexa Crawls</span></a></div>
		<div style="max-height:75px;overflow:hidden;position:relative;">
	  <div style="position:absolute;top:0;left:0;width:100%;height:75px;background:linear-gradient(to bottom,rgba(255,255,255,0) 0%,rgba(255,255,255,0) 90%,rgba(255,255,255,255) 100%);"></div>
	  Starting in 1996, <a href="http://www.alexa.com/">Alexa Internet</a> has been donating their crawl data to the Internet Archive.  Flowing in every day, these data are added to the <a href="http://web.archive.org/">Wayback Machine</a> after an embargo period.
	</div>
	      </div>
    </div>
    </div>
    <div id="wm-capinfo-timestamps">
    <div style="background-color:#666;color:#fff;font-weight:bold;text-align:center" title="Timestamps for the elements of this page">TIMESTAMPS</div>
    <div>
      <div id="wm-capresources" style="margin:0 5px 5px 5px;max-height:250px;overflow-y:scroll !important"></div>
      <div id="wm-capresources-loading" style="text-align:left;margin:0 20px 5px 5px;display:none"><img src="/_static/images/loading.gif" alt="loading" /></div>
    </div>
    </div>
  </div></div></div></div><div id="wm-ipp-print">The Wayback Machine - https://web.archive.org/web/**************/http://www.nina.jp:80/server/slackware/webapp/tomcat_charset.html</div>
<script type="text/javascript">
__wm.bt(675,27,25,2,"web","http://www.nina.jp/server/slackware/webapp/tomcat_charset.html","**************",1996,"/_static/",["/_static/css/banner-styles.css?v=fantwOh2","/_static/css/iconochive.css?v=qtvMKcIJ"], false);
  __wm.rw(1);
</script>
<!-- END WAYBACK TOOLBAR INSERT -->
<p class="dig1"><span class="title1">TomcatでUTF-8/EUC-JPを使う</span></p>
<p class="dig1">
[<a href="/web/**************/http://www.nina.jp/server/index-slackware.html">サーバの実験室 Slackware</a>]
</p>
<p class="right">
作成 : 2004/12/31
</p>
<form action="/web/**************/http://www.nina.jp/namazu/namazu.cgi" method="get">
<p class="right">
"サーバの実験室"の検索
<input type="text" name="query" size="25" value=""/>
<input type="submit" value="検索"/>
<input type="reset" value="クリア"/>
</p>
</form>
<hr/>

<p class="dig2">
Tomcatから返されるキャラクタセットの情報は、<span class="bold">httpd.conf</span>のルートで指定したAddDefaultCharsetの値と同じになるらしい。
Directoryディレクティブの中で指定したAddDefaultCharsetは無視されるっぽい。
ついでに、metaタグも無視されるみたい。
<span class="blue">（&lt;---このへん、後述のSetCharacterEncodingFilterがちゃんと動作しないこともあり、自信なし...）</span>
</p>

<p class="dig2">
うちのWEBサーバはルートのAddDefaultCharsetでEUC-JP指定をしており、コンテキストパス以下はUTF-8にしたいので、なんらかの対策をしないと文字化けしてしまう。
</p>

<p class="dig1"><span class="title2">サーブレットの場合</span></p>
<p class="dig2">
<span class="bold">response.setContentType</span>でキャラクタセットを指定する。
EUC-JPを使用するなら、response.setContentType("text/html; <span class="bold">charset=EUC-JP</span>")。
UTF-8を使用するなら、response.setContentType("text/html; <span class="bold">charset=UTF-8</span>")。
</p>
<pre class="dig3 text">
# HelloWorld.java

import java.io.*;
import java.text.*;
import java.util.*;
import javax.servlet.*;
import javax.servlet.http.*;

public class HelloWorld extends HttpServlet {

    public void doGet(HttpServletRequest request,
                      HttpServletResponse response)
        throws IOException, ServletException
    {
        <span class="red">response.setContentType("text/html; charset=EUC-JP");</span>
        PrintWriter out = response.getWriter();

        out.println("&lt;html&gt;");
        out.println("&lt;head&gt;");
        out.println("&lt;title&gt;HelloWorld&lt;/title&gt;");
        out.println("&lt;/head&gt;");
        out.println("&lt;body&gt;");
        out.println("&lt;p&gt;");
        out.println("こんにちは世界");
        out.println("&lt;/p&gt;");
        out.println("&lt;/body&gt;");
        out.println("&lt;/html&gt;");
    }
}
</pre>
<p class="dig2">
JAVAはUTF-8で処理を行うので、それ以外のEUC-JPなどを使用する場合は、コンパイルするときに<span class="bold">-encoding</span>をつけること。
</p>
<pre class="dig3 shell">
# <span class="bold">javac <span class="red">-encoding EUC-JP</span> -classpath .:$CATALINA_HOME/common/lib/servlet-api.jar HelloWorld.java</span>
</pre>

<p class="dig1"><span class="title2">JSPの場合</span></p>
<p class="dig2">
ディレクティブでキャラクタセットを指定する。
EUC-JPを使用するなら、&lt;%@ page contentType="text/html; <span class="bold">charset=EUC-JP</span>" %&gt;。
UTF-8を使用するなら、&lt;%@ page contentType="text/html; <span class="bold">charset=UTF-8</span>" %&gt;。
</p>
<pre class="dig3 text">
# hello.jsp

<span class="red">&lt;%@ page contentType="text/html; charset=EUC-JP" %&gt;</span>
&lt;html&gt;
&lt;head&gt;
&lt;title&gt;HelloWorld&lt;/title&gt;
&lt;/head&gt;
&lt;body&gt;
&lt;p&gt;
&lt;%
    out.println("こんにちは世界");
%&gt;
&lt;/p&gt;
&lt;/body&gt;
&lt;/html&gt;
</pre>

<p class="dig1"><span class="title2">静的コンテンツ（HTML）の場合</span></p>
<p class="dig2">
<span class="bold">workers2.propeties</span>ファイルで、コンテキストパス以下のすべてのファイルをTomcatに渡すよう設定している場合、静的コンテンツについてもこのページの先頭に書いたようなキャラクタセット情報が返される。
HTMLのmetaタグでcharsetを指定しても無視されるので、ドキュメントルートとコンテキストパスで異なるキャラクタセットを使用したいときは注意が必要。
</p>
<pre class="dig3 text">
# workers2.properties

[uri:/hoge/*]    <span class="blue">&lt;---すべてのファイルをTomcatに処理させる</span>
</pre>
<p class="dig2">
通常は<span class="bold">SetCharacterEncodingFilter</span>を利用するのが常套手段のようだが、どうやってもcharsetを返してくれない。
しかたないので、<span class="bold">web.xml</span>の<span class="bold">&lt;mime-mapping&gt;</span>でcharsetと拡張子の関連付けを指定した。
</p>
<pre class="dig3 text">
&lt;!--（コンテキストパス）/WEB-INF/web.xml--&gt;

&lt;?xml version="1.0" encoding="ISO-8859-1"?&gt;
&lt;!DOCTYPE web-app
     PUBLIC "-//Sun Microsystems, Inc.//DTD Web Application 2.3//EN"
    "http://java.sun.com/dtd/web-app_2_3.dtd"&gt;
&lt;web-app&gt;
<span class="red">  &lt;mime-mapping&gt;
    &lt;extension&gt;html&lt;/extension&gt;
    &lt;mime-type&gt;text/html; charset=UTF-8&lt;/mime-type&gt;
  &lt;/mime-mapping&gt;</span>
&lt;/web-app&gt;
</pre>
<p class="dig2">
いちおう、<span class="bold">SetCharacterEncodingFilter</span>を利用する方法を書いておくと、$CATALINA_HOME/webapps/jsp-examples/WEB-INF/classes/filtersディレクトリにある<span class="bold">SetCharacterEncodingFilter.java</span>をコンパイルして、
</p>
<pre class="dig3 shell">
# <span class="bold">cd $CATALINA_HOME/webapps/jsp-examples/WEB-INF/classes</span>
# <span class="bold">javac -classpath .:$CATALINA_HOME/common/lib/servlet-api.jar filters.SetCharacterEncodingFilter.java</span>
</pre>
<p class="dig2">
生成されたクラスファイルを<span class="bold">（コンテキストパス）/WEB-INF/classes/filters</span>ディレクトリにコピーして、<span class="bold">（コンテキストパス）/WEB-INF/web.xml</span>にフィルタの設定を記述するだけらしい。
</p>
<pre class="dig3 text">
&lt;!--（コンテキストパス）/WEB-INF/web.xml--&gt;

&lt;?xml version="1.0" encoding="ISO-8859-1"?&gt;
&lt;!DOCTYPE web-app
     PUBLIC "-//Sun Microsystems, Inc.//DTD Web Application 2.3//EN"
    "http://java.sun.com/dtd/web-app_2_3.dtd"&gt;
&lt;web-app&gt;
  <span class="red">&lt;filter&gt;
      &lt;filter-name&gt;Set Character Encoding&lt;/filter-name&gt;
      &lt;filter-class&gt;filters.SetCharacterEncodingFilter&lt;/filter-class&gt;
      &lt;init-param&gt;
          &lt;param-name&gt;encoding&lt;/param-name&gt;
          &lt;param-value&gt;UTF-8&lt;/param-value&gt;
      &lt;/init-param&gt;
  &lt;/filter&gt;
  &lt;filter-mapping&gt;
      &lt;filter-name&gt;Set Character Encoding&lt;/filter-name&gt;
      &lt;url-pattern&gt;/*&lt;/url-pattern&gt;
  &lt;/filter-mapping&gt;</span>
&lt;/web-app&gt;
</pre>
<p class="dig2">
うむむ...
</p>

<hr/>
<p class="dig1">
[<a href="/web/**************/http://www.nina.jp/server/index-slackware.html">サーバの実験室 Slackware</a>]
</p>
</body>
</html>

<!--
     FILE ARCHIVED ON 20:29:07 Oct 03, 2018 AND RETRIEVED FROM THE
     INTERNET ARCHIVE ON 23:27:21 Mar 21, 2022.
     JAVASCRIPT APPENDED BY WAYBACK MACHINE, COPYRIGHT INTERNET ARCHIVE.

     ALL OTHER CONTENT MAY ALSO BE PROTECTED BY COPYRIGHT (17 U.S.C.
     SECTION 108(a)(3)).
-->
<!--
playback timings (ms):
  captures_list: 133.209
  exclusion.robots: 0.289
  exclusion.robots.policy: 0.278
  RedisCDXSource: 6.152
  esindex: 0.009
  LoadShardBlock: 108.809 (3)
  PetaboxLoader3.datanode: 290.581 (4)
  CDXLines.iter: 15.789 (3)
  load_resource: 242.738
  PetaboxLoader3.resolve: 57.13
-->