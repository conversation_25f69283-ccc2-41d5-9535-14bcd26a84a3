#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*) basedir=`cygpath -w "$basedir"`;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/Users/<USER>/projects/chatbi/chatbi-server/chatbi-server-agent/ui-vue3/node_modules/uuid/dist/bin/node_modules:/Users/<USER>/projects/chatbi/chatbi-server/chatbi-server-agent/ui-vue3/node_modules/uuid/dist/node_modules:/Users/<USER>/projects/chatbi/chatbi-server/chatbi-server-agent/ui-vue3/node_modules/uuid/node_modules:/Users/<USER>/projects/chatbi/chatbi-server/chatbi-server-agent/ui-vue3/node_modules:/Users/<USER>/projects/chatbi/chatbi-server/chatbi-server-agent/node_modules:/Users/<USER>/projects/chatbi/chatbi-server/node_modules:/Users/<USER>/projects/chatbi/node_modules:/Users/<USER>/projects/node_modules:/Users/<USER>/node_modules:/Users/<USER>/node_modules:/Users/<USER>/projects/chatbi/chatbi-server/chatbi-server-agent/ui-vue3/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/Users/<USER>/projects/chatbi/chatbi-server/chatbi-server-agent/ui-vue3/node_modules/uuid/dist/bin/node_modules:/Users/<USER>/projects/chatbi/chatbi-server/chatbi-server-agent/ui-vue3/node_modules/uuid/dist/node_modules:/Users/<USER>/projects/chatbi/chatbi-server/chatbi-server-agent/ui-vue3/node_modules/uuid/node_modules:/Users/<USER>/projects/chatbi/chatbi-server/chatbi-server-agent/ui-vue3/node_modules:/Users/<USER>/projects/chatbi/chatbi-server/chatbi-server-agent/node_modules:/Users/<USER>/projects/chatbi/chatbi-server/node_modules:/Users/<USER>/projects/chatbi/node_modules:/Users/<USER>/projects/node_modules:/Users/<USER>/node_modules:/Users/<USER>/node_modules:/Users/<USER>/projects/chatbi/chatbi-server/chatbi-server-agent/ui-vue3/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../uuid/dist/bin/uuid" "$@"
else
  exec node  "$basedir/../uuid/dist/bin/uuid" "$@"
fi
