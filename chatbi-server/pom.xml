<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
  xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">

    <!-- Introduce spring startup project -->
    <parent>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-parent</artifactId>
        <version>3.3.3</version>
    </parent>

    <modelVersion>4.0.0</modelVersion>
    <groupId>com.cpit.chatbi</groupId>
    <artifactId>chatbi-server-parent</artifactId>
    <packaging>pom</packaging>
    <version>${revision}</version>
    <name>chatbi-server-parent</name>

    <properties>
        <!-- The version of the current project uses flatten and only needs to modify this one place -->
        <revision>1.0.0</revision>
        <maven.compiler.target>21</maven.compiler.target>
        <maven.compiler.source>21</maven.compiler.source>
        <java.version>21</java.version>
        <sa-token.version>1.44.0</sa-token.version>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
        <postgresql.version>42.6.0</postgresql.version>
        <druid.version>1.2.21</druid.version>
        <mybatis-plus.version>*******</mybatis-plus.version>
        <spring-ai.version>1.0.0</spring-ai.version>
        <!-- skip test -->
        <maven.test.skip>true</maven.test.skip>
    </properties>

    <modules>
        <module>chatbi-server-domain</module>
        <module>chatbi-server-start</module>
        <module>chatbi-server-test</module>
        <module>chatbi-server-tools</module>
        <module>chatbi-server-web</module>
        <module>chatbi-spi</module>
        <module>chatbi-plugins</module>
<!--        <module>chatbi-server-web-start</module>-->
<!--        <module>chatbi-server-metabase</module>-->
        <module>chatbi-server-chatbi</module>
        <module>chatbi-server-agent</module>
        <module>chatbi-admin</module>
        <module>chatbi-server-rag</module>
        <module>chatbi-team</module>
    </modules>

    <dependencyManagement>
        <dependencies>

            <!-- Comes with package -->
            <dependency>
                <groupId>com.cpit.chatbi</groupId>
                <artifactId>chatbi-server-tool-metabase</artifactId>
                <version>${revision}</version>
            </dependency>
            <!-- Comes with package -->
            <dependency>
                <groupId>com.cpit.chatbi</groupId>
                <artifactId>chatbi-server-tools-base</artifactId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <groupId>com.cpit.chatbi</groupId>
                <artifactId>chatbi-server-tools-common</artifactId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <groupId>com.cpit.chatbi</groupId>
                <artifactId>chatbi-server-tools-model</artifactId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <groupId>com.cpit.chatbi</groupId>
                <artifactId>chatbi-server-web-api</artifactId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <groupId>com.cpit.chatbi</groupId>
                <artifactId>chatbi-server-admin-api</artifactId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <groupId>com.cpit.chatbi</groupId>
                <artifactId>chatbi-server-common-api</artifactId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <groupId>com.cpit.chatbi</groupId>
                <artifactId>chatbi-server-domain-api</artifactId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <groupId>com.cpit.chatbi</groupId>
                <artifactId>chatbi-server-domain-core</artifactId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <groupId>com.cpit.chatbi</groupId>
                <artifactId>chatbi-server-domain-repository</artifactId>
                <version>${revision}</version>
            </dependency>
            <!--            <dependency>-->
            <!--                <groupId>com.cpit.chatbi</groupId>-->
            <!--                <artifactId>chatbi-server-domain-support</artifactId>-->
            <!--                <version>${revision}</version>-->
            <!--            </dependency>-->
            <dependency>
                <groupId>com.cpit.chatbi</groupId>
                <artifactId>chatbi-server-start</artifactId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <groupId>com.cpit.chatbi</groupId>
                <artifactId>chatbi-admin</artifactId>
                <version>${revision}</version>
            </dependency>

            <dependency>
                <groupId>com.cpit.chatbi</groupId>
                <artifactId>chatbi-team</artifactId>
                <version>${revision}</version>
            </dependency>

            <dependency>
                <groupId>com.cpit.chatbi</groupId>
                <artifactId>chatbi-spi</artifactId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <groupId>com.cpit.chatbi</groupId>
                <artifactId>chatbi-plugins</artifactId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.commons</groupId>
                <artifactId>commons-collections4</artifactId>
                <version>4.4</version>
            </dependency>
            <dependency>
                <groupId>com.google.guava</groupId>
                <artifactId>guava</artifactId>
                <version>32.0.1-jre</version>
            </dependency>
            <dependency>
                <groupId>cn.hutool</groupId>
                <artifactId>hutool-all</artifactId>
                <version>5.8.20</version>
            </dependency>
            <dependency>
                <groupId>com.alibaba.fastjson2</groupId>
                <artifactId>fastjson2</artifactId>
                <version>2.0.37</version>
            </dependency>

            <!-- mapstruct -->
            <dependency>
                <groupId>org.mapstruct</groupId>
                <artifactId>mapstruct</artifactId>
                <version>1.5.5.Final</version>
            </dependency>
            <dependency>
                <groupId>org.mapstruct</groupId>
                <artifactId>mapstruct-processor</artifactId>
                <version>1.5.5.Final</version>
            </dependency>
            <!-- Make sure to generate lombok first and then mapstruct -->
            <dependency>
                <groupId>org.projectlombok</groupId>
                <artifactId>lombok-mapstruct-binding</artifactId>
                <version>0.2.0</version>
            </dependency>

            <!-- Comes with database -->
            <dependency>
                <groupId>com.h2database</groupId>
                <artifactId>h2</artifactId>
                <version>2.1.214</version>
            </dependency>
            <dependency>
                <groupId>org.postgresql</groupId>
                <artifactId>postgresql</artifactId>
                <version>${postgresql.version}</version>
            </dependency>

            <!-- MyBatis Plus 核心库 (统一使用手动配置) -->
            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>mybatis-plus</artifactId>
                <version>${mybatis-plus.version}</version>
            </dependency>
            <dependency>
                <groupId>com.zaxxer</groupId>
                <artifactId>HikariCP</artifactId>
                <version>5.0.1</version>
            </dependency>
            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>mybatis-plus-generator</artifactId>
                <version>${mybatis-plus.version}</version>
            </dependency>
            <dependency>
                <groupId>org.freemarker</groupId>
                <artifactId>freemarker</artifactId>
                <version>2.3.32</version>
            </dependency>

            <!-- druid -->
            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>druid</artifactId>
                <version>1.2.18</version>
            </dependency>
            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>druid-spring-boot-starter</artifactId>
                <version>${druid.version}</version>
            </dependency>

            <!-- Sa-Token Permission authentication, online documentation:https://sa-token.cc -->
            <dependency>
                <groupId>cn.dev33</groupId>
                <artifactId>sa-token-spring-boot3-starter</artifactId>
                <version>${sa-token.version}</version>
            </dependency>

            <dependency>
                <groupId>cn.dev33</groupId>
                <artifactId>sa-token-redis-jackson</artifactId>
                <version>${sa-token.version}</version>
            </dependency>
            <!-- Sa-Token matching jwt -->
            <dependency>
                <groupId>cn.dev33</groupId>
                <artifactId>sa-token-jwt</artifactId>
                <version>${sa-token.version}</version>
                <exclusions>
                    <exclusion>
                        <artifactId>hutool-jwt</artifactId>
                        <groupId>cn.hutool</groupId>
                    </exclusion>
                </exclusions>
            </dependency>

            <!-- http -->
            <dependency>
                <groupId>com.dtflys.forest</groupId>
                <artifactId>forest-spring</artifactId>
                <version>1.5.32</version>
            </dependency>
            <dependency>
                <groupId>com.dtflys.forest</groupId>
                <artifactId>forest-core</artifactId>
                <version>1.5.32</version>
            </dependency>

            <!-- Database version management -->

            <dependency>
                <groupId>com.unfbx</groupId>
                <artifactId>chatgpt-java</artifactId>
                <version>1.0.8</version>
                <exclusions>
                    <exclusion>
                        <groupId>org.slf4j</groupId>
                        <artifactId>slf4j-simple</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.theokanning.openai-gpt3-java</groupId>
                <artifactId>service</artifactId>
                <version>0.12.0</version>
            </dependency>

            <!-- https://mvnrepository.com/artifact/org.zalando/logbook-spring-boot-starter -->
            <dependency>
                <groupId>org.zalando</groupId>
                <artifactId>logbook-spring-boot-starter</artifactId>
                <version>3.3.0</version>
            </dependency>

            <!-- https://mvnrepository.com/artifact/org.springframework/spring-context-indexer -->
            <dependency>
                <groupId>org.springframework</groupId>
                <artifactId>spring-context-indexer</artifactId>
                <version>6.0.10</version>
                <scope>optional</scope>
            </dependency>
            <dependency>
                <groupId>commons-beanutils</groupId>
                <artifactId>commons-beanutils</artifactId>
                <version>1.9.4</version>
            </dependency>
            <dependency>
                <groupId>org.ehcache</groupId>
                <artifactId>ehcache</artifactId>
                <version>3.10.8</version>
            </dependency>
            <dependency>
                <groupId>javax.cache</groupId>
                <artifactId>cache-api</artifactId>
                <version>1.1.1</version>
            </dependency>
            <dependency>
                <groupId>commons-io</groupId>
                <artifactId>commons-io</artifactId>
                <version>2.7</version>
            </dependency>
            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>easyexcel</artifactId>
                <version>3.3.2</version>
            </dependency>

            <!--poi-tl-->
            <dependency>
                <groupId>com.deepoove</groupId>
                <artifactId>poi-tl</artifactId>
                <version>1.10.5</version>
            </dependency>
            <!--pdf-->
            <dependency>
                <groupId>com.itextpdf</groupId>
                <artifactId>itext-asian</artifactId>
                <version>5.2.0</version>
            </dependency>
            <dependency>
                <groupId>com.itextpdf</groupId>
                <artifactId>itextpdf</artifactId>
                <version>5.5.13</version>
            </dependency>

            <!--    pdf analysis    -->
            <dependency>
                <groupId>org.apache.pdfbox</groupId>
                <artifactId>pdfbox</artifactId>
                <version>2.0.24</version>
            </dependency>
            <!-- https://mvnrepository.com/artifact/com.auth0/java-jwt -->
            <dependency>
                <groupId>com.auth0</groupId>
                <artifactId>java-jwt</artifactId>
                <version>4.4.0</version>
            </dependency>

            <dependency>
                <groupId>com.github.vertical-blank</groupId>
                <artifactId>sql-formatter</artifactId>
                <version>2.0.4</version>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <dependencies>
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <scope>provided</scope>
        </dependency>
        <!-- Make sure to generate lombok first and then mapstruct -->
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok-mapstruct-binding</artifactId>
            <scope>provided</scope>
        </dependency>
    </dependencies>


    <build>
        <plugins>
            <!-- compile -->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>3.11.0</version>
                <configuration>
                    <!-- Configure mapstruct to disable builder -->
                    <compilerArgs>
                        <arg>
                            -Amapstruct.disableBuilders=true
                        </arg>
                    </compilerArgs>
                </configuration>
            </plugin>

            <!-- Support maven revision to configure the system version -->
            <plugin>
                <groupId>org.codehaus.mojo</groupId>
                <artifactId>flatten-maven-plugin</artifactId>
                <version>1.5.0</version>
                <configuration>
                    <updatePomFile>true</updatePomFile>
                    <flattenMode>oss</flattenMode>
                </configuration>
                <executions>
                    <execution>
                        <id>flatten</id>
                        <phase>process-resources</phase>
                        <goals>
                            <goal>flatten</goal>
                        </goals>
                    </execution>
                    <execution>
                        <id>flatten.clean</id>
                        <phase>clean</phase>
                        <goals>
                            <goal>clean</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>

            <!-- 强制依赖解析插件 - 解决MapStruct类路径冲突 -->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-dependency-plugin</artifactId>
                <version>3.6.0</version>
                <executions>
                    <execution>
                        <id>analyze-dependencies</id>
                        <goals>
                            <goal>analyze-only</goal>
                        </goals>
                        <configuration>
                            <failOnWarning>false</failOnWarning>
                            <ignoreNonCompile>true</ignoreNonCompile>
                        </configuration>
                    </execution>
                </executions>
            </plugin>

            <!-- Run test case -->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-surefire-plugin</artifactId>
                <version>3.1.2</version>
                <configuration>
                    <includes>
                        <include>/com/cpit/chatbi/server/test/**/*.java</include>
                    </includes>
                    <excludes>
                        <include>/com/cpit/chatbi/server/test/temp/**/*.java</include>
                    </excludes>
                    <testFailureIgnore>true</testFailureIgnore>
                </configuration>
            </plugin>
        </plugins>
    </build>
</project>
