# port
server:
  port: 10821

#chat2db:
chatbi:
  gateway:
    base-url: http://test.sqlgpt.cn/gateway
    model-base-url: http://test.sqlgpt.cn/gateway
management:
  endpoints:
    web:
      exposure:
        include: startup

spring:
  datasource:
    type: com.alibaba.druid.pool.DruidDataSource
    driver-class-name: org.postgresql.Driver
    url: *************************************************************** # 数据库URL
    username: postgres
    password: 123456
    druid:
      initial-size: 5
      min-idle: 5
      max-active: 20
      max-wait: 60000
      time-between-eviction-runs-millis: 60000
      min-evictable-idle-time-millis: 300000
      validation-query: SELECT 1
      test-while-idle: true
      test-on-borrow: false
      test-on-return: false
      pool-prepared-statements: true
      max-pool-prepared-statement-per-connection-size: 20
      filters: stat,wall
      connection-properties: druid.stat.mergeSql=true;druid.stat.slowSqlMillis=5000

  mvc:
    throw-exception-if-no-handler-found: true
  web:
    resources:
      add-mappings: false
  ai:
    openai:
      api-key: xxx