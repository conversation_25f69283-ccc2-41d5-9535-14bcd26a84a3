package com.cpit.chatbi.metabase.meta.core;


import com.cpit.chatbi.metabase.domain.er.*;
import com.cpit.chatbi.metabase.meta.TestApplication;
import com.cpit.chatbi.metabase.meta.schema.*;
import com.cpit.chatbi.metabase.util.PrintUtils;
import com.alibaba.fastjson.JSON;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.dao.DataAccessException;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Set;

@SpringBootTest(classes = TestApplication.class)  // 修改这行
public class MetaLoaderTest {

    @Autowired
    private MetaLoader metaLoader;
    
    @Test
    public void getTableNames() {
        Set<String> tableNames = metaLoader.getTableNames();

        System.out.println(tableNames);
    }

    @Test
//  
    public void getTable() {
        String tableName = "dtp_hospital";// Oracle:"PUMP",mySql:"person_info":sql
                                                // server:"Dataset"
        Table table = metaLoader.getTable(tableName);
        
        String result= PrintUtils.getTableInfo(table);
        System.out.println(result);
    }

    @Test
    public void getTableInfo() {
        String tableName = "orders";
        Table table = metaLoader.getTable(tableName, SchemaInfoLevel.max());
        System.out.println(JSON.toJSONString(table));
    }


    @Test
    public void getTableInfoList() {
        long startTime = System.currentTimeMillis();
        
        // 获取所有表名
        Set<String> tableNames = metaLoader.getTableNames();
        System.out.println("Available tables: " + tableNames);

        ErDiagram erDiagram = new ErDiagram();
        List<TableInfo> tables = new ArrayList<>();
        List<Edge> edges = new ArrayList<>();

        // 批量获取所有表的信息
        Schema schema = metaLoader.getSchema(SchemaInfoLevel.max());
        Map<String, Table> allTables = schema.getTables();

        // 处理所有表
        for (Map.Entry<String, Table> tableEntry : allTables.entrySet()) {
            Table table = tableEntry.getValue();
            
            // 构建表信息
            TableInfo tableInfo = new TableInfo();
            
            TableData data = new TableData();
            List<ColumnInfo> columns = new ArrayList<>();
            
            // 处理列信息
            for (Map.Entry<String, Column> columnEntry : table.getColumns().entrySet()) {
                Column column = columnEntry.getValue();
                ColumnInfo columnInfo = new ColumnInfo();
                columnInfo.setName(column.getName());
                columnInfo.setColumnType(column.getTypeName().toUpperCase() + 
                    (column.getLength() > 0 ? "(" + column.getLength() + ")" : ""));
                columnInfo.setPrimaryKey(table.getPrimaryKey() != null && 
                    table.getPrimaryKey().getColumns().contains(column.getName()));
                columnInfo.setComment(""); // 如果有列注释可以在这里添加
                columns.add(columnInfo);
            }
            
            data.setColumns(columns);
            tableInfo.setData(data);
            tables.add(tableInfo);
            
            // 处理外键关系
            if (table.getForeignkeys() != null) {
                for (Map.Entry<String, ForeignKey> fkEntry : table.getForeignkeys().entrySet()) {
                    ForeignKey fk = fkEntry.getValue();
                    for (ForeignKeyColumnReference ref : fk.getColumnReferences()) {
                        Edge edge = new Edge();
                        edge.setSource(table.getName());
                        edge.setTarget(ref.getPrimaryColumn().getTable());
                        edge.setSourceHandle(ref.getForeignColumn().getColumn());
                        edge.setTargetHandle(ref.getPrimaryColumn().getColumn());
                        edges.add(edge);
                    }
                }
            }
        }
        
        erDiagram.setTables(tables);
        erDiagram.setEdges(edges);
        
        // 根据edges关系排序tables
        erDiagram.sortTablesByEdges();
        
        System.out.println(JSON.toJSONString(erDiagram, true));
        
        long endTime = System.currentTimeMillis();
        System.out.println("耗时：" + (endTime - startTime) + "ms");
    }



    @Test
    public void getTableLevel() {
        long startTime = System.currentTimeMillis();
        String tableName = "environment";// Oracle:"PUMP",mySql:"person_info":sql
                                        // server:"Dataset"
        Table table = metaLoader.getTable(tableName, SchemaInfoLevel.max());
        System.out.println(table);

        long endTime = System.currentTimeMillis();
        System.out.println("耗时：" + (endTime - startTime));
    }

    @Test
    public void getSchemaInfos() {
        long startTime = System.currentTimeMillis();

        Set<SchemaInfo> schemaInfos = metaLoader.getSchemaInfos();
        System.out.println(schemaInfos);
        long endTime = System.currentTimeMillis();
        System.out.println("耗时：" + (endTime - startTime));
    }

    @Test
    public void getSchema() {
        Schema schema = metaLoader.getSchema(SchemaInfoLevel.max());
        System.out.println(JSON.toJSONString(schema));
    }

    @Test
    public void getDatabase() {
        @SuppressWarnings("deprecation")
        Database database = metaLoader.getDatabase(SchemaInfoLevel.min());
        System.out.println(database);
    }

    @Test
    public void getSchemas() {
        Set<SchemaInfo> schemas = metaLoader.getSchemaInfos();
        int num = 0;
        int schemaNum=0;
        try {
            for (SchemaInfo schemaInfo : schemas) {
                if(schemaInfo.getSchemaName()!=null&&schemaInfo.getSchemaName().equals("XDB")){
                    continue;
                }
                Schema s = metaLoader.getSchema(schemaInfo);
                num += s.getTables().size();
                schemaNum++;
//              System.out.println(s);
//              System.out.println(num);
            }
        } catch (DataAccessException e) {
            e.printStackTrace();
            System.out.println(num);
            System.out.println(schemaNum);
        }
    }
    
    @Test
    public void getProcedureNames(){
        Set<String> procedureNames=metaLoader.getProcedureNames();
        System.out.println(procedureNames);
    }
    
    @Test
    public void getProcedure(){
        Procedure p=metaLoader.getProcedure("dt_generateansiname");
        System.out.println(p);
    }
    
    @Test
    public void getProcedures(){
        Map<String, Procedure> ps=metaLoader.getProcedures();
        System.out.println(ps);
        System.out.println(ps.size());
    }
    
    @Test
    public void getTriggerNames(){
        Set<String> names=metaLoader.getTriggerNames();
        System.out.println(names);
    }
    
    @Test
    public void getTrigger(){
        Trigger t=metaLoader.getTrigger("trigger_test");
        System.out.println(t);
    }
    
    @Test
    public void getTriggers(){
        Map<String, Trigger> ts=metaLoader.getTriggers();
        System.out.println(ts);
    }
    
    
    @Test
    public void getFunctionNames(){
        Set<String>  names=metaLoader.getFunctionNames();
        System.out.println(names);
    }
    
    @Test
    public void getFunction(){
        Function functio=metaLoader.getFunction("Functontest");
        System.out.println(functio);
    }
    
    @Test
    public void getFunctions(){
        Map<String, Function> functions=metaLoader.getFunctions();
        System.out.println(functions);
    }

    @Test
    public void executeDDL(){
        String sql = "CREATE TABLE chart (\n" +
                "\tid bigserial NOT NULL, -- 主键\n" +
                "\tgmt_create timestamp DEFAULT CURRENT_TIMESTAMP NOT NULL, -- 创建时间\n" +
                "\tgmt_modified timestamp DEFAULT CURRENT_TIMESTAMP NOT NULL, -- 修改时间\n" +
                "\tname varchar(128) NULL, -- 图表名称\n" +
                "\tdescription varchar(128) NULL, -- 图表描述\n" +
                "\t\"schema\" text NULL, -- 图表信息\n" +
                "\tdata_source_id int8 NULL, -- 数据源连接ID\n" +
                "\t\"type\" varchar(32) NULL, -- 数据库类型\n" +
                "\tdatabase_name varchar(128) NULL, -- db名称\n" +
                "\tddl text NULL, -- ddl内容\n" +
                "\tdeleted text NULL, -- 是否被删除,y表示删除,n表示未删除\n" +
                "\tuser_id int8 DEFAULT 1 NOT NULL, -- 用户id\n" +
                "\tschema_name varchar(128) NULL, -- schema名称\n" +
                "\tCONSTRAINT chart_pkey PRIMARY KEY (id)\n" +
                ");\n" +
                "COMMENT ON TABLE chart IS '自定义报表表';\n" +
                "\n" +
                "-- Column comments\n" +
                "\n" +
                "COMMENT ON COLUMN chart.id IS '主键';\n" +
                "COMMENT ON COLUMN chart.gmt_create IS '创建时间';\n" +
                "COMMENT ON COLUMN chart.gmt_modified IS '修改时间';\n" +
                "COMMENT ON COLUMN chart.name IS '图表名称';\n" +
                "COMMENT ON COLUMN chart.description IS '图表描述';\n" +
                "COMMENT ON COLUMN chart.\"schema\" IS '图表信息';\n" +
                "COMMENT ON COLUMN chart.data_source_id IS '数据源连接ID';\n" +
                "COMMENT ON COLUMN chart.\"type\" IS '数据库类型';\n" +
                "COMMENT ON COLUMN chart.database_name IS 'db名称';\n" +
                "COMMENT ON COLUMN chart.ddl IS 'ddl内容';\n" +
                "COMMENT ON COLUMN chart.deleted IS '是否被删除,y表示删除,n表示未删除';\n" +
                "COMMENT ON COLUMN chart.user_id IS '用户id';\n" +
                "COMMENT ON COLUMN chart.schema_name IS 'schema名称';";
        metaLoader.executeDDL(sql);
    }
}
