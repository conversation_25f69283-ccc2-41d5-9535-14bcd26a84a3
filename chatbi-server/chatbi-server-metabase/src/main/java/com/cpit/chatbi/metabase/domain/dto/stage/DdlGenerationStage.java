package com.cpit.chatbi.metabase.domain.dto.stage;

import com.cpit.chatbi.metabase.domain.chat.DdlTaskResponse;
import reactor.core.publisher.FluxSink;
import reactor.core.publisher.Mono;

public interface DdlGenerationStage {
    /**
     * Processes a single stage of the DDL generation.
     * Handles its own streaming to the sink and returns the final result.
     * @param context Shared context containing system prompt, chat ID, previous output, etc.
     * @param sink The FluxSink to send DdlTaskResponse events to the client.
     * @return A Mono emitting the StageResult (final output and continue flag) when the stage is complete.
     */
    Mono<StageResult> process(StageContext context, FluxSink<DdlTaskResponse> sink);

    /**
     * Gets the unique name of this stage (e.g., "analysis", "generation").
     * @return Stage name.
     */
    String getStageName();

    /**
     * Gets a human-readable description for this stage (e.g., "分析需求").
     * @return Stage description.
     */
    String getStageDescription();
}