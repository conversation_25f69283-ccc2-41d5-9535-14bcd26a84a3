package com.cpit.chatbi.metabase.domain.er;

import com.alibaba.fastjson.annotation.JSONField;

public class Edge {
    @J<PERSON><PERSON>ield(ordinal = 1)
    private String source;
    
    @J<PERSON><PERSON>ield(ordinal = 2)
    private String target;
    
    @J<PERSON><PERSON>ield(ordinal = 3)
    private String sourceHandle;
    
    @J<PERSON><PERSON>ield(ordinal = 4)
    private String targetHandle;

    public String getSource() {
        return source;
    }

    public void setSource(String source) {
        this.source = source;
    }

    public String getTarget() {
        return target;
    }

    public void setTarget(String target) {
        this.target = target;
    }

    public String getSourceHandle() {
        return sourceHandle;
    }

    public void setSourceHandle(String sourceHandle) {
        this.sourceHandle = sourceHandle;
    }

    public String getTargetHandle() {
        return targetHandle;
    }

    public void setTargetHandle(String targetHandle) {
        this.targetHandle = targetHandle;
    }
} 