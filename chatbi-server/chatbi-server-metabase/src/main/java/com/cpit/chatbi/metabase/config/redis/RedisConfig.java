package com.cpit.chatbi.metabase.config.redis;

import com.cpit.chatbi.metabase.config.redis.serializer.MessageRedisSerializer;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import org.springframework.ai.chat.messages.Message;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.redis.connection.RedisConnectionFactory;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.serializer.StringRedisSerializer;
import org.springframework.http.converter.json.Jackson2ObjectMapperBuilder;

@Configuration
@ConditionalOnProperty(
        prefix = "spring.data.redis",
        name = {"host"},  // 必须有 host 配置才生效
        matchIfMissing = false  // 默认就是 false，显式写出更清晰
)
public class RedisConfig {

/*    @Bean("messageRedisTemplate")
    public RedisTemplate<String, Object> redisTemplate(RedisConnectionFactory factory) {
        RedisTemplate<String, Object> template = new RedisTemplate<>();
        template.setConnectionFactory(factory);  // 绑定Redis连接工厂

        ObjectMapper objectMapper = new ObjectMapper();
        // 启用多态类型支持，匹配Redis存储的WRAPPER_ARRAY格式
        objectMapper.activateDefaultTyping(
                LaissezFaireSubTypeValidator.instance,  // 禁用子类型验证
                ObjectMapper.DefaultTyping.EVERYTHING,  // 对所有类型启用类型信息
                JsonTypeInfo.As.WRAPPER_ARRAY  // 使用数组包装类型信息
        );
//        SimpleModule module = new SimpleModule();
//        module.addDeserializer(Message.class, new MessageDeserializer());
//        objectMapper.registerModule(module);
        // 全局配置：忽略未知字段（关键修复点）
        objectMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        // 注册自定义消息类型的Mixin（解决构造函数匹配问题）
//        objectMapper.addMixIn(UserMessage.class, UserMessageMixin.class);
//        objectMapper.addMixIn(AssistantMessage.class, AssistantMessageMixin.class);

        Jackson2JsonRedisSerializer<Object> serializer =
                new Jackson2JsonRedisSerializer<>(objectMapper, Object.class);  // 创建定制化序列化器

        template.setKeySerializer(new StringRedisSerializer());  // 字符串序列化键
        template.setValueSerializer(serializer);  // JSON序列化值
        return template;
    }*/

    @Bean("redisTemplate")
    public RedisTemplate<String, String> redisTemplate(RedisConnectionFactory factory) {
        RedisTemplate<String, String> template = new RedisTemplate<>();
        template.setConnectionFactory(factory);
        template.setKeySerializer(new StringRedisSerializer());
        template.setValueSerializer(new StringRedisSerializer()); // 直接存储字符串
        template.setHashKeySerializer(new StringRedisSerializer());
        template.setHashValueSerializer(new StringRedisSerializer());
        template.afterPropertiesSet();
        return template;
    }



    @Bean("messageRedisTemplate")
    public RedisTemplate<String, Message> messageRedisTemplate(RedisConnectionFactory factory, Jackson2ObjectMapperBuilder builder) {
        RedisTemplate<String, Message> template = new RedisTemplate<>();
        template.setConnectionFactory(factory);

        // 使用String序列化器作为key的序列化方式
        template.setKeySerializer(new StringRedisSerializer());
        // 使用自定义的Message序列化器作为value的序列化方式
        template.setValueSerializer(new MessageRedisSerializer(builder.build()));

        // 设置hash类型的key和value序列化方式
        template.setHashKeySerializer(new StringRedisSerializer());
        template.setHashValueSerializer(new MessageRedisSerializer(builder.build()));

        template.afterPropertiesSet();
        return template;
    }

    
    @Bean
    public ObjectMapper objectMapper() {
        return new ObjectMapper().registerModule(new JavaTimeModule());
    }


}
