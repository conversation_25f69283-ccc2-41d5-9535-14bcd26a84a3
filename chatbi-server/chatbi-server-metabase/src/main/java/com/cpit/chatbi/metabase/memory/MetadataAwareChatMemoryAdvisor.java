package com.cpit.chatbi.metabase.memory;

import org.springframework.ai.chat.client.advisor.AbstractChatMemoryAdvisor;
import org.springframework.ai.chat.client.advisor.MessageChatMemoryAdvisor;
import org.springframework.ai.chat.client.advisor.api.*;
import org.springframework.ai.chat.memory.ChatMemory;
import org.springframework.ai.chat.messages.AssistantMessage;
import org.springframework.ai.chat.messages.Message;
import org.springframework.ai.chat.messages.UserMessage;
import org.springframework.ai.chat.model.ChatResponse;
import org.springframework.ai.chat.model.Generation;
import org.springframework.ai.chat.model.MessageAggregator;
import reactor.core.publisher.Flux;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

public class MetadataAwareChatMemoryAdvisor extends AbstractChatMemoryAdvisor<ChatMemory> {

    public MetadataAwareChatMemoryAdvisor(ChatMemory chatMemory) {
        super(chatMemory);
    }

    public MetadataAwareChatMemoryAdvisor(ChatMemory chatMemory, String defaultConversationId, int chatHistoryWindowSize) {
        this(chatMemory, defaultConversationId, chatHistoryWindowSize, Advisor.DEFAULT_CHAT_MEMORY_PRECEDENCE_ORDER);
    }

    public MetadataAwareChatMemoryAdvisor(ChatMemory chatMemory, String defaultConversationId, int chatHistoryWindowSize,
                                    int order) {
        super(chatMemory, defaultConversationId, chatHistoryWindowSize, true, order);
    }

    public static MetadataAwareChatMemoryAdvisor.Builder builder(ChatMemory chatMemory) {
        return new MetadataAwareChatMemoryAdvisor.Builder(chatMemory);
    }

    @Override
    public AdvisedResponse aroundCall(AdvisedRequest advisedRequest, CallAroundAdvisorChain chain) {

        advisedRequest = this.before(advisedRequest);

        AdvisedResponse advisedResponse = chain.nextAroundCall(advisedRequest);

        this.observeAfter(advisedResponse);

        return advisedResponse;
    }

    @Override
    public Flux<AdvisedResponse> aroundStream(AdvisedRequest advisedRequest, StreamAroundAdvisorChain chain) {

        Flux<AdvisedResponse> advisedResponses = this.doNextWithProtectFromBlockingBefore(advisedRequest, chain,
                this::before);

        return new MessageAggregator().aggregateAdvisedResponse(advisedResponses, this::observeAfter);
    }

    private AdvisedRequest before(AdvisedRequest request) {

        String conversationId = this.doGetConversationId(request.adviseContext());

        int chatMemoryRetrieveSize = this.doGetChatMemoryRetrieveSize(request.adviseContext());

        // 1. Retrieve the chat memory for the current conversation.
        List<Message> memoryMessages = this.getChatMemoryStore().get(conversationId, chatMemoryRetrieveSize);

        // 2. Advise the request messages list.
        List<Message> advisedMessages = new ArrayList<>(request.messages());
        advisedMessages.addAll(memoryMessages);

        // 3. Create a new request with the advised messages.
        AdvisedRequest advisedRequest = AdvisedRequest.from(request).messages(advisedMessages).build();

        // 4. Add the new user input to the conversation memory.
        UserMessage userMessage = new UserMessage(request.userText(), request.media(), request.adviseContext());
        this.getChatMemoryStore().add(this.doGetConversationId(request.adviseContext()), userMessage);

        return advisedRequest;
    }

    private void observeAfter(AdvisedResponse advisedResponse) {

        String conversationId = this.doGetConversationId(advisedResponse.adviseContext());
        // 1. 获取请求时的上下文元数据 (包含 questionId, userId 等)
        Map<String, Object> contextMetadata = advisedResponse.adviseContext();
        ChatResponse chatResponse = advisedResponse.response();

        if (chatResponse == null || chatResponse.getResults() == null) {
            return;
        }

        // 2. 遍历原始 Generation 结果
        List<Message> assistantMessagesWithContextMetadata = chatResponse.getResults()
                .stream()
                .map(Generation::getOutput) // 获取原始的 AssistantMessage
                .map(originalAssistantMessage -> {
                    // 3. 创建新的合并元数据 Map
                    Map<String, Object> combinedMetadata = new HashMap<>();

                    // 3a. 首先复制【原始】元数据 (保留 role, finishReason, id 等)
                    combinedMetadata.putAll(originalAssistantMessage.getMetadata());

                    // 3b. 然后合并【请求上下文】的元数据 (添加 questionId, userId 等)
                    combinedMetadata.putAll(contextMetadata);

                    // 4. 创建【新的】AssistantMessage 实例，使用合并后的元数据
                    return new AssistantMessage(
                            originalAssistantMessage.getText(),        // 原始内容
                            combinedMetadata,                             // 合并后的元数据
                            originalAssistantMessage.getToolCalls()       // 原始工具调用
                    );
                })
                .collect(Collectors.toList()); // 收集为 List<Message>

        // 5. 将这些【新的】、带有合并元数据的 AssistantMessage 存入内存
        if (!assistantMessagesWithContextMetadata.isEmpty()) {
            this.getChatMemoryStore().add(conversationId, assistantMessagesWithContextMetadata);
        }
    }

    public static class Builder extends AbstractChatMemoryAdvisor.AbstractBuilder<ChatMemory> {

        protected Builder(ChatMemory chatMemory) {
            super(chatMemory);
        }

        // 返回类型和构造函数调用需要是你自定义的 Advisor
        @Override
        public MetadataAwareChatMemoryAdvisor build() { // 返回类型是 MetadataAwareChatMemoryAdvisor
            return new MetadataAwareChatMemoryAdvisor( // 调用正确的构造函数
                    this.chatMemory,
                    this.conversationId,
                    this.chatMemoryRetrieveSize,
                    this.order
            );
        }
    }
}