package com.cpit.chatbi.metabase.controller;

import cn.hutool.core.lang.UUID;
import com.cpit.chatbi.metabase.domain.chat.ChatSession;
import com.cpit.chatbi.metabase.memory.ExtendedChatMemory;
import com.cpit.chatbi.metabase.meta.schema.Schema;
import com.cpit.chatbi.metabase.prompt.SqlAssistantPrompt;
import com.cpit.chatbi.metabase.service.MetabaseService;
import com.cpit.chatbi.metabase.service.stage.MultiStageDdlService;
import com.cpit.chatbi.metabase.toolcall.table.method.TableInfoTools;
import com.cpit.chatbi.metabase.util.Result;
import com.cpit.chatbi.metabase.util.ResultUtil;
import cn.hutool.core.lang.Assert;
import io.swagger.v3.oas.annotations.Operation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.ai.chat.client.ChatClient;
import org.springframework.ai.chat.messages.Message;
import org.springframework.ai.chat.model.ChatResponse;
import org.springframework.ai.chat.model.Generation;
import com.cpit.chatbi.metabase.domain.chat.DdlTaskResponse;
import com.cpit.chatbi.metabase.domain.chat.ChatResponseWrapper;
import reactor.core.publisher.Flux;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;
import com.cpit.chatbi.metabase.ai.utils.ChatResponseUtils;

import java.io.IOException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


import static org.springframework.ai.chat.client.advisor.AbstractChatMemoryAdvisor.CHAT_MEMORY_CONVERSATION_ID_KEY;
import static org.springframework.ai.chat.client.advisor.AbstractChatMemoryAdvisor.CHAT_MEMORY_RETRIEVE_SIZE_KEY;

@Slf4j
@RestController
@RequestMapping("/api/chat")
public class DdlChatController {

    @Autowired
    private ChatClient chatClient;
    @Autowired
    private ExtendedChatMemory chatMemory;
    @Autowired
    private MetabaseService metabaseService;
    @Autowired
    private SqlAssistantPrompt sqlAssistantPrompt;
    @Autowired
    private  MultiStageDdlService multiStageDdlService;


    @Operation(summary = "ai对话操作数据库DDL")
    @GetMapping(value = "/stream/ddl", produces = MediaType.TEXT_EVENT_STREAM_VALUE)
    public Flux<ChatResponse> generateDdl(
            @RequestParam(value = "message") String message,
            @RequestParam(value = "chatId") String chatId,
            @RequestParam(value = "isNewChat", defaultValue = "false") boolean isNewChat,
            @RequestParam(value = "userId", required = false) String userId,
            @RequestParam(value = "questionId", required = false) String questionId) {

        Assert.notNull(message, "message不能为空");
        Assert.notNull(chatId, "chatId不能为空");

        // 如果前端没有提供requestId，则生成一个
        final String finalRequestId = (questionId != null && !questionId.trim().isEmpty())
                ? questionId
                : UUID.randomUUID().toString();

        // 获取用户ID（优先使用入参，否则从服务中获取）
        String finalUserId = (userId != null && !userId.trim().isEmpty())
                ? userId
                : metabaseService.getCurrentUserId();

        // 只有在新建会话时才保存会话信息
        if (isNewChat) {
            ChatSession chatSession = new ChatSession()
                    .setChatId(chatId)
                    .setChatName(message.length() >= 15 ? message.substring(0, 15) : message)
                    .initCreateTime(); // 初始化创建时间为当前时间
            chatMemory.saveSession(chatSession, finalUserId);
        }

        // --- Prepare System Prompt Content ---
        String systemPrompt;
        try {
            // 2. Get the dynamic database context
            Schema schema = metabaseService.getSchemaWithDatabase(); // Get schema info
            systemPrompt = sqlAssistantPrompt.getSystemInstructionPrompt(schema); // Render DB info using the template
        } catch (IOException e) {
            log.error("Failed to load prompts or render DB info for session {}", chatId, e);
            // Return an error stream to the client
            return Flux.error(new RuntimeException("无法加载必要的提示信息或数据库结构，请稍后重试或联系管理员。", e));
        } catch (Exception e) {
            log.error("Unexpected error getting schema or prompts for session {}", chatId, e);
            return Flux.error(new RuntimeException("获取会话所需信息时发生内部错误。", e));
        }

        // 3. Combine static instructions and dynamic context for the final system message
        // Add clear separation between the static part and the dynamic part

        log.info("Final System Content for chat {}:\n{}", chatId, systemPrompt); // Log system content
        log.debug("User Message for chat {}: {}", chatId, message); // Log user message

        Flux<ChatResponse> responseFlux = chatClient.prompt()
                // Set the combined system content
                .system(systemSpec -> systemSpec.text(systemPrompt))
                // Set the original user message
                .user(userSpec -> userSpec.text(message))
                .advisors(advisorSpec -> advisorSpec
                        .param(CHAT_MEMORY_CONVERSATION_ID_KEY, chatId)
                        .param(CHAT_MEMORY_RETRIEVE_SIZE_KEY, 100)
                        .param("questionId", finalRequestId)  // 添加requestId
                        .param("timestamp", System.currentTimeMillis())
                        .param("userId", finalUserId)        // 添加userId
                        .param("chatId", chatId))      // 添加sessionId
                .stream()
                .chatResponse();

        responseFlux = getChatResponseFlux(responseFlux);
        return responseFlux;
    }


    @Operation(summary = "ai对话操作数据库DDL")
    @GetMapping(value = "/stream/ddl3", produces = MediaType.TEXT_EVENT_STREAM_VALUE)
    public Flux<ChatResponse> generateDdl3(
            @RequestParam(value = "message") String message,
            @RequestParam(value = "chatId") String chatId,
            @RequestParam(value = "isNewChat", defaultValue = "false") boolean isNewChat,
            @RequestParam(value = "userId", required = false) String userId,
            @RequestParam(value = "questionId", required = false) String questionId) {

        Assert.notNull(message, "message不能为空");
        Assert.notNull(chatId, "chatId不能为空");

        // 如果前端没有提供requestId，则生成一个
        final String finalRequestId = (questionId != null && !questionId.trim().isEmpty())
                ? questionId
                : UUID.randomUUID().toString();

        // 获取用户ID（优先使用入参，否则从服务中获取）
        String finalUserId = (userId != null && !userId.trim().isEmpty())
                ? userId
                : metabaseService.getCurrentUserId();

        // 只有在新建会话时才保存会话信息
        if (isNewChat) {
            ChatSession chatSession = new ChatSession()
                    .setChatId(chatId)
                    .setChatName(message.length() >= 15 ? message.substring(0, 15) : message)
                    .initCreateTime(); // 初始化创建时间为当前时间
            chatMemory.saveSession(chatSession, finalUserId);
        }

        // --- Prepare System Prompt Content ---
        String systemPrompt;
        try {
            // 2. Get the dynamic database context
            Schema schema = metabaseService.getSchemaWithDatabase(); // Get schema info
            systemPrompt = sqlAssistantPrompt.getSystemInstructionPromptOnlyTable(schema); // Render DB info using the template
        } catch (IOException e) {
            log.error("Failed to load prompts or render DB info for session {}", chatId, e);
            // Return an error stream to the client
            return Flux.error(new RuntimeException("无法加载必要的提示信息或数据库结构，请稍后重试或联系管理员。", e));
        } catch (Exception e) {
            log.error("Unexpected error getting schema or prompts for session {}", chatId, e);
            return Flux.error(new RuntimeException("获取会话所需信息时发生内部错误。", e));
        }

        // 3. Combine static instructions and dynamic context for the final system message
        // Add clear separation between the static part and the dynamic part

        log.info("Final System Content for chat {}:\n{}", chatId, systemPrompt); // Log system content
        log.debug("User Message for chat {}: {}", chatId, message); // Log user message
        TableInfoTools tableInfoTools = new TableInfoTools(metabaseService); // 实例化 Tool
        Flux<ChatResponse> responseFlux = chatClient.prompt()
                // Set the combined system content
                .system(systemSpec -> systemSpec.text(systemPrompt))
                // Set the original user message
                .user(userSpec -> userSpec.text(message))
//                .tools(new TimeTools())
                .tools(tableInfoTools)
                .advisors(advisorSpec -> advisorSpec
                        .param(CHAT_MEMORY_CONVERSATION_ID_KEY, chatId)
                        .param(CHAT_MEMORY_RETRIEVE_SIZE_KEY, 100)
                        .param("questionId", finalRequestId)  // 添加requestId
                        .param("timestamp", System.currentTimeMillis())
                        .param("userId", finalUserId)        // 添加userId
                        .param("chatId", chatId))      // 添加sessionId
                .stream()
                .chatResponse();

        responseFlux = getChatResponseFlux(responseFlux);
        return responseFlux;
    }

    @Operation(summary = "获取历史聊天记录")
    @GetMapping("/messages/history")
    public Result<Map<String, Object>> getMessages(
            @RequestParam String chatId,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size) {

        // 获取分页消息
        List<Message> messages = chatMemory.getMessagesByPage(chatId, page, size);

        // 获取消息总数
        long total = chatMemory.getMessagesCount(chatId);

        // 构建返回结果
        Map<String, Object> result = new HashMap<>();
        result.put("messages", messages);
        result.put("total", total);
        result.put("page", page);
        result.put("size", size);
        result.put("hasMore", (page + 1) * size < total);

        return Result.success(result);
    }


    @Operation(summary = "删除会话")
    @DeleteMapping("/del/{chatId}")
    @ResponseBody
    public Result<String> deleteChat(
            @PathVariable("chatId") String chatId,
            @RequestParam(value = "userId", required = false) String userId) {
        // 获取用户ID（优先使用入参，否则从服务中获取）
        String finalUserId = (userId != null && !userId.trim().isEmpty())
                ? userId
                : metabaseService.getCurrentUserId();

        boolean success = chatMemory.deleteSession(chatId, finalUserId);

        if (success) {
            chatMemory.clear(chatId);
            return Result.success("会话删除成功");
        } else {
            return Result.fail("会话删除失败，可能不存在或已被删除");
        }
    }


    @Operation(summary = "获取按日期分组的ai会话列表")
    @GetMapping("/chats/group-by-date")
    public String getSessionsByDate(
            @RequestParam(value = "userId", required = false) String userId) {
        // 获取用户ID（优先使用入参，否则从服务中获取）
        String finalUserId = (userId != null && !userId.trim().isEmpty())
                ? userId
                : metabaseService.getCurrentUserId();
        return ResultUtil.successWithData(chatMemory.getSessionsByDate(finalUserId));
    }

    private static Flux<ChatResponse> getChatResponseFlux(Flux<ChatResponse> responseFlux) {
        return ChatResponseUtils.getChatResponseFlux(responseFlux);
    }


    /**
     * 测试端点，用于检查服务器是否正常工作
     */
    @GetMapping("/test")
    public String test() {
        return "DdlChatController is working!";
    }



    @Operation(summary = "多轮 AI 调用示例 - 真正的流式输出 (重构版)")
    @GetMapping(value = "/multi-stage/ddl-stream-real", produces = MediaType.TEXT_EVENT_STREAM_VALUE)
    public Flux<DdlTaskResponse> multiStageGenerateDdlStreamRefactored(
            @RequestParam(value = "message") String message,
            @RequestParam(value = "chatId") String chatId,
            @RequestParam(value = "isNewChat", defaultValue = "false") boolean isNewChat,
            @RequestParam(value = "userId", required = false) String userId,
            @RequestParam(value = "questionId", required = false) String questionId) {

        Assert.notNull(message, "message不能为空");
        Assert.notNull(chatId, "chatId不能为空");

        log.info("用户消息: {}", message);
        log.info("会话ID: {}", chatId);
        log.info("问题ID: {}", questionId);

        // Delegate the entire multi-stage logic to the service
        return multiStageDdlService.generateDdlStream(message, chatId, isNewChat, userId, questionId)
                .doOnError(e -> log.error("[Controller] 多轮调用流出错", e))
                .doFinally(signalType -> log.info("[Controller] SSE Stream finished with signal: {}", signalType));
    }

    @Operation(summary = "多轮 AI 调用示例 - 优化版 (三阶段流程)")
    @GetMapping(value = "/multi-stage/ddl-stream-refactored", produces = MediaType.TEXT_EVENT_STREAM_VALUE)
    public Flux<DdlTaskResponse> multiStageGenerateDdlStreamOptimized(
            @RequestParam(value = "message") String message,
            @RequestParam(value = "chatId") String chatId,
            @RequestParam(value = "isNewChat", defaultValue = "false") boolean isNewChat,
            @RequestParam(value = "userId", required = false) String userId,
            @RequestParam(value = "questionId", required = false) String questionId) {

        Assert.notNull(message, "message不能为空");
        Assert.notNull(chatId, "chatId不能为空");

        log.info("用户消息: {}", message);
        log.info("会话ID: {}", chatId);
        log.info("问题ID: {}", questionId);

        // 使用相同的服务，但是流程已经优化为三阶段
        return multiStageDdlService.generateDdlStream(message, chatId, isNewChat, userId, questionId)
                .doOnError(e -> log.error("多轮调用流出错", e))
                .doFinally(signalType -> log.info(" SSE Stream finished with signal: {}", signalType));
    }

    @Operation(summary = "多轮 AI 调用示例 - 与 ChatResponse 兼容的格式")
    @GetMapping(value = "/multi-stage/ddl-stream-chat", produces = MediaType.TEXT_EVENT_STREAM_VALUE)
    public Flux<ChatResponseWrapper> multiStageGenerateDdlStreamAsChat(
            @RequestParam(value = "message") String message,
            @RequestParam(value = "chatId") String chatId,
            @RequestParam(value = "isNewChat", defaultValue = "false") boolean isNewChat,
            @RequestParam(value = "userId", required = false) String userId,
            @RequestParam(value = "questionId", required = false) String questionId) {

        Assert.notNull(message, "message不能为空");
        Assert.notNull(chatId, "chatId不能为空");

        // 如果前端没有提供requestId，则生成一个
        final String finalRequestId = (questionId != null && !questionId.trim().isEmpty())
                ? questionId
                : UUID.randomUUID().toString();
        log.info("========== [Controller] 请求多轮 AI 调用 (ChatResponse兼容格式) ==========");
        log.info("用户消息: {}", message);
        log.info("会话ID: {}", chatId);
        log.info("问题ID: {}", finalRequestId);

        // 调用多阶段DDL生成服务，并将结果转换为ChatResponseWrapper格式
        return multiStageDdlService.generateDdlStream(message, chatId, isNewChat, userId, finalRequestId)
                .map(ChatResponseWrapper::fromDdlTaskResponse)
                .doOnError(e -> log.error("[Controller] 多轮调用流出错", e))
                .doFinally(signalType -> log.info("[Controller] SSE Stream finished with signal: {}", signalType));
    }
}