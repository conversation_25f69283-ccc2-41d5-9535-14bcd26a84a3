package com.cpit.chatbi.metabase.util;

import cn.hutool.json.JSONUtil;

import java.util.HashMap;
import java.util.Map;

/**
 * 返回统一接口
 *
 * <AUTHOR>
 */
public class ResultUtil {
    final static String CODE = "code";
    final static Integer SUCCESS = 1;//成功默认0
    final static String IS_SUCCESS = "success";
    final static Integer ERROR = 0;//失败默认-1
    final static String RESULT = "result";
    final static String MESSAGE = "message";
    final static int NUM_TWO = 2;
    final static int NUM_THREE = 3;


    /**
     * 返回结果【只携带消息】,success状态
     *
     * @param code success error
     * @return String
     */
    public static String resultWithMessage(Object code, Boolean isSuccess, String message) {
        Map<Object, Object> map = new HashMap<>(NUM_TWO);
        map.put(CODE, code);
        map.put(MESSAGE, message);
        map.put(IS_SUCCESS, isSuccess);
        return JSONUtil.toJsonPrettyStr(map);
    }

    /**
     * 返回结果【只携带消息】
     *
     * @param code success error
     * @return String
     */
    public static String resultWithMessage(Object code, String message) {
        Map<Object, Object> map = new HashMap<>(NUM_TWO);
        map.put(CODE, code);
        map.put(MESSAGE, message);
        // 根据code值设置success字段
        boolean isSuccess = code.equals(SUCCESS);
        map.put(IS_SUCCESS, isSuccess);
        return JSONUtil.toJsonPrettyStr(map);
    }

    /**
     * 返回结果【携带数据和消息】
     *
     * @param code
     * @param data
     * @param message
     * @return
     */
    public static String resultWithDataAndMessage(Object code, Object data, String message) {
        if (data == null) {
            data = "null";
        }
        Map<Object, Object> map = new HashMap<>(NUM_THREE);
        map.put(CODE, code);
        map.put(RESULT, data);
        map.put(MESSAGE, message);
        // 根据code值设置success字段
        boolean isSuccess = code.equals(SUCCESS);
        map.put(IS_SUCCESS, isSuccess);
        return JSONUtil.toJsonPrettyStr(map);
    }

    /**
     * 返回结果【携带数据和消息】
     *
     * @param code
     * @param data
     * @param message
     * @return
     */
    public static String resultWithDataAndMessage(Object code, Boolean isSuccess, Object data, String message) {
        if (data == null) {
            data = "null";
        }
        Map<Object, Object> map = new HashMap<>(NUM_THREE);
        map.put(CODE, code);
        map.put(RESULT, data);
        map.put(MESSAGE, message);
        map.put(IS_SUCCESS, isSuccess);
        return JSONUtil.toJsonPrettyStr(map);
    }

    /**
     * 返回成功的结果【只携带数据。默认添加成功状态码、操作成功消息】
     *
     * @param data
     * @return
     */
    public static String successWithData(Object data) {
        return resultWithDataAndMessage(SUCCESS, data, "操作成功");
    }

    /**
     * 返回成功的结果【只携带消息，默认添加失败状态码】
     *
     * @param message
     * @return
     */
    public static String successWithMessage(String message) {
        return resultWithMessage(SUCCESS, message);
    }

    /**
     * 返回成功的结果【携带数据和消息。 默认添加成功状态码】
     *
     * @param message
     * @return
     */
    public static String successWithDataAndMessage(Object data, String message) {
        return resultWithDataAndMessage(SUCCESS, data, message);
    }


    /**
     * 返回失败的结果【只携带消息，默认添加失败状态码】
     *
     * @param message
     * @return
     */
    public static String errorWithMessage(String message) {
        return resultWithMessage(ERROR, false, message);
    }

    /**
     * 返回错误信息，简单版本
     *
     * @param message 错误信息
     * @return 错误结果字符串
     */
    public static String error(String message) {
        return errorWithMessage(message);
    }

    /**
     * 返回失败的结果【只携带消息，默认添加失败状态码】
     *
     * @param message
     * @return
     */
    public static String errorWithMessageAndCode(String message, String code) {
        return resultWithMessage(code, message);
    }

}
