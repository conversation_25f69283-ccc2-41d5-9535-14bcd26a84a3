package com.cpit.chatbi.metabase;

import com.cpit.chatbi.metabase.util.NetworkUtils;
import org.springframework.ai.chat.memory.ChatMemory;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.context.ConfigurableApplicationContext;
import org.springframework.core.env.Environment;

@SpringBootApplication
public class MetabaseTestApplication {
    public static void main(String[] args) {
        ConfigurableApplicationContext context = SpringApplication.run(MetabaseTestApplication.class, args);
        ChatMemory memory = context.getBean(ChatMemory.class);
        System.out.println("当前 ChatMemory 实现: " + memory.getClass().getSimpleName());

        // 检查 RedisTemplate 是否存在（可选）
        boolean hasRedisTemplate = context.containsBean("messageRedisTemplate");
        System.out.println("RedisTemplate 是否存在: " + hasRedisTemplate);

        // 打印 Swagger 地址
        Environment env = context.getEnvironment();
        String port = env.getProperty("server.port", "8080");
        String contextPath = env.getProperty("server.servlet.context-path", "");
        String localIp = NetworkUtils.getLocalIp();
        String swaggerLocalUrl = String.format("http://localhost:%s%s/swagger-ui/index.html", port, contextPath);
        String swaggerIpUrl = String.format("http://%s:%s%s/swagger-ui/index.html", localIp, port, contextPath);

        System.out.println("\n===========================================================");
        System.out.println("\tSwagger 文档地址(本地): " + swaggerLocalUrl);
        System.out.println("\tSwagger 文档地址(IP): " + swaggerIpUrl);
        System.out.println("===========================================================\n");
    }
}