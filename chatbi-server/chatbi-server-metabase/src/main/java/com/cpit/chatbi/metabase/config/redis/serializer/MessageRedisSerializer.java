package com.cpit.chatbi.metabase.config.redis.serializer;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.DeserializationContext;
import com.fasterxml.jackson.databind.JsonDeserializer;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import org.springframework.ai.chat.messages.AssistantMessage;
import org.springframework.ai.chat.messages.Message;
import org.springframework.ai.chat.messages.UserMessage;
import org.springframework.ai.model.Media;
import org.springframework.data.redis.serializer.RedisSerializer;

import java.io.IOException;
import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @des 聊天消息序列化器
 * @date 2025/2/11 下午2:22
 */
public class MessageRedisSerializer implements RedisSerializer<Message> {

    private final ObjectMapper objectMapper;
    private final JsonDeserializer<Message> messageDeserializer;

    public MessageRedisSerializer(ObjectMapper objectMapper) {
        this.objectMapper = objectMapper;
        this.messageDeserializer = new JsonDeserializer<>() {
            @Override
            public Message deserialize(JsonParser jp, DeserializationContext ctx)
                    throws IOException {
                ObjectNode root = jp.readValueAsTree();
                String messageType = root.get("messageType").asText();

                return switch (messageType) {
                    case "USER" -> new UserMessage(
                            root.get("text").asText(),
                            objectMapper.convertValue(root.get("media"),
                                    new TypeReference<>() {
                                    }),
                            objectMapper.convertValue(root.get("metadata"),
                                    new TypeReference<>() {
                                    })
                    );
                    case "ASSISTANT" -> new AssistantMessage(
                            root.get("text").asText(),
                            objectMapper.convertValue(root.get("metadata"),
                                    new TypeReference<>() {
                                    }),
                            objectMapper.convertValue(root.get("toolCalls"),
                                    new TypeReference<>() {
                                    }),
                            objectMapper.convertValue(root.get("media"),
                                    new TypeReference<>() {
                                    })
                    );
                    default -> throw new UnsupportedOperationException("未知的消息类型: " + messageType);
                };
            }
        };
    }
    @Override
    public byte[] serialize(Message message) {
        try {
            return objectMapper.writeValueAsBytes(message);
        } catch (JsonProcessingException e) {
            throw new RuntimeException("无法序列化", e);
        }
    }

    @Override
    public Message deserialize(byte[] bytes) {
        if (bytes == null || bytes.length == 0) {
            return null;
        }
        try {
            return messageDeserializer.deserialize(objectMapper.getFactory().createParser(bytes), objectMapper.getDeserializationContext());
        } catch (Exception e) {
            throw new RuntimeException("无法反序列化", e);
        }
    }
}