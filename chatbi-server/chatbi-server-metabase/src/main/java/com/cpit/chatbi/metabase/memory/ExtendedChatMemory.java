package com.cpit.chatbi.metabase.memory;

import com.cpit.chatbi.metabase.domain.chat.ChatSession;
import org.springframework.ai.chat.memory.ChatMemory;
import org.springframework.ai.chat.messages.Message;

import java.time.LocalDate;
import java.util.List;
import java.util.Map;

public interface ExtendedChatMemory extends ChatMemory {
    /**
     * 保存会话
     *
     * @param chatSession 会话
     * @param userId 用户ID
     */
    void saveSession(ChatSession chatSession, String userId);

    /**
     * 获取会话列表，按创建时间降序排序
     *
     * @param userId 用户ID
     * @return 会话列表，最新的在前
     */
    List<ChatSession> getSessions(String userId);

    /**
     * 获取按日期分组的会话列表
     *
     * @param userId 用户ID
     * @return 按日期分组的会话列表映射
     */
    Map<LocalDate, List<ChatSession>> getSessionsByDate(String userId);

    /**
     * 获取分页的聊天消息历史记录
     *
     * @param conversationId 会话ID
     * @param page 页码（从0开始）
     * @param size 每页数量
     * @return 消息列表
     */
    List<Message> getMessagesByPage(String conversationId, int page, int size);

    /**
     * 获取聊天消息总数
     *
     * @param conversationId 会话ID
     * @return 消息总数
     */
    long getMessagesCount(String conversationId);

    /**
     * 删除用户的指定会话
     *
     * @param chatId 会话ID
     * @param userId 用户ID
     * @return 是否删除成功
     */
    boolean deleteSession(String chatId, String userId);
}