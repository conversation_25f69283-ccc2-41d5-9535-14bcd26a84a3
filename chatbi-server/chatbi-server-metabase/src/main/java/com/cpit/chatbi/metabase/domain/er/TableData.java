package com.cpit.chatbi.metabase.domain.er;

import com.alibaba.fastjson.annotation.JSONField;
import java.util.List;

public class TableData {

    @JSONField(ordinal = 1)
    private String tableName;

    @J<PERSON>NField(ordinal = 2)
    private String comment;

    @JSONField(ordinal = 3)
    private List<ColumnInfo> columns;

    public List<ColumnInfo> getColumns() {
        return columns;
    }

    public void setColumns(List<ColumnInfo> columns) {
        this.columns = columns;
    }

    public String getTableName() {
        return tableName;
    }

    public void setTableName(String tableName) {
        this.tableName = tableName;
    }

    public String getComment() {
        return comment;
    }

    public void setComment(String comment) {
        this.comment = comment;
    }
} 