package com.cpit.chatbi.metabase.config;

import com.cpit.chatbi.metabase.service.MetabaseService;
import com.cpit.chatbi.metabase.service.impl.DefaultMetabaseServiceImpl;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class MetabaseServiceConfig {
    
    @Bean
    @ConditionalOnMissingBean(MetabaseService.class)
    public MetabaseService metabaseService() {
        return new DefaultMetabaseServiceImpl();
    }

}