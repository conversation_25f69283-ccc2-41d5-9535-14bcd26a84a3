package com.cpit.chatbi.metabase.service;

import com.cpit.chatbi.metabase.domain.dto.ColumnInfo;
import com.cpit.chatbi.metabase.domain.dto.ColumnType;
import com.cpit.chatbi.metabase.domain.dto.QueryResult;

import java.util.List;
import java.util.Map;

public interface ChartGenerationService {

    /**
     * Identifies columns in the query result as Dimensions or Measures.
     * Can use rules-based logic or potentially an LLM for assistance.
     *
     * @param columns List of ColumnInfo from the query result metadata.
     * @return A map where keys are column names and values are ColumnType enums.
     */
    Map<String, ColumnType> identifyColumnTypes(List<ColumnInfo> columns);

    /**
     * Recommends suitable chart types based on identified dimensions and measures.
     * Can use rules-based logic or potentially an LLM for assistance.
     *
     * @param columnTypes Map result from identifyColumnTypes.
     * @return A list of recommended chart type names (e.g., "bar", "line", "pie", "table"),
     *         ordered by preference (most suitable first).
     */
    List<String> recommendChartTypes(Map<String, ColumnType> columnTypes);

    /**
     * Formats the query result data into a JSON string suitable for ECharts options.
     *
     * @param queryResult       The result from the SQL query execution.
     * @param columnTypes       The identified types (Dimension/Measure) for each column.
     * @param selectedChartType The specific chart type to format the data for (e.g., "bar", "line").
     * @return A JSON string representing the ECharts options object.
     * @throws Exception If formatting fails (e.g., incompatible data, unsupported chart type).
     */
    String formatForECharts(QueryResult queryResult, Map<String, ColumnType> columnTypes, String selectedChartType) throws Exception;
}