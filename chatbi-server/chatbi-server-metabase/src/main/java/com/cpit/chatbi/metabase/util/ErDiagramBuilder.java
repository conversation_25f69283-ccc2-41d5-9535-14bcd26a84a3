package com.cpit.chatbi.metabase.util;

import com.cpit.chatbi.metabase.domain.er.*;
import com.cpit.chatbi.metabase.meta.schema.*;

import java.util.*;

public class ErDiagramBuilder {

    public static ErDiagram build(Schema schema) {
        ErDiagram erDiagram = new ErDiagram();
        List<TableInfo> tables = new ArrayList<>();
        List<Edge> edges = new ArrayList<>();

        Map<String, Table> allTables = schema.getTables();
        for (Table table : allTables.values()) {
            tables.add(buildTableInfo(table));
            edges.addAll(buildEdges(table));
        }

        erDiagram.setTables(tables);
        erDiagram.setEdges(edges);
        erDiagram.sortTablesByEdges();

        return erDiagram;
    }

    private static TableInfo buildTableInfo(Table table) {
        TableInfo tableInfo = new TableInfo();
        TableData data = new TableData();
        data.setTableName(table.getName());
        data.setComment(table.getComment());

        List<ColumnInfo> columns = new ArrayList<>();
        for (Column column : table.getColumns().values()) {
            columns.add(buildColumnInfo(column, table));
        }

        // 对列进行排序，主键排在前面
        columns.sort((c1, c2) -> {
            if (c1.isPrimaryKey() && !c2.isPrimaryKey()) return -1;
            if (!c1.isPrimaryKey() && c2.isPrimaryKey()) return 1;
            return 0;
        });

        data.setColumns(columns);
        tableInfo.setData(data);
        return tableInfo;
    }

    private static ColumnInfo buildColumnInfo(Column column, Table table) {
        ColumnInfo columnInfo = new ColumnInfo();
        columnInfo.setName(column.getName());
        columnInfo.setColumnType(buildColumnType(column));
        columnInfo.setPrimaryKey(isPrimaryKey(column, table));
        columnInfo.setForeignKey(isForeignKey(column, table));
        columnInfo.setComment(column.getComment());
        return columnInfo;
    }

    private static boolean isForeignKey(Column column, Table table) {
        if (table.getForeignkeys() == null) {
            return false;
        }

        for (ForeignKey fk : table.getForeignkeys().values()) {
            for (ForeignKeyColumnReference ref : fk.getColumnReferences()) {
                if (ref.getForeignColumn().getTable().equals(table.getName()) &&
                    ref.getForeignColumn().getColumn().equals(column.getName())) {
                    return true;
                }
            }
        }
        return false;
    }

    private static String buildColumnType(Column column) {
        return column.getTypeName().toUpperCase() +
            (column.getLength() > 0 ? "(" + column.getLength() + ")" : "");
    }

    private static boolean isPrimaryKey(Column column, Table table) {
        return table.getPrimaryKey() != null &&
            table.getPrimaryKey().getColumns().contains(column.getName());
    }

    private static List<Edge> buildEdges(Table table) {
        List<Edge> edges = new ArrayList<>();
        if (table.getForeignkeys() != null) {
            for (ForeignKey fk : table.getForeignkeys().values()) {
                for (ForeignKeyColumnReference ref : fk.getColumnReferences()) {
                    edges.add(buildEdge(table.getName(), ref));
                }
            }
        }
        return edges;
    }

    private static Edge buildEdge(String tableName, ForeignKeyColumnReference ref) {
        Edge edge = new Edge();
        edge.setSource(tableName);
        edge.setTarget(ref.getPrimaryColumn().getTable());
        edge.setSourceHandle(ref.getForeignColumn().getColumn());
        edge.setTargetHandle(ref.getPrimaryColumn().getColumn());
        return edge;
    }
}