package com.cpit.chatbi.metabase.toolcall.table.method;

import com.cpit.chatbi.metabase.meta.schema.Schema;
import com.cpit.chatbi.metabase.service.MetabaseService;
import com.cpit.chatbi.metabase.toolcall.table.TableInfoUtils;
import com.cpit.chatbi.metabase.toolcall.time.method.TimeTools;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.ai.chat.model.ChatResponse;
import org.springframework.ai.tool.annotation.Tool;
import org.springframework.ai.tool.annotation.ToolParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import reactor.core.publisher.Flux;

/**
 * 表信息工具类，提供给大模型使用
 */

public class TableInfoTools {
    private static final Logger logger = LoggerFactory.getLogger(TableInfoTools.class);
    // 注入 MetabaseService - 确保它能被正确注入
    // 可以通过构造函数注入，或者如果 TableInfoTools 是 Bean，使用 @Autowired
    private final MetabaseService metabaseService;

    public TableInfoTools(MetabaseService metabaseService) {
        this.metabaseService = metabaseService;
    }

    /**
     * 获取指定表的详细信息，包括列定义、主键、外键和索引等。
     * 仅在你需要了解一个【已知存在于数据库中】的表的详细结构时调用此工具。
     * @param tableName 要查询的、确认存在的表名
     * @return 表的详细结构信息字符串，如果查找失败则返回错误信息。
     */
    @Tool(description = "获取一个已存在表的详细结构信息(列, 类型, 约束等)")
    public String getTableInfo(@ToolParam(description = "要查询详细信息的表名") String tableName) {
        logger.info("Tool Call: getTableInfo for table: {}", tableName);
        if (tableName == null || tableName.trim().isEmpty()) {
            return "错误：表名不能为空。";
        }
        try {
            // 确保 metabaseService 已被正确初始化/注入
            if (metabaseService == null) {
                logger.error("MetabaseService is not injected/initialized in TableInfoTools.");
                return "内部错误：无法访问数据库服务。";
            }
            Schema schema = metabaseService.getSchemaWithDatabase();
            // TableInfoUtils 需要能处理表不存在的情况，或者在这里加一层判断
            String tableInfo = TableInfoUtils.getTableInfo(schema, tableName.trim());
            if (tableInfo == null || tableInfo.contains("not found") || tableInfo.contains("未找到")) { // 根据 TableInfoUtils 的实际输出来调整判断条件
                logger.warn("Table '{}' not found in schema during tool call.", tableName);
                // 虽然理论上 System Prompt 指导了只有存在的表才调用，但加一层防护更好
                return String.format("错误：表 '%s' 在数据库模式中未找到。", tableName);
            }
            logger.debug("Table info retrieved for {}: {}", tableName, tableInfo);
            return tableInfo;
        } catch (Exception e) {
            logger.error("获取表信息失败 for table {}: {}", tableName, e.getMessage(), e);
            // 返回对LLM友好的错误信息
            return String.format("获取表 '%s' 的信息时发生内部错误: %s", tableName, e.getMessage());
        }
    }
}
