package com.cpit.chatbi.metabase.domain.dto.stage;

import java.util.ArrayList;
import java.util.List;

// DTO for stage result
@lombok.Data
@lombok.AllArgsConstructor
@lombok.NoArgsConstructor
public class StageResult {
    private String output; // Final output string of the stage
    private boolean shouldContinue; // Should the process continue to the next stage?
    private List<String> tableNames = new ArrayList<>(); // 相关表名列表

    public static StageResult continueWith(String output) {
        return new StageResult(output, true, new ArrayList<>());
    }

    public static StageResult continueWith(String output, List<String> tableNames) {
        return new StageResult(output, true, tableNames);
    }

    public static StageResult stopWith(String output) {
        return new StageResult(output, false, new ArrayList<>());
    }
}