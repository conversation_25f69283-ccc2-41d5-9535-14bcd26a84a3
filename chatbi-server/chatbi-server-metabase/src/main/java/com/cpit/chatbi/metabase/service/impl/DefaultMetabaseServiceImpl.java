package com.cpit.chatbi.metabase.service.impl;

import com.cpit.chatbi.metabase.domain.er.ErDiagram;
import com.cpit.chatbi.metabase.meta.core.MetaLoader;
import com.cpit.chatbi.metabase.meta.core.SchemaInfoLevel;
import com.cpit.chatbi.metabase.meta.schema.Schema;
import com.cpit.chatbi.metabase.service.MetabaseService;
import com.cpit.chatbi.metabase.util.ErDiagramBuilder;
import com.cpit.chatbi.metabase.util.Result;
import com.cpit.chatbi.metabase.util.ResultUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;

@Slf4j
public class DefaultMetabaseServiceImpl implements MetabaseService {

    @Autowired
    private MetaLoader metaLoader;
    @Override
    public ErDiagram getSchema() {
        Schema schema = getSchemaWithDatabase();
        // 使用构建器生成ER图
        return ErDiagramBuilder.build(schema);
    }

    @Override
    public Schema getSchemaWithDatabase() {
        return metaLoader.getSchema(SchemaInfoLevel.max());
    }

    @Override
    public String getCurrentUserId() {
        // 默认实现返回系统用户
        return "userId";
    }

    @Override
    public String executeDdlSql(String sql) {
        try {
            metaLoader.executeDDL(sql);
            return ResultUtil.successWithMessage("SQL执行成功");
        } catch (Exception e) {
            String errorMessage = String.format("SQL执行失败：%s\n错误信息：%s", sql, e.getMessage());
            log.error(errorMessage, e);
            // 使用新的error方法，确保返回正确的错误状态
            return ResultUtil.error(errorMessage);
        }
    }

}