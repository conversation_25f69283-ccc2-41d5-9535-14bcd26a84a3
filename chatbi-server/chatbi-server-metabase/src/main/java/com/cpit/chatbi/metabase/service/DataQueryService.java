package com.cpit.chatbi.metabase.service;


import com.cpit.chatbi.metabase.domain.dto.QueryResult;

import java.sql.SQLException;

public interface DataQueryService {

    /**
     * Executes a given SELECT SQL query against the configured database.
     * IMPORTANT: Implementations MUST handle potential SQL injection risks
     * associated with executing dynamically generated SQL. Use read-only users,
     * input validation, or other security measures.
     *
     * @param selectSql The SELECT SQL query string to execute.
     * @return A QueryResult object containing the data, metadata, and execution status.
     *         Even if execution fails, a QueryResult is returned with errorMessage set.
     */
    QueryResult executeQuery(String selectSql);
}