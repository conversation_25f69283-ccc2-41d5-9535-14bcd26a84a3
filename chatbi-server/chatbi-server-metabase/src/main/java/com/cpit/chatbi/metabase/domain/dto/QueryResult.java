package com.cpit.chatbi.metabase.domain.dto;

import lombok.Data;
import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Map;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class QueryResult {
    private List<Map<String, Object>> data;
    private List<ColumnInfo> columns;
    private String generatedSql; // Include the SQL that generated this result
    private boolean executionSuccessful = true;
    private String errorMessage;

    // Constructor for successful execution
    public QueryResult(List<Map<String, Object>> data, List<ColumnInfo> columns, String generatedSql) {
        this.data = data;
        this.columns = columns;
        this.generatedSql = generatedSql;
    }

    // Constructor for failed execution
    public QueryResult(String generatedSql, String errorMessage) {
        this.generatedSql = generatedSql;
        this.executionSuccessful = false;
        this.errorMessage = errorMessage;
    }
}