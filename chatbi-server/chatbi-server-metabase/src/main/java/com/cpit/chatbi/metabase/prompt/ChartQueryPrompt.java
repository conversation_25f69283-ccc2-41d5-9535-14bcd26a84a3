package com.cpit.chatbi.metabase.prompt;

import com.cpit.chatbi.metabase.meta.schema.Column;
import com.cpit.chatbi.metabase.meta.schema.DatabaseInfo;
import com.cpit.chatbi.metabase.meta.schema.Schema;
import com.cpit.chatbi.metabase.meta.schema.Table;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.Resource;
import org.springframework.stereotype.Component;
import org.springframework.util.StreamUtils; // Import StreamUtils

import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.Map;

@Slf4j
@Component
public class ChartQueryPrompt {

    @Value("classpath:/prompts/system-prompt-text-to-sql.st")
    private Resource systemPromptResource;

    /**
     * Generates the system prompt content for the Text-to-SQL LLM.
     *
     * @param schema The database schema information.
     * @return The formatted system prompt string.
     * @throws IOException If the prompt template file cannot be read.
     */
    public String getSystemQueryPrompt(Schema schema) throws IOException {
        if (schema == null || schema.getDatabaseInfo() == null || schema.getSchemaInfo() == null) {
            log.error("Schema, DatabaseInfo, or SchemaInfo is null. Cannot generate prompt.");
            // Or throw a more specific exception
            return "ERROR: Incomplete database schema information provided.";
        }

        // Use try-with-resources for InputStream
        String template;
        try {
            template = StreamUtils.copyToString(systemPromptResource.getInputStream(), StandardCharsets.UTF_8);
        } catch (IOException e) {
            log.error("Failed to read system prompt template resource: {}", systemPromptResource, e);
            throw e; // Re-throw or handle appropriately
        }


        String tableInfo = generateTableInfo(schema); // Re-use or adapt your existing method

        DatabaseInfo dbInfo = schema.getDatabaseInfo();
        String schemaName = schema.getSchemaInfo().getSchemaName();

        // Basic null checks for required fields
        String productName = dbInfo.getProductName() != null ? dbInfo.getProductName() : "[Unknown Database]";
        String productVersion = dbInfo.getProductVersion() != null ? dbInfo.getProductVersion() : "[Unknown Version]";
        schemaName = schemaName != null ? schemaName : "[Unknown Schema]";

        if (tableInfo.isEmpty() && (schema.getTables() == null || schema.getTables().isEmpty())) {
            tableInfo = "/* No tables found in the provided schema. */";
            log.warn("No tables found in schema '{}' for database '{}'. Prompt will reflect this.", schemaName, productName);
        }


        return template
                .replace("${productName}", productName)
                .replace("${productVersion}", productVersion)
                .replace("${schemaName}", schemaName)
                .replace("${tableInfo}", tableInfo);
    }

    /**
     * Generates table structure information string.
     * (Assuming similar logic to your SqlAssistantPrompt's method)
     * Adapt this method if your DTO structure or desired format differs.
     */
    private String generateTableInfo(Schema schema) {
        if (schema == null || schema.getTables() == null || schema.getTables().isEmpty()) {
            return ""; // Return empty if no tables
        }
        StringBuilder sb = new StringBuilder();
        Map<String, Table> tables = schema.getTables();

        for (Table table : tables.values()) {
            sb.append("### Table: `").append(table.getName()).append("`\n"); // Use backticks for table name
            if (table.getComment() != null && !table.getComment().isBlank()) {
                sb.append("- Description: ").append(table.getComment()).append("\n");
            }

            // Columns
            if (table.getColumns() != null && !table.getColumns().isEmpty()){
                sb.append("- Columns:\n");
                for (Column col : table.getColumns().values()) {
                    sb.append("  * `").append(col.getName()).append("` ") // Backticks for column name
                            .append("(").append(col.getTypeName()).append(")");

                    if (!col.isNullable()) {
                        sb.append(" NOT NULL");
                    }
                    // Defaults might be less relevant for SELECT, but keep if needed
                    // if (col.getDefaultValue() != null) {
                    //     sb.append(" DEFAULT ").append(col.getDefaultValue());
                    // }
                    if (col.getComment() != null && !col.getComment().isBlank()) {
                        sb.append(" -- ").append(col.getComment()); // Use SQL comment style for descriptions
                    }
                    sb.append("\n");
                }
            } else {
                sb.append("- Columns: (No column information available)\n");
            }


            // Primary Key (Less critical for SELECT but good context)
            if (table.getPrimaryKey() != null && !table.getPrimaryKey().getColumns().isEmpty()) {
                sb.append("- Primary Key: `").append(String.join("`, `", table.getPrimaryKey().getColumns())).append("`\n");
            }

            // Indices (Less critical for SELECT but good context)
            // if (!table.getIndexs().isEmpty()) {
            //     sb.append("- Indexes:\n");
            //     table.getIndexs().forEach((name, index) -> {
            //         sb.append("  * ").append(name).append(": (").append(String.join(", ", index.getColumnNames())).append(")\n");
            //     });
            // }
            sb.append("\n"); // Separator between tables
        }
        return sb.toString();
    }
}
