package com.cpit.chatbi.metabase.domain.dto;

import lombok.Data;
import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class ColumnInfo {
    private String name;
    private String typeName; // DB specific type name (e.g., "varchar", "int4")
    private int sqlType;     // JDBC type code (java.sql.Types)

    @Override
    public String toString() {
        return name + " (" + typeName + ")";
    }
}