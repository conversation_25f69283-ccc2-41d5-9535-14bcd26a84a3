package com.cpit.chatbi.metabase.domain.chat;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

/**
 * <AUTHOR>
 * @des 聊天会话
 * @date 2025/2/11 下午3:03
 */
@Data
@Accessors(chain = true)
public class ChatSession implements Comparable<ChatSession> {

    @Schema(description = "会话id")
    private String chatId;

    @Schema(description = "会话名称")
    private String chatName;

    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    @Schema(description = "格式化的创建时间")
    private String formattedCreateTime;

    /**
     * 设置创建时间并自动格式化
     * @param createTime 创建时间
     * @return this
     */
    public ChatSession setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
        if (createTime != null) {
            this.formattedCreateTime = createTime.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
        }
        return this;
    }

    /**
     * 初始化创建时间为当前时间
     * @return this
     */
    public ChatSession initCreateTime() {
        return setCreateTime(LocalDateTime.now());
    }

    /**
     * 实现Comparable接口，用于按时间排序（最新的在前）
     */
    @Override
    public int compareTo(ChatSession other) {
        if (this.createTime == null && other.createTime == null) {
            return 0;
        } else if (this.createTime == null) {
            return 1; // null值排在后面
        } else if (other.createTime == null) {
            return -1;
        }
        // 降序排列，最新的在前
        return other.createTime.compareTo(this.createTime);
    }
}
