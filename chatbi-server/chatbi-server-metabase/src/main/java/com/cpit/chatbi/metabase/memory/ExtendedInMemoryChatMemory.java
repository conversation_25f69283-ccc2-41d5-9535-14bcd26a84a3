package com.cpit.chatbi.metabase.memory;

import com.cpit.chatbi.metabase.domain.chat.ChatSession;
import lombok.extern.slf4j.Slf4j;
import org.springframework.ai.chat.memory.InMemoryChatMemory;
import org.springframework.ai.chat.messages.Message;

import java.time.LocalDate;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;
import java.util.Iterator;
import java.util.Map;

@Slf4j
public class ExtendedInMemoryChatMemory extends InMemoryChatMemory implements ExtendedChatMemory {

    private static final int MAX_SESSIONS_PER_USER = 100;
    private final Map<String, List<ChatSession>> userSessions = new ConcurrentHashMap<>();

    @Override
    public void saveSession(ChatSession chatSession, String userId) {
        // 确保创建时间已设置
        if (chatSession.getCreateTime() == null) {
            chatSession.initCreateTime();
        }

        List<ChatSession> sessions = userSessions.computeIfAbsent(userId, k -> new ArrayList<>());

        // 检查是否已存在相同sessionId的会话
        Optional<ChatSession> existingSession = sessions.stream()
                .filter(s -> s.getChatId().equals(chatSession.getChatId()))
                .findFirst();

        if (existingSession.isPresent()) {
            // 如果存在，更新会话名称和时间
            ChatSession session = existingSession.get();
            session.setChatName(chatSession.getChatName());
            session.setCreateTime(chatSession.getCreateTime());
            return;
        }

        // 检查会话数量限制
        if (sessions.size() >= MAX_SESSIONS_PER_USER) {
            // 按时间排序，移除最旧的会话
            sessions.sort(Comparator.comparing(ChatSession::getCreateTime));
            sessions.remove(0); // 移除最旧的会话
        }

        sessions.add(chatSession);

        // 按时间降序排序，最新的在前面
        sessions.sort(Collections.reverseOrder(Comparator.comparing(ChatSession::getCreateTime)));
    }

    @Override
    public List<ChatSession> getSessions(String userId) {
        List<ChatSession> sessions = userSessions.getOrDefault(userId, List.of());
        // 确保返回的列表是按时间降序排序的
        if (!sessions.isEmpty()) {
            List<ChatSession> sortedSessions = new ArrayList<>(sessions);
            sortedSessions.sort(Collections.reverseOrder(Comparator.comparing(ChatSession::getCreateTime,
                    Comparator.nullsLast(Comparator.naturalOrder()))));
            return sortedSessions;
        }
        return sessions;
    }

    @Override
    public Map<LocalDate, List<ChatSession>> getSessionsByDate(String userId) {
        List<ChatSession> sessions = getSessions(userId);
        return sessions.stream()
                .filter(session -> session.getCreateTime() != null)
                .collect(Collectors.groupingBy(
                        session -> session.getCreateTime().toLocalDate(),
                        Collectors.toList()
                ));
    }

    @Override
    public List<Message> getMessagesByPage(String conversationId, int page, int size) {
        try {
            // 获取所有消息
            List<Message> allMessages = super.get(conversationId, Integer.MAX_VALUE);

            // 计算总数
            int total = allMessages.size();
            if (total == 0) {
                return List.of();
            }

            // 过滤掉中间阶段的系统提问
            List<Message> filteredMessages = allMessages.stream()
                .filter(message -> {
                    // 检查元数据
                    Map<String, Object> metadata = message.getMetadata();
                    if (metadata != null) {
                        // 如果是生成阶段的系统提问，则过滤掉
                        Object stage = metadata.get("stage");
                        Object metaMessageType = metadata.get("messageType");

                        // 如果是生成阶段的系统提问，则过滤掉
                        if (stage != null && "generation".equals(stage.toString()) &&
                            metaMessageType != null && "USER".equals(metaMessageType.toString())) {
                            return false;
                        }
                    }
                    return true;
                })
                .collect(Collectors.toList());

            // 计算过滤后的总数
            int filteredTotal = filteredMessages.size();
            if (filteredTotal == 0) {
                return List.of();
            }

            // 计算起始和结束索引
            // 我们希望最新的消息在第一页，所以从列表尾部开始计算
            int start = Math.max(0, filteredTotal - (page + 1) * size);
            int end = Math.min(filteredTotal, filteredTotal - page * size);

            // 处理边界情况
            if (start >= end) {
                return List.of();
            }

            // 获取指定范围的消息
            return filteredMessages.subList(start, end);
        } catch (Exception e) {
            log.error("获取分页消息时发生异常", e);
            return List.of();
        }
    }

    @Override
    public long getMessagesCount(String conversationId) {
        try {
            // 获取所有消息
            List<Message> allMessages = super.get(conversationId, Integer.MAX_VALUE);
            if (allMessages.isEmpty()) {
                return 0;
            }

            // 过滤掉中间阶段的系统提问
            long filteredCount = allMessages.stream()
                .filter(message -> {
                    // 检查元数据
                    Map<String, Object> metadata = message.getMetadata();
                    if (metadata != null) {
                        // 如果是生成阶段的系统提问，则过滤掉
                        Object stage = metadata.get("stage");
                        Object metaMessageType = metadata.get("messageType");

                        // 如果是生成阶段的系统提问，则过滤掉
                        if (stage != null && "generation".equals(stage.toString()) &&
                            metaMessageType != null && "USER".equals(metaMessageType.toString())) {
                            return false;
                        }
                    }
                    return true;
                })
                .count();

            return filteredCount;
        } catch (Exception e) {
            log.error("获取消息总数时发生异常", e);
            return 0;
        }
    }

    @Override
    public boolean deleteSession(String chatId, String userId) {
        try {
            if (chatId == null || chatId.trim().isEmpty()) {
                log.warn("会话ID为空，无法删除会话");
                return false;
            }
            if (userId == null || userId.trim().isEmpty()) {
                log.warn("用户ID为空，无法删除会话");
                return false;
            }

            List<ChatSession> sessions = userSessions.get(userId);
            if (sessions == null || sessions.isEmpty()) {
                log.warn("用户没有会话记录: {}", userId);
                return false;
            }

            boolean found = false;
            Iterator<ChatSession> iterator = sessions.iterator();
            while (iterator.hasNext()) {
                ChatSession session = iterator.next();
                if (chatId.equals(session.getChatId())) {
                    // 找到匹配的会话，从列表中删除
                    iterator.remove();
                    found = true;

                    // 同时删除会话的消息历史
                    super.clear(chatId);

                    log.debug("成功删除会话: {}, 用户: {}", chatId, userId);
                    break;
                }
            }

            if (!found) {
                log.warn("未找到指定的会话: {}, 用户: {}", chatId, userId);
            }

            return found;
        } catch (Exception e) {
            log.error("删除会话时发生异常", e);
            return false;
        }
    }
}