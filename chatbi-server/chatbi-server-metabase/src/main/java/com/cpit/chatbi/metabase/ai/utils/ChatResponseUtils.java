package com.cpit.chatbi.metabase.ai.utils;

import lombok.extern.slf4j.Slf4j;
import org.springframework.ai.chat.ChatResponse;
import org.springframework.ai.chat.Generation;
import reactor.core.publisher.Flux;

/**
 * 聊天响应工具类
 * 提供处理ChatResponse流的实用方法
 */
@Slf4j
public class ChatResponseUtils {

    /**
     * 处理ChatResponse流，打印内容并返回处理后的流
     * 
     * @param responseFlux ChatResponse流
     * @return 处理后的ChatResponse流
     */
    public static Flux<ChatResponse> getChatResponseFlux(Flux<ChatResponse> responseFlux) {
        responseFlux = responseFlux
                .doOnNext(chatResponse -> {
                    if (chatResponse != null && chatResponse.getResults() != null) {
                        for (Generation generation : chatResponse.getResults()) {
                            if (generation != null && generation.getOutput() != null) {
                                String content = generation.getOutput().getText();
                                if (content != null) {
                                    System.out.print(content);
                                }
                            }
                        }
                    }
                })
                .doOnError(error -> log.error("流式输出错误: ", error))
                .doOnComplete(() -> System.out.println("\n流式输出完成"));
        return responseFlux;
    }
}
