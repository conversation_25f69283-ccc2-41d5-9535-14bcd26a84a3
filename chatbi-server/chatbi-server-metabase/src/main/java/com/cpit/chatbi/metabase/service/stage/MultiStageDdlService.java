package com.cpit.chatbi.metabase.service.stage;

import com.cpit.chatbi.metabase.domain.chat.ChatSession;
import com.cpit.chatbi.metabase.domain.chat.DdlTaskResponse;
import com.cpit.chatbi.metabase.domain.dto.stage.DdlGenerationStage;
import com.cpit.chatbi.metabase.domain.dto.stage.StageContext;
import com.cpit.chatbi.metabase.domain.dto.stage.StageResult;
import com.cpit.chatbi.metabase.memory.ExtendedChatMemory;
import com.cpit.chatbi.metabase.meta.schema.Schema;
import com.cpit.chatbi.metabase.prompt.SqlAssistantPrompt;
import com.cpit.chatbi.metabase.service.MetabaseService;
import com.cpit.chatbi.metabase.util.SqlExtractorUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.ai.chat.client.ChatClient;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Flux;
import reactor.core.publisher.FluxSink;
import reactor.core.publisher.Mono;
import reactor.core.scheduler.Schedulers;

import java.util.Arrays;
import java.util.List;

@Service
@Slf4j
@RequiredArgsConstructor // Injects final fields via constructor
public class MultiStageDdlService {

    private final ChatClient chatClient;
    private final MetabaseService metabaseService;
    private final ExtendedChatMemory chatMemory;
    private final SqlAssistantPrompt sqlAssistantPrompt;

    // Define the sequence of stages
    private List<DdlGenerationStage> getStages() {
        return Arrays.asList(
                new AnalysisStage(chatClient, sqlAssistantPrompt, metabaseService),
                new GenerationStage(chatClient, sqlAssistantPrompt, metabaseService)
                // 注释掉验证阶段，直接在生成SQL后结束流程
                // new OptimizationStage(chatClient, sqlAssistantPrompt, metabaseService)
        );
    }

    public Flux<DdlTaskResponse> generateDdlStream(String message, String chatId, boolean isNewChat, String userId, String questionId) {

        // --- 1. Preparation Monos ---
        Mono<String> userIdMono = Mono.fromCallable(() ->
                (userId != null && !userId.trim().isEmpty()) ? userId : metabaseService.getCurrentUserId()
        ).subscribeOn(Schedulers.boundedElastic()).cache(); // Cache result

        Mono<Void> saveSessionMono = userIdMono.flatMap(finalUserId -> {
            if (isNewChat) {
                return Mono.fromRunnable(() -> {
                    ChatSession chatSession = new ChatSession()
                            .setChatId(chatId)
                            .setChatName(message.length() >= 15 ? message.substring(0, 15) : message)
                            .initCreateTime();
                    chatMemory.saveSession(chatSession, finalUserId);
                    log.info("新会话信息已保存: {}", chatId);
                }).subscribeOn(Schedulers.boundedElastic()).then();
            }
            return Mono.empty();
        });

        // 不再需要获取系统提示词，因为每个阶段都会设置自己的系统提示词
        // 使用一个默认的系统提示词作为初始值
        String defaultSystemPrompt = "你是中国邮政公司的数据库专家，专注于数据库设计和 SQL 生成。";

        // --- 2. Combine Preparation and Start Orchestration ---
        return userIdMono
                .flatMapMany(finalUserId -> {
                    // 使用默认的系统提示词创建初始上下文
                    StageContext initialContext = StageContext.initial(defaultSystemPrompt, chatId, finalUserId, message, questionId);

                    // Execute session saving in parallel/before starting the main flow
                    return saveSessionMono.thenMany(
                            Flux.<DdlTaskResponse>create(sink ->
                                    orchestrateStages(initialContext, getStages(), sink)
                            )
                    );
                })
                .onErrorResume(e -> { // Catch errors from preparation phase (e.g., getting system prompt)
                    log.error("多轮 DDL 流准备阶段出错", e);
                    // 使用更美观的Markdown格式化错误消息
                    String errorMessage = String.format("### ❌ **初始化任务失败**\n\n```\n%s\n```", e.getMessage());
                    return Flux.just(new DdlTaskResponse("error", errorMessage, "error"));
                });
    }

    private void orchestrateStages(StageContext initialContext, List<DdlGenerationStage> stages, FluxSink<DdlTaskResponse> sink) {
        // 不再发送初始化消息，直接开始分析阶段

        // Start with the initial context as a Mono
        Mono<StageContext> contextMono = Mono.just(initialContext);

        // Chain the stages using concatMap to ensure sequential execution
        for (DdlGenerationStage stage : stages) {
            contextMono = contextMono.flatMap(currentContext -> {
                // Execute the stage's process method
                return stage.process(currentContext, sink)
                        .flatMap(stageResult -> {
                            // Update context with the output of the completed stage
                            StageContext nextContext = currentContext.withPreviousStageOutput(stageResult.getOutput());

                            // 保留当前上下文中的系统提示词修改
                            nextContext.setSystemPrompt(currentContext.getSystemPrompt());

                            // 将当前阶段的结果存储到下一阶段的上下文中
                            nextContext.setPreviousStageResult(stageResult);

                            // 不再需要保存最后一个阶段的结果，因为我们使用finalContext.getPreviousStageOutput()

                            if (!stageResult.isShouldContinue()) {
                                if (stage.getStageName().equals("validation")) {
                                    log.info("========== [Orchestrator] 验证阶段完成，正常结束流程 ==========");
                                    // 验证阶段是最后一个阶段，正常结束
                                    // 使用空字符串作为完成消息
                                    // 在AbstractDdlStage中已经发送了相应状态的响应，这里不需要再次发送
                                } else {
                                    log.info("========== [Orchestrator] Stage '{}' 请求停止流程 ==========", stage.getStageName());
                                    // 不再发送“流程提前终止”消息，直接使用原始消息
                                    // 如果输出为空，说明已经在前面发送过内容，不需要重复发送
                                    // 在AbstractDdlStage中已经发送了相应状态的响应，这里不需要再次发送
                                }
                                // Return an empty Mono to signal completion of the chain *without* error
                                return Mono.empty(); // This stops the concatMap chain gracefully
                            }
                            // If continuing, just pass the updated context down the chain
                            return Mono.just(nextContext);
                        });
            });
        }

        // After all stages (or early termination), execute the finalization logic
        contextMono
                .doOnSuccess(finalContext -> { // Only executes if the chain completed successfully (didn't Mono.empty())
                    if (finalContext != null && finalContext.getPreviousStageOutput() != null) {
                        // Final processing after the last stage completes
                        String finalStageOutput = finalContext.getPreviousStageOutput();

                        // 不再发送总结消息
                        // sink.next(new DdlTaskResponse("summary", "", "complete"));

                        String finalSql = SqlExtractorUtils.extractSql(finalStageOutput);
                        if (SqlExtractorUtils.validSql(finalSql)) {
                            // 使用Markdown格式化SQL，使其更美观
                            String formattedSql = "```sql\n" + finalSql + "\n```";
                            sink.next(new DdlTaskResponse("sql", formattedSql, "complete", finalSql));
                        }
                        log.info("========== [Orchestrator] 流程正常完成 ==========");
                    } else {
                        // 如果是生成阶段结束的，这是正常情况
                        if (stages.get(stages.size() - 1).getStageName().equals("generation")) {
                            log.info("========== [Orchestrator] 生成阶段完成，流程正常结束 ==========");

                            // 尝试从生成阶段的输出中提取SQL
                            if (finalContext != null && finalContext.getPreviousStageOutput() != null) {
                                // 使用finalContext中的上一阶段输出
                                String lastOutput = finalContext.getPreviousStageOutput();
                                String extractedSql = SqlExtractorUtils.extractSql(lastOutput);
                                if (SqlExtractorUtils.validSql(extractedSql)) {
                                    log.info("========== [Orchestrator] 从生成阶段提取SQL成功 ==========");
                                    // 使用Markdown格式化SQL，使其更美观
                                    String formattedSql = "```sql\n" + extractedSql + "\n```";
                                    sink.next(new DdlTaskResponse("sql", formattedSql, "complete", extractedSql));
                                } else {
                                    log.warn("========== [Orchestrator] 无法从生成阶段提取有效SQL ==========");
                                }
                            } else {
                                log.warn("========== [Orchestrator] finalContext为空或没有上一阶段输出 ==========");
                            }
                        } else if (stages.get(stages.size() - 1).getStageName().equals("validation")) {
                            log.info("========== [Orchestrator] 验证阶段完成，流程正常结束 ==========");
                        } else {
                            log.info("========== [Orchestrator] 流程提前终止，无最终结果处理 ==========");
                        }
                    }
                    sink.complete(); // Complete the main Flux sink
                })
                .doOnError(e -> {
                    // Errors from stage.process (like AI call failures) are caught here if sink.error() was called
                    log.error("========== [Orchestrator] 流程因错误终止 ==========", e);
                    // Error message already sent by the stage, just ensure completion
                    if (!sink.isCancelled()) { // Check if already completed/error'd
                        sink.complete(); // Or sink.error(e) again if needed, but usually just complete after sending error DTO
                    }
                })
                .doFinally(signal -> log.info("[Orchestrator] Stage chain processing finished with signal: {}", signal))
                .subscribe(); // Subscribe to start the orchestration chain
    }




}