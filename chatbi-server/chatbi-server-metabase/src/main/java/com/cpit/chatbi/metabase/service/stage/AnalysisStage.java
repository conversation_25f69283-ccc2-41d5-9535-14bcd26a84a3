package com.cpit.chatbi.metabase.service.stage;

import com.cpit.chatbi.metabase.domain.dto.stage.StageContext;
import com.cpit.chatbi.metabase.domain.dto.stage.StageResult;
import com.cpit.chatbi.metabase.meta.schema.Schema;
import com.cpit.chatbi.metabase.meta.schema.Table;
import com.cpit.chatbi.metabase.prompt.SqlAssistantPrompt;
import com.cpit.chatbi.metabase.service.MetabaseService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.ai.chat.client.ChatClient;
import reactor.core.publisher.Mono;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@Slf4j
class AnalysisStage extends AbstractDdlStage {
    private final SqlAssistantPrompt sqlAssistantPrompt;
    private final MetabaseService metabaseService;

    public AnalysisStage(ChatClient chatClient, SqlAssistantPrompt sqlAssistantPrompt, MetabaseService metabaseService) {
        super(chatClient, "analysis", "分析需求");
        this.sqlAssistantPrompt = sqlAssistantPrompt;
        this.metabaseService = metabaseService;
    }
    @Override protected String buildUserPrompt(StageContext context) {
        try {
            // 获取数据库模式信息
            Schema schema = metabaseService.getSchemaWithDatabase();

            // 使用专门的DDL相关性判断提示词
            String systemPrompt = sqlAssistantPrompt.getDdlRelevancePrompt(schema);

            // 设置到上下文中，覆盖原来的系统提示词
            context.setSystemPrompt(systemPrompt);

            // 用户问题直接作为提示词
            return context.getInitialUserMessage();
        } catch (IOException e) {
            log.error("获取DDL相关性判断提示词失败", e);
            // 如果出错，使用默认提示词
            return "请分析以下需求，并判断是否与数据库、表结构、SQL、DDL、数据模型相关。\n\n" +
                   "首先，明确回答'是'或'否'，表示该需求是否与数据库相关。\n" +
                   "然后，如果是数据库相关需求，请提取关键实体和关系；如果不是，请简要解释原因。\n\n" +
                   "用户需求：" + context.getInitialUserMessage();
        }
    }
    // getStageChatIdSuffix() 方法已经不再需要，因为我们使用统一的聊天ID

    // 判断是否与数据库相关，如果不相关则停止流程
    @Override
    protected Mono<StageResult> decideContinuation(String finalOutput) {
        if (finalOutput == null || finalOutput.trim().isEmpty()) {
            log.warn("========== [Stage: {}] 分析结果为空，终止流程 ==========", getStageName());
            return Mono.just(StageResult.stopWith("需求分析未能提取有效信息，无法继续生成DDL。"));
        }

        // 检查输出中是否包含表名相关的关键词
        boolean isDatabaseRelated = finalOutput.contains("匹配到表") ||
                                   finalOutput.contains("新建表") ||
                                   finalOutput.contains("已存在表") ||
                                   finalOutput.contains("未匹配到表");

        // 从输出中提取表名
        List<String> tableNames = extractTableNames(finalOutput);
        List<String> newTableNames = extractNewTableNames(finalOutput);

        // 如果没有提取到表名且没有新建表，且没有数据库相关关键词，则认为不是数据库相关的需求
        if (!isDatabaseRelated && tableNames.isEmpty() && newTableNames.isEmpty()) {
            log.info("========== [Stage: {}] 需求与数据库不相关，终止DDL生成流程 ==========", getStageName());
            // 使用空字符串作为输出，避免重复发送相同内容
            return Mono.just(StageResult.stopWith(""));
        }

        // 如果是“未匹配到表”的情况，但没有新建表，则终止流程
        if (finalOutput.contains("未匹配到表") && newTableNames.isEmpty()) {
            log.info("========== [Stage: {}] 未匹配到表且没有新建表，终止DDL生成流程 ==========", getStageName());
            // 使用空字符串作为输出，避免重复发送相同内容
            return Mono.just(StageResult.stopWith(""));
        }

        // 如果是“已存在表”的情况，说明用户想创建的表已经存在，直接终止流程
        if (finalOutput.contains("已存在表")) {
            log.info("========== [Stage: {}] 检测到已存在表，为避免覆盖现有表，终止DDL生成流程 ==========", getStageName());
            // 使用空字符串作为输出，避免重复发送相同内容
            return Mono.just(StageResult.stopWith(""));
        }

        // 如果是数据库相关的需求，将表名列表添加到结果中
        log.info("========== [Stage: {}] 需求与数据库相关，提取到现有表: {}, 新表: {} ==========",
                getStageName(), tableNames, newTableNames);

        // 创建结果列表，包含现有表和新表
        List<String> allTables = new ArrayList<>(tableNames);
        allTables.addAll(newTableNames);

        // 如果没有提取到任何表，但是有数据库相关关键词，仍然继续流程
        if (allTables.isEmpty() && isDatabaseRelated) {
            log.info("========== [Stage: {}] 检测到数据库相关关键词，继续DDL生成流程 ==========", getStageName());
        }

        // 将表名列表添加到结果中，传递给下一阶段
        return Mono.just(StageResult.continueWith(finalOutput, allTables));
    }

    /**
     * 从分析结果中提取现有表名
     *
     * @param analysisOutput AI分析的输出结果
     * @return 提取到的表名列表
     */
    private List<String> extractTableNames(String analysisOutput) {
        List<String> tableNames = new ArrayList<>();

        // 获取所有可用的表
        Schema schema = metabaseService.getSchemaWithDatabase();
        Map<String, Table> availableTables = schema.getTables();

        // 尝试从标准格式中提取“匹配到表”
        if (analysisOutput.contains("匹配到表")) {
            int startIndex = analysisOutput.indexOf("匹配到表") + 5; // "匹配到表"的长度为5
            int endIndex = analysisOutput.indexOf("]", startIndex);
            if (startIndex > 5 && endIndex > startIndex) {
                String tablesStr = analysisOutput.substring(startIndex, endIndex + 1);
                // 处理格式如 "[表名1, 表名2, ...]"
                tablesStr = tablesStr.replaceAll("[\\[\\]\\s]", ""); // 移除方括号和空格
                String[] tableArray = tablesStr.split(",");
                for (String tableName : tableArray) {
                    if (!tableName.trim().isEmpty() && availableTables.containsKey(tableName.trim())) {
                        tableNames.add(tableName.trim());
                    }
                }
            }
        }

        // 尝试从标准格式中提取“已存在表”
        if (analysisOutput.contains("已存在表")) {
            int startIndex = analysisOutput.indexOf("已存在表") + 5; // "已存在表"的长度为5
            int endIndex = analysisOutput.indexOf("]", startIndex);
            if (startIndex > 5 && endIndex > startIndex) {
                String tablesStr = analysisOutput.substring(startIndex, endIndex + 1);
                tablesStr = tablesStr.replaceAll("[\\[\\]\\s]", "");
                String[] tableArray = tablesStr.split(",");
                for (String tableName : tableArray) {
                    if (!tableName.trim().isEmpty() && availableTables.containsKey(tableName.trim())) {
                        tableNames.add(tableName.trim());
                    }
                }
            }
        }

        // 如果从标准格式中没有提取到，则尝试从表格中提取
        if (tableNames.isEmpty() && analysisOutput.contains("|")) {
            // 尝试从表格中提取表名
            String[] lines = analysisOutput.split("\n");
            for (String line : lines) {
                if (line.trim().startsWith("|") && line.contains("|")) {
                    String[] cells = line.split("\\|");
                    if (cells.length >= 2) {
                        String potentialTableName = cells[1].trim();
                        if (!potentialTableName.equals("表名") && !potentialTableName.equals("现有表") &&
                            !potentialTableName.contains("-----") && availableTables.containsKey(potentialTableName)) {
                            tableNames.add(potentialTableName);
                        }
                    }
                }
            }
        }

        // 如果还是没有提取到，则尝试从整个输出中查找
        if (tableNames.isEmpty()) {
            for (String tableName : availableTables.keySet()) {
                if (analysisOutput.contains(tableName)) {
                    tableNames.add(tableName);
                }
            }
        }

        log.info("========== [Stage: {}] 提取到的现有表名: {} ==========", getStageName(), tableNames);
        return tableNames;
    }

    /**
     * 从分析结果中提取新建表名
     *
     * @param analysisOutput AI分析的输出结果
     * @return 提取到的新表名列表
     */
    private List<String> extractNewTableNames(String analysisOutput) {
        List<String> newTableNames = new ArrayList<>();

        // 尝试从标准格式中提取
        if (analysisOutput.contains("新建表")) {
            // 先检查是否在行首或者在引号后面
            int startIndex = -1;

            // 尝试在行首找到“新建表”
            String[] lines = analysisOutput.split("\n");
            for (String line : lines) {
                String trimmedLine = line.trim();
                if (trimmedLine.startsWith("新建表")) {
                    startIndex = analysisOutput.indexOf(trimmedLine) + 4; // "新建表"的长度为4
                    break;
                }
            }

            // 如果在行首没有找到，就尝试在整个文本中找
            if (startIndex == -1) {
                startIndex = analysisOutput.indexOf("新建表") + 4;
            }

            int endIndex = analysisOutput.indexOf("]", startIndex);
            if (startIndex > 4 && endIndex > startIndex) {
                String tablesStr = analysisOutput.substring(startIndex, endIndex + 1);
                // 处理格式如 "[表名1, 表名2, ...]"
                tablesStr = tablesStr.replaceAll("[\\[\\]\\s]", ""); // 移除方括号和空格
                String[] tableArray = tablesStr.split(",");
                for (String tableName : tableArray) {
                    if (!tableName.trim().isEmpty()) {
                        newTableNames.add(tableName.trim());
                    }
                }
            }
        }

        // 如果从标准格式中没有提取到，则尝试从文本中提取
        if (newTableNames.isEmpty()) {
            // 尝试从“我将为您创建”或“我将为您创建以下新表”等文本中提取
            String[] patterns = {
                "我将为您创建以下新表",
                "我将为您创建全新的表结构",
                "我将为您创建"
            };

            for (String pattern : patterns) {
                if (analysisOutput.contains(pattern)) {
                    int startIndex = analysisOutput.indexOf(pattern) + pattern.length();
                    // 尝试找到方括号内的内容
                    int openBracket = analysisOutput.indexOf("[", startIndex);
                    int closeBracket = analysisOutput.indexOf("]", openBracket);
                    if (openBracket > 0 && closeBracket > openBracket) {
                        String tablesStr = analysisOutput.substring(openBracket + 1, closeBracket);
                        String[] tableArray = tablesStr.split(",");
                        for (String tableName : tableArray) {
                            if (!tableName.trim().isEmpty()) {
                                newTableNames.add(tableName.trim());
                            }
                        }
                        break; // 找到了就不继续循环
                    }
                }
            }
        }

        log.info("========== [Stage: {}] 提取到的新建表名: {} ==========", getStageName(), newTableNames);
        return newTableNames;
    }
}