package com.cpit.chatbi.metabase.domain.er;

import com.alibaba.fastjson.annotation.JSONField;

public class ColumnInfo {
    @JSONField(ordinal = 1)
    private String name;

    @JSONField(ordinal = 2)
    private String columnType;

    @J<PERSON><PERSON>ield(ordinal = 3)
    private boolean primaryKey;

    @JSO<PERSON>ield(ordinal = 4)
    private String comment;

    @JSO<PERSON>ield(ordinal = 5)
    private boolean foreignKey;

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getColumnType() {
        return columnType;
    }

    public void setColumnType(String columnType) {
        this.columnType = columnType;
    }

    public boolean isPrimaryKey() {
        return primaryKey;
    }

    public void setPrimaryKey(boolean primaryKey) {
        this.primaryKey = primaryKey;
    }

    public String getComment() {
        return comment;
    }

    public void setComment(String comment) {
        this.comment = comment;
    }

    public boolean isForeignKey() {
        return foreignKey;
    }

    public void setForeignKey(boolean foreignKey) {
        this.foreignKey = foreignKey;
    }
}