package com.cpit.chatbi.metabase.util;

/**
 * 通用返回结果实体类
 *
 * @param <T> 返回数据类型
 */
public class Result<T> {
    /**
     * 成功状态码
     */
    public static final int SUCCESS = 1;

    /**
     * 失败状态码
     */
    public static final int ERROR = 0;

    /**
     * 返回数据（可以是任意类型）
     */
    private T result;

    /**
     * 状态码
     */
    private int code;

    /**
     * 返回消息
     */
    private String message;

    /**
     * 是否成功
     */
    private boolean success;

    /**
     * 构造方法
     *
     * @param result    返回数据
     * @param code    状态码
     * @param message 返回消息
     * @param success 是否成功
     */
    public Result(T result, int code, String message, boolean success) {
        this.result = result;
        this.code = code;
        this.message = message;
        this.success = success;
    }

    // 静态工厂方法

    /**
     * 创建成功结果（无数据）
     */
    public static <T> Result<T> success() {
        return new Result<>(null, SUCCESS, "操作成功", true);
    }

    /**
     * 创建成功结果（带数据）
     */
    public static <T> Result<T> success(T data) {
        return new Result<>(data, SUCCESS, "操作成功", true);
    }

    /**
     * 创建成功结果（带数据和自定义消息）
     */
    public static <T> Result<T> success(T data, String message) {
        return new Result<>(data, SUCCESS, message, true);
    }

    /**
     * 创建失败结果（默认错误码）
     */
    public static <T> Result<T> fail(String message) {
        return new Result<>(null, ERROR, message, false);
    }

    /**
     * 创建失败结果（自定义错误码）
     */
    public static <T> Result<T> fail(int code, String message) {
        return new Result<>(null, code, message, false);
    }

    // Getter和Setter方法

    public T getResult() {
        return result;
    }

    public void setResult(T result) {
        this.result = result;
    }

    public int getCode() {
        return code;
    }

    public void setCode(int code) {
        this.code = code;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public boolean isSuccess() {
        return success;
    }

    public void setSuccess(boolean success) {
        this.success = success;
    }

    @Override
    public String toString() {
        return "Result{" +
                "data=" + result +
                ", code=" + code +
                ", message='" + message + '\'' +
                ", success=" + success +
                '}';
    }
}
