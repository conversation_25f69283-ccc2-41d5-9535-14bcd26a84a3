package com.cpit.chatbi.metabase.domain.chat;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 包装ChatResponse的类，用于与前端格式兼容
 */
@Data
public class ChatResponseWrapper {
    private Result result;
    private Metadata metadata;
    private List<Result> results;

    public ChatResponseWrapper() {
        this.metadata = new Metadata();
        this.results = new ArrayList<>();
    }

    // 用于跟踪每个阶段的当前内容
    private static final Map<String, String> stageContentMap = new ConcurrentHashMap<>();

    public static ChatResponseWrapper fromDdlTaskResponse(DdlTaskResponse response) {
        ChatResponseWrapper wrapper = new ChatResponseWrapper();

        // 创建输出
        Output output = new Output();

        // 获取增量内容
        String incrementalContent = getIncrementalContent(response);

        output.setText(incrementalContent);
        output.setMessageType("ASSISTANT");
        output.setMetadata(Collections.singletonMap("messageType", "ASSISTANT"));

        // 创建结果
        Result result = new Result();
        result.setOutput(output);

        // 设置结果元数据
        ResultMetadata resultMetadata = new ResultMetadata();
        // 如果是thinking状态，设置empty为true
        if ("thinking".equals(response.getStatus())) {
            resultMetadata.setEmpty(true);
        } else {
            resultMetadata.setEmpty(false);
            // 如果是complete状态，并且是最后一个阶段，设置finishReason为stop
            if ("complete".equals(response.getStatus())) {
                // 只有在最后一个阶段（validation或sql）完成时才设置finishReason为stop generation
                if ("generation".equals(response.getStage()) || "sql".equals(response.getStage())) {
                    resultMetadata.setFinishReason("stop");
                } else {
                    // 对于其他阶段，设置为stage_complete表示阶段完成但整体流程未结束
                    resultMetadata.setFinishReason("stage_complete");
                }
            } else if ("stopped".equals(response.getStatus())) {
                // 如果状态是stopped，说明流程提前终止，也应该设置finishReason为stop
                resultMetadata.setFinishReason("stop");
            } else if ("error".equals(response.getStatus())) {
                resultMetadata.setFinishReason("error");
            }
        }
        result.setMetadata(resultMetadata);

        // 设置主要结果
        wrapper.setResult(result);

        // 添加到结果列表
        wrapper.getResults().add(result);

        // 设置元数据
        wrapper.getMetadata().setId(response.getId());
        wrapper.getMetadata().setEmpty(false);

        // 将阶段信息添加到元数据中
        wrapper.getMetadata().setStage(response.getStage());
        wrapper.getMetadata().setStatus(response.getStatus());

        // 如果有数据，处理不同类型的数据
        if (response.getData() != null) {
            // 如果是字符串类型，先尝试提取SQL
            String dataStr = String.valueOf(response.getData());

            // 如果是生成阶段完成，并且data包含完整内容，尝试提取SQL
            if ("generation".equals(response.getStage()) && "complete".equals(response.getStatus())) {
                String extractedSql = com.cpit.chatbi.metabase.util.SqlExtractorUtils.extractSql(dataStr);
                if (com.cpit.chatbi.metabase.util.SqlExtractorUtils.validSql(extractedSql)) {
                    wrapper.getMetadata().setSql(extractedSql);
                } else {
                    wrapper.getMetadata().setSql(dataStr);
                }
            } else {
                // 其他情况下直接设置数据
                wrapper.getMetadata().setSql(dataStr);
            }
        }

        return wrapper;
    }

    /**
     * 获取增量内容，避免重复
     */
    private static String getIncrementalContent(DdlTaskResponse response) {
        String stage = response.getStage();
        String currentContent = response.getContent();

        // 如果是新阶段或内容为空，直接返回当前内容
        // 注意：现在我们在AbstractDdlStage中已经格式化了消息，这里不需要再添加前缀
        if (!stageContentMap.containsKey(stage) || stageContentMap.get(stage) == null) {
            stageContentMap.put(stage, currentContent);
            return currentContent;
        }

        // 获取上一次的内容
        String previousContent = stageContentMap.get(stage);

        // 如果当前内容比上一次的短，说明是新的开始，直接返回当前内容
        if (currentContent.length() < previousContent.length()) {
            stageContentMap.put(stage, currentContent);
            return currentContent;
        }

        // 如果当前内容与上一次相同，返回空字符串
        if (currentContent.equals(previousContent)) {
            return "";
        }

        // 如果当前内容包含上一次的内容，返回增量部分
        if (currentContent.startsWith(previousContent)) {
            String incrementalPart = currentContent.substring(previousContent.length());
            stageContentMap.put(stage, currentContent);
            return incrementalPart;
        }

        // 如果不包含，说明是完全不同的内容，返回当前内容
        stageContentMap.put(stage, currentContent);
        return currentContent;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Result {
        private ResultMetadata metadata;
        private Output output;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ResultMetadata {
        private String finishReason;
        private List<Object> contentFilters = new ArrayList<>();
        private boolean empty = true;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Output {
        private String messageType;
        private Object metadata;
        private List<Object> toolCalls = new ArrayList<>();
        private List<Object> media = new ArrayList<>();
        private String text;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Metadata {
        private String id = "";
        private String model = "qwen2.5-coder";
        private RateLimit rateLimit = new RateLimit();
        private Usage usage = new Usage();
        private List<Object> promptMetadata = new ArrayList<>();
        private boolean empty = false;
        private String sql; // 添加SQL字段，用于存储生成的SQL
        private String stage; // 添加阶段字段，用于存储当前阶段
        private String status; // 添加状态字段，用于存储当前状态
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class RateLimit {
        private int requestsRemaining = 0;
        private double tokensReset = 0.0;
        private double requestsReset = 0.0;
        private int requestsLimit = 0;
        private int tokensLimit = 0;
        private int tokensRemaining = 0;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Usage {
        private int promptTokens = 0;
        private int completionTokens = 0;
        private int totalTokens = 0;
        private int generationTokens = 0;
    }
}
