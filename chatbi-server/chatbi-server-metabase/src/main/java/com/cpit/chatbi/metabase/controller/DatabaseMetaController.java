package com.cpit.chatbi.metabase.controller;

import com.cpit.chatbi.metabase.domain.request.SqlExecuteRequest;
import com.cpit.chatbi.metabase.service.MetabaseService;
import com.cpit.chatbi.metabase.util.ResultUtil;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/api/meta")
@Tag(name = "数据库元数据接口", description = "提供数据库元数据查询和SQL执行功能")
public class DatabaseMetaController {

    @Autowired
    private MetabaseService metabaseService;

    @GetMapping("/er-diagram")
    public String getErDiagram() {
        return ResultUtil.successWithData(metabaseService.getSchema());
    }

    @PostMapping("/execute-sql")
    @Operation(
        summary = "执行SQL语句",
        description = "执行用户提供的SQL语句，支持DDL和DML操作"
    )
    public String executeSql(@RequestBody SqlExecuteRequest request) {
        if (request.getSql() == null || request.getSql().trim().isEmpty()) {
            return ResultUtil.errorWithMessage("请提供SQL语句");
        }
        return metabaseService.executeDdlSql(request.getSql());
    }
}