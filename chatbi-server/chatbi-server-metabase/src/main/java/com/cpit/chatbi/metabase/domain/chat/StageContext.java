package com.cpit.chatbi.metabase.domain.chat;

import lombok.Data;

/**
 * 阶段上下文，用于在多轮调用之间传递信息
 */
@Data
public class StageContext {
    private final String systemPrompt;
    private final String chatId;
    private final String userId;
    private final String userMessage;
    private String previousStageOutput;

    public StageContext(String systemPrompt, String chatId, String userId, String userMessage) {
        this.systemPrompt = systemPrompt;
        this.chatId = chatId;
        this.userId = userId;
        this.userMessage = userMessage;
        this.previousStageOutput = null;
    }

    /**
     * 创建一个新的上下文，包含前一阶段的输出
     * @param previousStageOutput 前一阶段的输出
     * @return 新的上下文
     */
    public StageContext withPreviousStageOutput(String previousStageOutput) {
        StageContext newContext = new StageContext(this.systemPrompt, this.chatId, this.userId, this.userMessage);
        newContext.setPreviousStageOutput(previousStageOutput);
        return newContext;
    }
}
