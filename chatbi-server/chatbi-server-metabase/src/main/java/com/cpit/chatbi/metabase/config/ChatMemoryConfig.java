package com.cpit.chatbi.metabase.config;

import com.cpit.chatbi.metabase.memory.ExtendedChatMemory;
import com.cpit.chatbi.metabase.memory.ExtendedInMemoryChatMemory;
import com.cpit.chatbi.metabase.memory.MetadataAwareChatMemoryAdvisor;
import com.cpit.chatbi.metabase.memory.RedisChatMemory;
import org.springframework.ai.chat.client.ChatClient;
import org.springframework.ai.chat.client.advisor.MessageChatMemoryAdvisor;
import org.springframework.ai.chat.messages.Message;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.io.Resource;
import org.springframework.data.redis.core.RedisTemplate;

import java.io.IOException;
import java.nio.charset.StandardCharsets;


@Configuration
public class ChatMemoryConfig {

    @Bean
    @ConditionalOnProperty(name = "spring.data.redis.host", havingValue = "false", matchIfMissing = true)
    public ExtendedChatMemory inMemoryChatMemory() {
        return new ExtendedInMemoryChatMemory();
    }

    @Bean
    @ConditionalOnProperty(name = "spring.data.redis.host")
    public ExtendedChatMemory redisChatMemory(
            @Qualifier("messageRedisTemplate") RedisTemplate<String, Message> messageRedisTemplate,
            @Qualifier("redisTemplate") RedisTemplate<String, String> stringRedisTemplate) {
        return new RedisChatMemory(messageRedisTemplate, stringRedisTemplate);
    }

    // 使用新的 Advisor
    @Bean
    public MetadataAwareChatMemoryAdvisor metadataAwareChatMemoryAdvisor(ExtendedChatMemory chatMemory) {
        // 确保 chatMemory 实现了 Spring AI 的 ChatMemory 接口
        return new MetadataAwareChatMemoryAdvisor(chatMemory);
    }

    // 将新的 Advisor 注入 ChatClient
    @Bean
    public ChatClient chatClient(ChatClient.Builder builder, MetadataAwareChatMemoryAdvisor advisor) throws IOException {
        return builder
                .defaultAdvisors(advisor) // 使用新的 Advisor
                .build();
    }

}