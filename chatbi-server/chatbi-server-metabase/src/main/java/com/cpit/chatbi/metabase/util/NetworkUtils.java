package com.cpit.chatbi.metabase.util;

import lombok.extern.slf4j.Slf4j;
import java.net.InetAddress;
import java.net.NetworkInterface;
import java.util.Enumeration;

/**
 * 网络工具类
 */
@Slf4j
public class NetworkUtils {

    /**
     * 获取本地IP地址
     * 
     * @return 本地IP地址，如果获取失败则返回localhost
     */
    public static String getLocalIp() {
        try {
            // 优先获取非回环地址
            Enumeration<NetworkInterface> networkInterfaces = NetworkInterface.getNetworkInterfaces();
            while (networkInterfaces.hasMoreElements()) {
                NetworkInterface networkInterface = networkInterfaces.nextElement();
                // 排除回环接口、虚拟接口等
                if (networkInterface.isLoopback() || networkInterface.isVirtual() || !networkInterface.isUp()) {
                    continue;
                }

                Enumeration<InetAddress> addresses = networkInterface.getInetAddresses();
                while (addresses.hasMoreElements()) {
                    InetAddress address = addresses.nextElement();
                    // 只获取IPv4地址
                    if (address.getHostAddress().contains(".")) {
                        return address.getHostAddress();
                    }
                }
            }

            // 如果没有找到合适的地址，则获取本地主机地址
            return InetAddress.getLocalHost().getHostAddress();
        } catch (Exception e) {
            log.warn("获取本地IP地址失败", e);
            return "localhost";
        }
    }
}
