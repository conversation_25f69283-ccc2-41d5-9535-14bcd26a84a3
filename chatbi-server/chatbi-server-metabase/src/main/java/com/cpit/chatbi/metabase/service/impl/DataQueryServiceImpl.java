package com.cpit.chatbi.metabase.service.impl;

import com.cpit.chatbi.metabase.domain.dto.QueryResult;
import com.cpit.chatbi.metabase.service.DataQueryService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import  com.cpit.chatbi.metabase.domain.dto.ColumnInfo;

import javax.sql.DataSource; // Use standard DataSource
import java.sql.*;
import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;


@Slf4j
@Service
@RequiredArgsConstructor // Use constructor injection
public class DataQueryServiceImpl implements DataQueryService {

    private final DataSource dataSource; // Inject standard DataSource

    // Consider adding limits or using JdbcTemplate for potentially safer execution
    // private final JdbcTemplate jdbcTemplate;

    @Override
    public QueryResult executeQuery(String selectSql) {
        log.info("Executing SQL query: {}", selectSql);
        // !!! --- SECURITY WARNING --- !!!
        // Executing SQL generated by an LLM carries significant security risks (SQL Injection).
        // In a production environment, you MUST implement robust security measures:
        // 1. Use a dedicated read-only database user for these queries.
        // 2. Validate the SQL string: Ensure it's ONLY a SELECT statement, contains no malicious patterns (DROP, DELETE, UPDATE, system calls, etc.). Regex or parsing can help but is complex.
        // 3. Consider resource limits (query timeout, max rows) on the database side or via JdbcTemplate settings.
        // 4. NEVER grant write permissions to the user executing these queries.
        // 5. Consider using a query virtualization layer or semantic layer if possible, which adds abstraction.
        // !!! --- END SECURITY WARNING --- !!!

        // Basic validation (add more robust checks)
        if (selectSql == null || selectSql.trim().isEmpty() || !selectSql.trim().toLowerCase().startsWith("select")) {
            log.error("Invalid SQL provided: Not a SELECT statement or empty.");
            return new QueryResult(selectSql, "Invalid query: Only SELECT statements are allowed.");
        }


        List<Map<String, Object>> data = new ArrayList<>();
        List<ColumnInfo> columns = new ArrayList<>();

        try (Connection conn = dataSource.getConnection(); // Get connection from injected DataSource
             Statement stmt = conn.createStatement(); // Use Statement for dynamic SQL (less safe than PreparedStatement)
             ResultSet rs = stmt.executeQuery(selectSql)) {

            ResultSetMetaData metaData = rs.getMetaData();
            int columnCount = metaData.getColumnCount();

            // Get column info
            for (int i = 1; i <= columnCount; i++) {
                columns.add(new ColumnInfo(
                        metaData.getColumnLabel(i), // Use getColumnLabel for potential AS aliases
                        metaData.getColumnTypeName(i),
                        metaData.getColumnType(i)
                ));
            }

            // Get data rows
            while (rs.next()) {
                Map<String, Object> row = new LinkedHashMap<>(); // Preserve column order
                for (ColumnInfo col : columns) {
                    // Use getObject which handles most types reasonably well
                    Object value = rs.getObject(col.getName());
                    // Optional: Handle specific types like BLOB/CLOB if necessary
                    // if (value instanceof Clob) { value = clobToString((Clob) value); }
                    // if (value instanceof Blob) { value = blobToBytes((Blob) value); }
                    row.put(col.getName(), value);
                }
                data.add(row);
            }
            log.info("SQL query executed successfully, fetched {} rows.", data.size());
            return new QueryResult(data, columns, selectSql);

        } catch (SQLException e) {
            log.error("Failed to execute SQL query: {}", selectSql, e);
            // Provide a user-friendly error message, avoid leaking sensitive DB details
            String userFriendlyError = "Database query failed: " + e.getMessage();
            // You might want to map common SQLException codes to better messages
            return new QueryResult(selectSql, userFriendlyError);
        } catch (Exception e) {
            log.error("Unexpected error during SQL query execution: {}", selectSql, e);
            return new QueryResult(selectSql, "An unexpected internal error occurred during query execution.");
        }
    }
    // Optional helper methods for CLOB/BLOB if needed
    // private String clobToString(Clob data) { ... }
    // private byte[] blobToBytes(Blob data) { ... }
}
