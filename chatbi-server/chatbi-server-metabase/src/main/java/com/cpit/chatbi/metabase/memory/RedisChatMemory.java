package com.cpit.chatbi.metabase.memory;

import com.cpit.chatbi.metabase.domain.chat.ChatSession;
import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import org.springframework.ai.chat.messages.AssistantMessage;
import org.springframework.ai.chat.messages.Message;
import org.springframework.data.redis.core.RedisTemplate;

import java.time.LocalDate;
import java.util.*;
import java.util.stream.Collectors;
import java.util.Map;


@Slf4j
public class RedisChatMemory implements ExtendedChatMemory {
    private static final String KEY_PREFIX = "chat:history:";
    public static final String CHAT_SESSION_PREFIX = "chat:session:";
    private static final int MAX_SESSIONS_PER_USER = 1000;

    private final RedisTemplate<String, Message> messageRedisTemplate;
    private final RedisTemplate<String, String> stringRedisTemplate;

    public RedisChatMemory(RedisTemplate<String, Message> messageRedisTemplate,
                           RedisTemplate<String, String> stringRedisTemplate) {
        this.messageRedisTemplate = messageRedisTemplate;
        this.stringRedisTemplate = stringRedisTemplate;
    }

    @Override
    public void add(String conversationId, List<Message> messages) {
        String key = KEY_PREFIX + conversationId;
        // 存储到 Redis
        messageRedisTemplate.opsForList().rightPushAll(key, messages);
    }

    @Override
    public List<Message> get(String conversationId, int lastN) {
        String key = KEY_PREFIX + conversationId;
        // 从 Redis 获取最新的 lastN 条消息
        List<Message> serializedMessages = messageRedisTemplate.opsForList().range(key, -lastN, -1);
        if (serializedMessages != null) {
            return serializedMessages;
        }
        return List.of();
    }

    @Override
    public void clear(String conversationId) {
        messageRedisTemplate.delete(KEY_PREFIX + conversationId);
    }

    /**
     * 保存会话
     *
     * @param chatSession 会话
     */
    @Override
    public void saveSession(ChatSession chatSession, String userId) {
        try {
            if (chatSession == null) {
                log.warn("尝试保存空会话对象");
                return;
            }
            if (userId == null || userId.trim().isEmpty()) {
                log.warn("用户ID为空，无法保存会话");
                return;
            }
            String key = CHAT_SESSION_PREFIX + userId;
            // 设置创建时间
            if (chatSession.getCreateTime() == null) {
                chatSession.initCreateTime();
            }
            // 转 JSON 后直接插入左侧
            String sessionJson = JSON.toJSONString(chatSession);
            stringRedisTemplate.opsForList().leftPush(key, sessionJson);
            // 限制总长度，最多保留 MAX_SESSIONS_PER_USER 条
            stringRedisTemplate.opsForList().trim(key, 0, MAX_SESSIONS_PER_USER - 1);
            log.debug("成功保存会话: {}, 用户: {}", chatSession.getChatId(), userId);
        } catch (Exception e) {
            log.error("保存会话时发生异常", e);
        }
    }

    @Override
    public List<ChatSession> getSessions(String userId) {
        try {
            String key = CHAT_SESSION_PREFIX + userId;
            List<String> strings = stringRedisTemplate.opsForList().range(key, 0, -1);
            if (strings == null || strings.isEmpty()) {
                return List.of();
            }

            List<ChatSession> sessions = strings.stream()
                    .filter(s -> s != null)
                    .map(s -> {
                        try {
                            return JSON.parseObject(s, ChatSession.class);
                        } catch (Exception e) {
                            log.error("解析会话 JSON 失败: {}", s, e);
                            return null;
                        }
                    })
                    .filter(s -> s != null)
                    .collect(Collectors.toList());

            // 按创建时间降序排序，最新的在前
            try {
                sessions.sort(Collections.reverseOrder(Comparator.comparing(ChatSession::getCreateTime,
                        Comparator.nullsLast(Comparator.naturalOrder()))));
            } catch (Exception e) {
                log.error("排序会话列表失败", e);
            }

            return sessions;
        } catch (Exception e) {
            log.error("获取会话列表时发生异常", e);
            return List.of();
        }
    }

    @Override
    public Map<LocalDate, List<ChatSession>> getSessionsByDate(String userId) {
        try {
            List<ChatSession> sessions = getSessions(userId);
            return sessions.stream()
                    .filter(session -> session.getCreateTime() != null)
                    .collect(Collectors.groupingBy(
                            session -> session.getCreateTime().toLocalDate(),
                            Collectors.toList()
                    ));
        } catch (Exception e) {
            log.error("按日期分组会话列表时发生异常", e);
            return Collections.emptyMap();
        }
    }

/*
    @Override
    public List<Message> getMessagesByPage(String conversationId, int page, int size) {
        try {
            String key = KEY_PREFIX + conversationId;

            // 计算起始和结束索引
            // Redis列表是从左到右的，最近的消息在右边（即列表的尾部）
            // 所以我们需要从右到左计算分页
            Long total = messageRedisTemplate.opsForList().size(key);
            if (total == null || total == 0) {
                return List.of();
            }

            // 获取所有消息，然后进行过滤
            List<Message> allMessages = messageRedisTemplate.opsForList().range(key, 0, -1);
            if (allMessages == null || allMessages.isEmpty()) {
                return List.of();
            }

            // 过滤掉中间阶段的系统提问
            List<Message> filteredMessages = allMessages.stream()
                .filter(message -> {
                    // 检查元数据
                    Map<String, Object> metadata = message.getMetadata();
                    if (metadata != null) {
                        // 如果是生成阶段的系统提问，则过滤掉
                        Object stage = metadata.get("stage");
                        Object metaMessageType = metadata.get("messageType");

                        // 如果是生成阶段的系统提问，则过滤掉
                        if (stage != null && "generation".equals(stage.toString()) &&
                            metaMessageType != null && "USER".equals(metaMessageType.toString())) {
                            return false;
                        }
                    }
                    return true;
                })
                .collect(Collectors.toList());

            // 计算过滤后的总数
            int filteredTotal = filteredMessages.size();

            // 计算分页
            int start = Math.max(0, filteredTotal - (page + 1) * size);
            int end = Math.min(filteredTotal, filteredTotal - page * size);

            // 处理边界情况
            if (start >= end) {
                return List.of();
            }

            // 返回分页结果
            return filteredMessages.subList(start, end);
        } catch (Exception e) {
            log.error("获取分页消息时发生异常", e);
            return List.of();
        }
    }
*/

    @Override
    public List<Message> getMessagesByPage(String conversationId, int page, int size) {
        try {
            String key = KEY_PREFIX + conversationId;

            // 计算起始和结束索引
            // Redis列表是从左到右的，最近的消息在右边（即列表的尾部）
            // 所以我们需要从右到左计算分页
            Long total = messageRedisTemplate.opsForList().size(key);
            if (total == null || total == 0) {
                return List.of();
            }

            // 获取所有消息，然后进行过滤
            List<Message> allMessages = messageRedisTemplate.opsForList().range(key, 0, -1);
            if (allMessages == null || allMessages.isEmpty()) {
                return List.of();
            }

            // 过滤掉中间阶段的系统提问
            List<Message> filteredMessages = allMessages.stream()
                    .filter(message -> {
                        // 检查元数据
                        Map<String, Object> metadata = message.getMetadata();
                        if (metadata != null) {
                            // 如果是生成阶段的系统提问，则过滤掉
                            Object stage = metadata.get("stage");
                            Object metaMessageType = metadata.get("messageType");

                            // 如果是生成阶段的系统提问，则过滤掉
                            if (stage != null && "generation".equals(stage.toString()) &&
                                    metaMessageType != null && "USER".equals(metaMessageType.toString())) {
                                return false;
                            }
                        }
                        return true;
                    })
                    .collect(Collectors.toList());

            // 合并相同questionId的ASSISTANT消息
            Map<String, List<Message>> questionIdToAssistantMessages = new HashMap<>();
            List<Message> mergedMessages = new ArrayList<>();

            for (Message message : filteredMessages) {
                if ("ASSISTANT".equals(message.getMessageType().toString())) {
                    Map<String, Object> metadata = message.getMetadata();
                    if (metadata != null && metadata.containsKey("questionId")) {
                        String questionId = metadata.get("questionId").toString();
                        questionIdToAssistantMessages.computeIfAbsent(questionId, k -> new ArrayList<>()).add(message);
                    } else {
                        // 如果没有questionId，直接添加到结果列表
                        mergedMessages.add(message);
                    }
                } else {
                    // 非ASSISTANT消息直接添加到结果列表
                    mergedMessages.add(message);
                }
            }

            // 处理相同questionId的ASSISTANT消息
            for (List<Message> assistantMessages : questionIdToAssistantMessages.values()) {
                if (assistantMessages.size() == 1) {
                    // 只有一条消息，直接添加
                    mergedMessages.add(assistantMessages.get(0));
                } else {
                    // 多条消息，合并text内容
                    Message firstMessage = assistantMessages.get(0);
                    StringBuilder mergedText = new StringBuilder();

                    // 按照时间戳排序消息
                    assistantMessages.sort(Comparator.comparing(msg ->
                            Long.parseLong(msg.getMetadata().getOrDefault("timestamp", "0").toString())));

                    // 合并所有消息的text内容
                    for (Message msg : assistantMessages) {
                        if (mergedText.length() > 0) {
                            mergedText.append("\n\n");
                        }
                        mergedText.append(msg.getText());
                    }

                    // 创建一个新的AssistantMessage，包含合并后的text内容和第一条消息的元数据
                    AssistantMessage mergedMessage = new AssistantMessage(mergedText.toString(), firstMessage.getMetadata());
                    mergedMessages.add(mergedMessage);
                }
            }

            // 按照时间戳排序合并后的消息
            mergedMessages.sort(Comparator.comparing(msg -> {
                Object timestamp = msg.getMetadata().getOrDefault("timestamp", "0");
                return Long.parseLong(timestamp.toString());
            }));

            // 计算过滤后的总数
            int filteredTotal = mergedMessages.size();

            // 计算分页
            int start = Math.max(0, filteredTotal - (page + 1) * size);
            int end = Math.min(filteredTotal, filteredTotal - page * size);

            // 处理边界情况
            if (start >= end) {
                return List.of();
            }

            // 返回分页结果
            return mergedMessages.subList(start, end);
        } catch (Exception e) {
            log.error("获取分页消息时发生异常", e);
            return List.of();
        }
    }

    @Override
    public long getMessagesCount(String conversationId) {
        try {
            String key = KEY_PREFIX + conversationId;

            // 获取所有消息
            List<Message> allMessages = messageRedisTemplate.opsForList().range(key, 0, -1);
            if (allMessages == null || allMessages.isEmpty()) {
                return 0;
            }

            // 过滤掉中间阶段的系统提问
            long filteredCount = allMessages.stream()
                .filter(message -> {
                    // 检查元数据
                    Map<String, Object> metadata = message.getMetadata();
                    if (metadata != null) {
                        // 如果是生成阶段的系统提问，则过滤掉
                        Object stage = metadata.get("stage");
                        Object metaMessageType = metadata.get("messageType");

                        // 如果是生成阶段的系统提问，则过滤掉
                        if (stage != null && "generation".equals(stage.toString()) &&
                            metaMessageType != null && "USER".equals(metaMessageType.toString())) {
                            return false;
                        }
                    }
                    return true;
                })
                .count();

            return filteredCount;
        } catch (Exception e) {
            log.error("获取消息总数时发生异常", e);
            return 0;
        }
    }

    @Override
    public boolean deleteSession(String chatId, String userId) {
        try {
            if (chatId == null || chatId.trim().isEmpty()) {
                log.warn("会话ID为空，无法删除会话");
                return false;
            }
            if (userId == null || userId.trim().isEmpty()) {
                log.warn("用户ID为空，无法删除会话");
                return false;
            }

            String key = CHAT_SESSION_PREFIX + userId;
            List<String> strings = stringRedisTemplate.opsForList().range(key, 0, -1);
            if (strings == null || strings.isEmpty()) {
                log.warn("用户没有会话记录: {}", userId);
                return false;
            }

            boolean found = false;
            for (String sessionJson : strings) {
                try {
                    ChatSession session = JSON.parseObject(sessionJson, ChatSession.class);
                    if (session != null && chatId.equals(session.getChatId())) {
                        // 找到匹配的会话，从 Redis 中删除
                        stringRedisTemplate.opsForList().remove(key, 1, sessionJson);
                        found = true;

                        // 同时删除会话的消息历史
                        messageRedisTemplate.delete(KEY_PREFIX + chatId);

                        log.debug("成功删除会话: {}, 用户: {}", chatId, userId);
                        break;
                    }
                } catch (Exception e) {
                    log.error("解析会话JSON失败: {}", sessionJson, e);
                }
            }

            if (!found) {
                log.warn("未找到指定的会话: {}, 用户: {}", chatId, userId);
            }

            return found;
        } catch (Exception e) {
            log.error("删除会话时发生异常", e);
            return false;
        }
    }
}
