package com.cpit.chatbi.metabase.toolcall.table;

import com.cpit.chatbi.metabase.meta.schema.Column;
import com.cpit.chatbi.metabase.meta.schema.Schema;
import com.cpit.chatbi.metabase.meta.schema.Table;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.HashMap;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 表信息工具类
 */
public class TableInfoUtils {
    private static final Logger logger = LoggerFactory.getLogger(TableInfoUtils.class);

    /**
     * 根据表名获取表的详细信息
     *
     * @param schema 数据库Schema
     * @param tableName 表名
     * @return 表的详细信息，包括列信息
     */
    public static String getTableInfo(Schema schema, String tableName) {
        if (schema == null) {
            logger.error("Schema is null");
            return "无法获取表信息，Schema为空";
        }

        Map<String, Table> tables = schema.getTables();
        if (tables == null || tables.isEmpty()) {
            logger.error("No tables found in schema");
            return "无法获取表信息，Schema中没有表";
        }

        // 尝试精确匹配表名
        Table table = tables.get(tableName);
        
        // 如果没有精确匹配，尝试不区分大小写的匹配
        if (table == null) {
            for (Map.Entry<String, Table> entry : tables.entrySet()) {
                if (entry.getKey().equalsIgnoreCase(tableName)) {
                    table = entry.getValue();
                    break;
                }
            }
        }

        if (table == null) {
            logger.error("Table '{}' not found in schema", tableName);
            return "未找到表: " + tableName;
        }

        StringBuilder result = new StringBuilder();
        result.append("表名: ").append(table.getName()).append("\n");
        
        if (table.getComment() != null && !table.getComment().isEmpty()) {
            result.append("表注释: ").append(table.getComment()).append("\n");
        }
        
        result.append("表类型: ").append(table.getTableType()).append("\n\n");
        
        // 添加列信息
        result.append("列信息:\n");
        Map<String, Column> columns = table.getColumns();
        if (columns != null && !columns.isEmpty()) {
            for (Column column : columns.values()) {
                result.append("  - ").append(column.getName())
                      .append(" (").append(column.getTypeName());
                
                // 添加长度和精度信息（如果有）
                if (column.getLength() > 0) {
                    result.append("(").append(column.getLength());
                    if (column.getPrecision() > 0) {
                        result.append(",").append(column.getPrecision());
                    }
                    result.append(")");
                }
                
                result.append(")");
                
                // 添加是否可为空
                result.append(column.isNullable() ? " NULL" : " NOT NULL");
                
                // 添加默认值（如果有）
                if (column.getDefaultValue() != null && !column.getDefaultValue().isEmpty()) {
                    result.append(" DEFAULT ").append(column.getDefaultValue());
                }
                
                // 添加注释（如果有）
                if (column.getComment() != null && !column.getComment().isEmpty()) {
                    result.append(" COMMENT '").append(column.getComment()).append("'");
                }
                
                result.append("\n");
            }
        } else {
            result.append("  无列信息\n");
        }
        
        // 添加主键信息
        if (table.getPrimaryKey() != null) {
            result.append("\n主键: ").append(table.getPrimaryKey().getName())
                  .append(" (").append(String.join(", ", table.getPrimaryKey().getName())).append(")\n");
        }

        
        // 添加索引信息
        if (table.getIndexs() != null && !table.getIndexs().isEmpty()) {
            result.append("\n索引:\n");
            table.getIndexs().values().forEach(idx -> {
                result.append("  - ").append(idx.getName())
                      .append(" (").append(String.join(", ", idx.getColumnNames())).append(")")
                      .append(idx.isUnique() ? " UNIQUE" : "").append("\n");
            });
        }
        
        return result.toString();
    }

    /**
     * 获取所有表的名称列表
     *
     * @param schema 数据库Schema
     * @return 表名列表
     */
    public static String getTableList(Schema schema) {
        if (schema == null) {
            logger.error("Schema is null");
            return "无法获取表列表，Schema为空";
        }

        Map<String, Table> tables = schema.getTables();
        if (tables == null || tables.isEmpty()) {
            logger.error("No tables found in schema");
            return "Schema中没有表";
        }

        return tables.keySet().stream()
                .collect(Collectors.joining(", "));
    }
}
