package com.cpit.chatbi.metabase.ai.config;

import org.springframework.ai.chat.ChatClient;
import org.springframework.ai.chat.ChatOptions;
import org.springframework.ai.chat.client.ChatClientBuilder;
import org.springframework.ai.chat.messages.Message;
import org.springframework.ai.chat.messages.MessageType;
import org.springframework.ai.chat.model.ChatModel;
import org.springframework.ai.chat.model.ChatResponse;
import org.springframework.ai.chat.prompt.Prompt;
import org.springframework.ai.embedding.EmbeddingModel;
import org.springframework.ai.ollama.OllamaEmbeddingModel;
import org.springframework.ai.openai.OpenAiChatModel;
import org.springframework.ai.openai.OpenAiChatOptions;
import org.springframework.ai.openai.api.OpenAiApi;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;

/**
 * AI模型配置类
 * 手动配置OpenAI聊天模型和Ollama嵌入模型
 */
@Configuration
public class AIModelConfig {

    @Value("${spring.ai.openai.api-key}")
    private String openAiApiKey;

    @Value("${spring.ai.openai.base-url}")
    private String openAiBaseUrl;

    @Value("${spring.ai.openai.chat.options.model}")
    private String openAiChatModel;

    @Value("${spring.ai.ollama.base-url}")
    private String ollamaBaseUrl;

    @Value("${spring.ai.ollama.embedding.options.model}")
    private String ollamaEmbeddingModel;

    /**
     * 配置OpenAI API客户端
     */
    @Bean
    public OpenAiApi openAiApi() {
        return OpenAiApi.builder()
                .baseUrl(openAiBaseUrl)
                .apiKey(openAiApiKey)
                .build();
    }

    /**
     * 配置OpenAI聊天模型
     */
    @Bean
    public ChatModel openAiChatModel(OpenAiApi openAiApi) {
        OpenAiChatOptions options = OpenAiChatOptions.builder()
                .withModel(openAiChatModel)
                .withTemperature(0.7f)
                .build();
        
        return new OpenAiChatModel(openAiApi, options);
    }

    /**
     * 配置ChatClient
     */
    @Bean
    public ChatClient chatClient(ChatModel openAiChatModel) {
        return ChatClientBuilder.create(openAiChatModel).build();
    }

    /**
     * 配置Ollama嵌入模型
     * 使用@Primary注解确保它优先于其他EmbeddingModel Bean被注入
     */
    @Bean
    @Primary
    public EmbeddingModel embeddingModel() {
        return new OllamaEmbeddingModel(ollamaBaseUrl, ollamaEmbeddingModel);
    }
}
