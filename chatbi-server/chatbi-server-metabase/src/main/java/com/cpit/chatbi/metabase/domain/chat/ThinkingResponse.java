package com.cpit.chatbi.metabase.domain.chat;

import org.springframework.ai.chat.model.ChatResponse;
import org.springframework.ai.chat.model.Generation;

import java.util.ArrayList;
import java.util.List;

/**
 * 提供创建“思考中”消息的工具类
 */
public class ThinkingResponse {

    /**
     * 创建一个包含思考中消息的 ChatResponse
     *
     * @param message 思考中的消息内容
     * @return 包含思考中消息的 ChatResponse
     */
    public static ChatResponse create(String message) {
        // 创建一个模拟的 Generation 列表
        List<Generation> generations = new ArrayList<>();

        // 添加一个模拟的 Generation 对象
        generations.add(new MockGeneration(message));

        // 创建并返回 ChatResponse
        return new ChatResponse(generations);
    }

    /**
     * 模拟的 Generation 类，用于显示思考中消息
     */
    private static class MockGeneration extends Generation {
        private final String content;

        public MockGeneration(String content) {
            super(null); // 调用父类构造函数，传入 null
            this.content = content;
        }

        @Override
        public String toString() {
            return content;
        }
    }
}
