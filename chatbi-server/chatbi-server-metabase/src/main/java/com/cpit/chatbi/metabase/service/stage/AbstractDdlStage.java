package com.cpit.chatbi.metabase.service.stage;

import com.cpit.chatbi.metabase.domain.chat.DdlTaskResponse;
import com.cpit.chatbi.metabase.domain.dto.stage.DdlGenerationStage;
import com.cpit.chatbi.metabase.domain.dto.stage.StageContext;
import com.cpit.chatbi.metabase.domain.dto.stage.StageResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.ai.chat.client.ChatClient;
import org.springframework.ai.chat.client.advisor.AbstractChatMemoryAdvisor;
import reactor.core.publisher.FluxSink;
import reactor.core.publisher.Mono;
import reactor.core.scheduler.Schedulers;
import org.springframework.ai.chat.model.ChatResponse;
import java.util.concurrent.atomic.AtomicReference;

@Slf4j
abstract class AbstractDdlStage implements DdlGenerationStage {
    protected final ChatClient chatClient;
    protected final String stageName;
    protected final String stageDescription;

    protected AbstractDdlStage(ChatClient chatClient, String stageName, String stageDescription) {
        this.chatClient = chatClient;
        this.stageName = stageName;
        this.stageDescription = stageDescription;
    }

    @Override
    public String getStageName() { return stageName; }
    @Override
    public String getStageDescription() { return stageDescription; }

    protected abstract String buildUserPrompt(StageContext context);
    // getStageChatIdSuffix() 方法已经不再需要，因为我们使用统一的聊天ID

    // Default implementation: always continue
    protected Mono<StageResult> decideContinuation(String finalOutput) {
        // Add logic here based on finalOutput if a stage needs to decide to stop
        // Example: if (finalOutput == null || finalOutput.isBlank()) return Mono.just(StageResult.stopWith("..."));
        return Mono.just(StageResult.continueWith(finalOutput));
    }

    @Override
    public Mono<StageResult> process(StageContext context, FluxSink<DdlTaskResponse> sink) {
        // 使用更美观的Markdown格式发送阶段开始消息
        String thinkingMessage = formatThinkingMessage(getStageDescription());
        sink.next(new DdlTaskResponse(getStageName(), thinkingMessage, "thinking"));
        log.info("========== [Stage: {}] 开始 ==========", getStageName());

        String userPrompt = buildUserPrompt(context);
        // 使用统一的聊天ID，不再添加阶段后缀
        String stageChatId = context.getChatId();

        // 打印系统提示词和用户提示词
        log.info("========== [Stage: {}] 系统提示词 ==========", getStageName());
        log.info("{}\n", context.getSystemPrompt());
        log.info("========== [Stage: {}] 用户提示词 ==========", getStageName());
        log.info("{}\n", userPrompt);

        AtomicReference<String> fullContentRef = new AtomicReference<>("");
        StringBuilder currentChunkResult = new StringBuilder();

        return chatClient.prompt()
                .system(context.getSystemPrompt())
                .user(userPrompt)
                .advisors(advisorSpec -> advisorSpec
                        .param(AbstractChatMemoryAdvisor.CHAT_MEMORY_CONVERSATION_ID_KEY, stageChatId)
                        .param(AbstractChatMemoryAdvisor.CHAT_MEMORY_RETRIEVE_SIZE_KEY, 100)
                        // 添加阶段信息作为元数据
                        .param("stage", getStageName())
                        .param("stageDescription", getStageDescription())
                        .param("timestamp", System.currentTimeMillis())
                        // 添加问题ID
                        .param("questionId", context.getQuestionId()))
                // .tools(...) // Add tools if needed per stage
                .stream()
                .chatResponse()
                .publishOn(Schedulers.parallel())
                .mapNotNull(response -> {
                    String content = extractContent(response); // Use the existing extractContent method
                    if (content != null) {
                        currentChunkResult.append(content);
                        fullContentRef.getAndAccumulate(content, String::concat);
                        sink.next(new DdlTaskResponse(getStageName(), currentChunkResult.toString(), "thinking"));
                    }
                    return content; // Return content to know if something was processed
                })
                .doOnError(e -> {
                    log.error("========== [Stage: {}] AI 调用失败 ==========", getStageName(), e);
                    // 使用更美观的Markdown格式化错误消息
                    String errorMessage = String.format("### ❌ **%s时发生错误**\n\n```\n%s\n```",
                            getStageDescription(), e.getMessage());
                    sink.next(new DdlTaskResponse(getStageName(), errorMessage, "error"));
                    // Propagate the error to stop the main chain
                    sink.error(new RuntimeException("Stage " + getStageName() + " failed", e));
                })
                .then(Mono.defer(() -> { // Use defer to ensure execution after stream completes
                    String finalResult = fullContentRef.get();
                    log.info("========== [Stage: {}] 结果 ==========\n{}\n=======================================", getStageName(), finalResult);

                    // 添加阶段完成标记
                    String emoji = getStageName().equals("analysis") ? "🔍" :
                                  getStageName().equals("generation") ? "⚙️" :
                                  getStageName().equals("validation") ? "✅" : "🏁";

                    // 不再发送完成标记，使用空字符串
                    String completionHeader = "";

                    // 在生成阶段完成时，不再发送完整内容，只发送空字符串
                    // 但是要确保完整内容传递给decideContinuation方法，以便提取SQL

                    // 先决定是否继续流程，再发送相应状态的响应
                    return decideContinuation(finalResult)
                        .flatMap(stageResult -> {
                            // 如果不继续流程，发送stopped状态
                            if (!stageResult.isShouldContinue()) {
                                sink.next(new DdlTaskResponse(getStageName(), completionHeader, "stopped", finalResult));
                            } else {
                                // 如果继续流程，发送complete状态
                                sink.next(new DdlTaskResponse(getStageName(), completionHeader, "complete", finalResult));
                            }
                            return Mono.just(stageResult);
                        });
                }))
                .subscribeOn(Schedulers.boundedElastic());
    }

    // Assume extractContent is available in the scope or passed in
    private String extractContent(ChatResponse response) {
        // ... (use the implementation from previous answer) ...
        if (response != null && response.getResult() != null && response.getResult().getOutput() != null) {
            try { return response.getResult().getOutput().getText();} catch (Exception e) {log.warn("Error extracting content: {}", e.getMessage());}
        } else if (response != null && response.getResults() != null && !response.getResults().isEmpty()) {
            try { return response.getResults().get(0).getOutput().getText();} catch (Exception e) {log.warn("Error extracting content from results: {}", e.getMessage());}
        }
        return null;
    }

    /**
     * 格式化思考中的消息，使用更美观的Markdown格式
     * @param stageDescription 阶段描述
     * @return 格式化后的消息
     */
    private String formatThinkingMessage(String stageDescription) {
        // 根据阶段名称选择合适的emoji
        String emoji = "🔍";
        if ("生成 SQL".equals(stageDescription)) {
            emoji = "⚙️";
        } else if ("验证 SQL".equals(stageDescription)) {
            emoji = "✅";
        } else if ("生成最终结果".equals(stageDescription)) {
            emoji = "🏁";
        }

        // 使用更简洁的格式，不再使用标题格式
        return "";
    }

    /**
     * 生成进度动画字符
     * @return 进度动画字符串
     */
    private String getProgressAnimation() {
        return "<span class='thinking-animation'>...</span>";
    }

}
