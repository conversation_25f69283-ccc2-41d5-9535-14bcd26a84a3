package com.cpit.chatbi.metabase.service.stage;

import com.cpit.chatbi.metabase.domain.dto.stage.StageContext;
import com.cpit.chatbi.metabase.domain.dto.stage.StageResult;
import com.cpit.chatbi.metabase.meta.schema.Schema;
import com.cpit.chatbi.metabase.prompt.SqlAssistantPrompt;
import com.cpit.chatbi.metabase.service.MetabaseService;
import com.cpit.chatbi.metabase.util.SqlExtractorUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.ai.chat.client.ChatClient;
import reactor.core.publisher.Mono;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

@Slf4j
class GenerationStage extends AbstractDdlStage {
    private final SqlAssistantPrompt sqlAssistantPrompt;
    private final MetabaseService metabaseService;

    public GenerationStage(ChatClient chatClient, SqlAssistantPrompt sqlAssistantPrompt, MetabaseService metabaseService) {
        super(chatClient, "generation", "生成 SQL");
        this.sqlAssistantPrompt = sqlAssistantPrompt;
        this.metabaseService = metabaseService;
    }
    @Override protected String buildUserPrompt(StageContext context) {
        try {
            // 获取上一阶段的结果，包含表名列表
            StageResult previousResult = (StageResult) context.getAdditionalData().get("previousStageResult");
            List<String> tableNames = previousResult != null ? previousResult.getTableNames() : new ArrayList<>();

            // 获取数据库模式信息
            Schema schema = metabaseService.getSchemaWithDatabase();

            // 使用专门的DDL生成提示词，即使没有表名也使用专门提示词
            String systemPrompt = sqlAssistantPrompt.getDdlGenerationPrompt(schema, tableNames);

            // 设置到上下文中，覆盖原来的系统提示词
            context.setSystemPrompt(systemPrompt);

            // 构建用户提示词
            if (tableNames == null || tableNames.isEmpty()) {
                log.warn("========== [Stage: {}] 未提取到表名，使用分析结果作为提示 ==========", getStageName());
                // 即使没有表名，也使用分析结果和用户原始需求作为提示
                return "请基于以下分析和用户需求，生成适当的SQL语句。\n\n用户需求：\n" +
                       context.getInitialUserMessage() + "\n\n分析结果：\n" + context.getPreviousStageOutput();
            }

            // 有表名时，直接使用用户原始需求作为提示词
            return context.getInitialUserMessage();
        } catch (IOException e) {
            log.error("获取DDL生成提示词失败", e);
            // 如果出错，使用更全面的默认提示词
            return "请基于以下分析和用户需求，生成适当的SQL语句。\n\n用户需求：\n" +
                   context.getInitialUserMessage() + "\n\n分析结果：\n" + context.getPreviousStageOutput();
        }
    }
    // getStageChatIdSuffix() 方法已经不再需要，因为我们使用统一的聊天ID

    // 生成SQL后直接结束流程，不再进入验证阶段
    @Override
    protected Mono<StageResult> decideContinuation(String finalOutput) {
        String extractedSql = SqlExtractorUtils.extractSql(finalOutput);
        if (!SqlExtractorUtils.validSql(extractedSql)) {
            log.warn("========== [Stage: {}] 未生成有效SQL，终止流程 ==========", getStageName());
            // Include the raw output for context
            return Mono.just(StageResult.stopWith("未能生成有效的SQL语句。\n原始输出:\n" + finalOutput));
        }

        // 生成有效SQL后直接结束流程，不再进入验证阶段
        log.info("========== [Stage: {}] 生成SQL成功，直接结束流程 ==========", getStageName());
        // 使用stopWith而不是continueWith，表示流程结束
        return Mono.just(StageResult.stopWith(finalOutput));
    }
}