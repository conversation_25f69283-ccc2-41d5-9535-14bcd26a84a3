package com.cpit.chatbi.metabase.controller;

import com.cpit.chatbi.metabase.domain.dto.ChartDataResponse;
import com.cpit.chatbi.metabase.domain.dto.ColumnType;
import com.cpit.chatbi.metabase.domain.dto.QueryResult;
import com.cpit.chatbi.metabase.meta.schema.Schema;
import com.cpit.chatbi.metabase.prompt.ChartQueryPrompt;
import com.cpit.chatbi.metabase.service.ChartGenerationService;
import com.cpit.chatbi.metabase.service.DataQueryService;
import com.cpit.chatbi.metabase.service.MetabaseService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.ai.chat.client.ChatClient;
import org.springframework.ai.chat.model.ChatResponse;
import org.springframework.ai.chat.prompt.Prompt;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.io.IOException;
import java.util.List;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

@Slf4j
@RestController
@RequestMapping("/api/chart") // Base path for chart related endpoints
@RequiredArgsConstructor // Use constructor injection
@Tag(name = "Chart Generation API", description = "Endpoints for generating chart data from natural language")
public class ChartDataController {

    private final ChatClient chatClient; // Spring AI ChatClient
    private final MetabaseService metabaseService;
    private final ChartQueryPrompt chartQueryPrompt;
    private final DataQueryService dataQueryService;
    private final ChartGenerationService chartGenerationService;

    // Regex to extract SQL from ```sql ... ``` block, handling potential variations
    private static final Pattern SQL_BLOCK_PATTERN = Pattern.compile("```(?:sql)?\\s*(.*?)\\s*```", Pattern.DOTALL | Pattern.CASE_INSENSITIVE);


    @Operation(summary = "Generate Chart Data from Natural Language", description = "Takes a natural language query, generates SQL, executes it, and returns ECharts options JSON.")
    @PostMapping(value = "/generate", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ChartDataResponse> generateChartData(
            @Parameter(description = "Natural language query about the data") @RequestParam String message,
            @Parameter(description = "Optional user ID") @RequestParam(required = false) String userId,
            @Parameter(description = "Optional session ID for context/logging") @RequestParam(required = false) String sessionId
            // Add other parameters if needed (e.g., specific chart type preference)
    ) {
        Assert.hasText(message, "Message cannot be empty");
        log.info("Received chart generation request for session [{}], user [{}]: {}", sessionId, userId, message);

        String systemPrompt;
        Schema schema;
        String generatedSql = null; // Keep track of SQL even if later steps fail

        try {
            // 1. Get Schema & Prepare Prompt
            schema = metabaseService.getSchemaWithDatabase(); // Assumes this gets the necessary info
            systemPrompt = chartQueryPrompt.getSystemQueryPrompt(schema);
            log.debug("Generated System Prompt for Text-to-SQL.");


            // Use system() method correctly - provide System Message content directly
            String llmResponseContent = chatClient.prompt()
                    .system(systemSpec -> systemSpec.text(systemPrompt))
                    .user(userSpec -> userSpec.text(message))
                    .call() // Use call() for non-streaming response
                    .content();

            log.debug("LLM Response Content: {}", llmResponseContent);

            // 3. Extract SQL from LLM Response
            generatedSql = extractSqlFromLlmResponse(llmResponseContent);
            if (generatedSql == null) {
                log.error("Failed to extract SQL from LLM response for message: {}", message);
                // Check if LLM asked for clarification
                if (llmResponseContent.toUpperCase().contains("CLARIFICATION NEEDED:")) {
                    return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                            .body(new ChartDataResponse("CLARIFICATION NEEDED: " + llmResponseContent, null));
                }
                // Check for assumptions
                if (llmResponseContent.toUpperCase().startsWith("ASSUMPTION:")) {
                    // Maybe try to extract SQL after the assumption text? More complex parsing needed.
                    log.warn("LLM stated assumption, attempting to proceed but parsing might be needed.");
                    // Attempt to find SQL block even after assumption text
                    generatedSql = extractSqlFromLlmResponse(llmResponseContent.substring(llmResponseContent.indexOf("```")));
                    if(generatedSql == null) {
                        return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                                .body(new ChartDataResponse("LLM stated an assumption but failed to provide SQL in the expected format.", null));
                    }
                } else {
                    return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                            .body(new ChartDataResponse("Could not understand or extract SQL from the AI's response. Response: " + llmResponseContent, null));
                }
            }
            log.info("Extracted SQL: {}", generatedSql);

            // 4. Execute SQL Query (Handles its own errors internally)
            QueryResult queryResult = dataQueryService.executeQuery(generatedSql);
            queryResult.setGeneratedSql(generatedSql); // Ensure SQL is in the result

            if (!queryResult.isExecutionSuccessful()) {
                log.error("SQL execution failed: {}", queryResult.getErrorMessage());
                return ResponseEntity.status(HttpStatus.BAD_REQUEST) // Or INTERNAL_SERVER_ERROR depending on cause
                        .body(new ChartDataResponse("Failed to execute the generated query: " + queryResult.getErrorMessage(), generatedSql));
            }
            if (queryResult.getData() == null || queryResult.getColumns() == null) {
                log.error("Query execution returned null data or columns unexpectedly.");
                return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                        .body(new ChartDataResponse("Internal error: Query executed but result data is missing.", generatedSql));
            }


            // 5. Identify Column Types
            Map<String, ColumnType> columnTypes = chartGenerationService.identifyColumnTypes(queryResult.getColumns());

            // 6. Recommend Chart Types
            List<String> recommendedCharts = chartGenerationService.recommendChartTypes(columnTypes);
            if (recommendedCharts.isEmpty()) {
                log.warn("No specific chart type recommended, defaulting to table.");
                recommendedCharts.add("table"); // Ensure at least 'table' is recommended
            }
            String selectedChartType = recommendedCharts.get(0); // Select the top recommendation
            log.info("Selected chart type: {}", selectedChartType);


            // 7. Format Data for ECharts
            String eChartsOptionsJson = chartGenerationService.formatForECharts(queryResult, columnTypes, selectedChartType);


            // 8. Return Success Response
            ChartDataResponse successResponse = new ChartDataResponse(
                    eChartsOptionsJson,
                    selectedChartType,
                    recommendedCharts.subList(1, recommendedCharts.size()), // Other recommendations
                    generatedSql
            );
            return ResponseEntity.ok(successResponse);

        } catch (IOException e) {
            log.error("Error loading prompt template or schema: {}", e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(new ChartDataResponse("Internal error: Could not load necessary configuration.", generatedSql));
        } catch (IllegalArgumentException e) {
            log.error("Error during chart formatting or validation: {}", e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                    .body(new ChartDataResponse("Error processing request: " + e.getMessage(), generatedSql));
        } catch (Exception e) {
            log.error("An unexpected error occurred during chart generation: {}", e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(new ChartDataResponse("An unexpected internal error occurred.", generatedSql));
        }
    }

    /**
     * Extracts SQL content from a string, expecting it within ```sql ... ``` blocks.
     * Handles optional 'sql' language identifier and trims whitespace.
     *
     * @param responseText The text potentially containing the SQL block.
     * @return The extracted SQL string, or null if not found or empty.
     */
    private String extractSqlFromLlmResponse(String responseText) {
        if (responseText == null || responseText.isBlank()) {
            return null;
        }
        Matcher matcher = SQL_BLOCK_PATTERN.matcher(responseText);
        if (matcher.find()) {
            String sql = matcher.group(1).trim(); // Group 1 captures content inside the backticks
            return sql.isEmpty() ? null : sql;
        }
        // Fallback: Maybe the LLM just returned plain SQL without backticks (less ideal)
        String trimmedResponse = responseText.trim();
        if (trimmedResponse.toLowerCase().startsWith("select")) {
            log.warn("SQL block ```sql ... ``` not found, but response starts with SELECT. Assuming plain SQL response.");
            // Basic sanity check: ensure it likely ends with a semicolon or is a single statement
            if (trimmedResponse.contains(";") || !trimmedResponse.substring(trimmedResponse.indexOf(' ')).trim().contains(" ")) {
                return trimmedResponse;
            }
        }

        log.warn("Could not find SQL block ```sql ... ``` in response.");
        return null;
    }
}