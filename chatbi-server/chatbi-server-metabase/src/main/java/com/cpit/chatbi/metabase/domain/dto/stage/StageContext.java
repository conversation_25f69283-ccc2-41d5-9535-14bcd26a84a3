package com.cpit.chatbi.metabase.domain.dto.stage;

import lombok.With; // 使用 Lombok 的 @With 注解更方便

@lombok.Data
// 使用 @AllArgsConstructor 让 @With 能正常工作，或者手动写 with 方法
@lombok.AllArgsConstructor
// @lombok.RequiredArgsConstructor 已移除，因为 @Data + @AllArgsConstructor 已经包含了
public class StageContext {
    private String systemPrompt; // 不再是final，允许修改
    private final String chatId;
    private final String userId;
    private final String initialUserMessage;
    private final String questionId; // 问题ID，用于关联问题和回答

    // 使用 @With 注解 (Lombok 会自动生成 withPreviousStageOutput 方法)
    // 它会创建一个新对象，只改变这个字段的值
    @With
    private final String previousStageOutput; // 可以设为 final

    // 用于存储阶段间传递的额外数据
    private final java.util.Map<String, Object> additionalData = new java.util.HashMap<>();

    // 初始上下文工厂方法
    public static StageContext initial(String systemPrompt, String chatId, String userId, String initialUserMessage, String questionId) {
        // 确保构造函数匹配
        return new StageContext(systemPrompt, chatId, userId, initialUserMessage, questionId, null);
    }

    /**
     * 将上一阶段的结果存储到额外数据中
     *
     * @param result 上一阶段的结果
     */
    public void setPreviousStageResult(StageResult result) {
        this.additionalData.put("previousStageResult", result);
    }
}