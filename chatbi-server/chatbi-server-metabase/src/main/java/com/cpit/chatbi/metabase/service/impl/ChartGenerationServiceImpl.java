package com.cpit.chatbi.metabase.service.impl;

import com.cpit.chatbi.metabase.domain.dto.ColumnInfo;
import com.cpit.chatbi.metabase.domain.dto.ColumnType;
import com.cpit.chatbi.metabase.domain.dto.QueryResult;
import com.cpit.chatbi.metabase.service.ChartGenerationService;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.sql.Types;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
@RequiredArgsConstructor // Use constructor injection
public class ChartGenerationServiceImpl implements ChartGenerationService {

    private final ObjectMapper objectMapper; // Jackson ObjectMapper bean

    // Optional: Inject ChatLanguageModel if using LLM for assist
    // private final ChatLanguageModel chatModel;

    @Override
    public Map<String, ColumnType> identifyColumnTypes(List<ColumnInfo> columns) {
        log.debug("Identifying column types for columns: {}", columns);
        // --- Placeholder for LLM Interaction ---
        // if (chatModel != null) { /* Call LLM */ return parseLlmClassificationResponse(response); }

        // --- Simple Rule-Based Fallback ---
        Map<String, ColumnType> columnTypes = new LinkedHashMap<>(); // Preserve order
        if (columns == null) return columnTypes;

        for (ColumnInfo col : columns) {
            String colNameLower = col.getName().toLowerCase();
            int type = col.getSqlType();
            ColumnType identifiedType = ColumnType.UNKNOWN;

            // Rule: Numeric types are measures unless name suggests ID/Code/Year etc.
            if (type == Types.NUMERIC || type == Types.DECIMAL || type == Types.DOUBLE || type == Types.FLOAT ||
                    type == Types.INTEGER || type == Types.BIGINT || type == Types.SMALLINT || type == Types.TINYINT)
            {
                // More keywords that often indicate a dimension despite being numeric
                if (colNameLower.contains("id") || colNameLower.contains("code") || colNameLower.endsWith("key") ||
                        colNameLower.contains("year") || colNameLower.contains("month") || colNameLower.contains("day") ||
                        colNameLower.contains("hour") || colNameLower.contains("num") || colNameLower.startsWith("no") ||
                        colNameLower.contains("level") || colNameLower.contains("rank") || colNameLower.contains("seq")) {
                    identifiedType = ColumnType.DIMENSION;
                } else {
                    identifiedType = ColumnType.MEASURE;
                }
            }
            // Rule: Date/Time/Timestamp are dimensions
            else if (type == Types.DATE || type == Types.TIME || type == Types.TIMESTAMP || type == Types.TIMESTAMP_WITH_TIMEZONE) {
                identifiedType = ColumnType.DIMENSION;
            }
            // Rule: String/Boolean/Other are usually dimensions
            else { // Includes VARCHAR, CHAR, BOOLEAN, CLOB, etc.
                identifiedType = ColumnType.DIMENSION;
            }
            columnTypes.put(col.getName(), identifiedType);
        }
        log.debug("Identified column types: {}", columnTypes);
        return columnTypes;
    }

    @Override
    public List<String> recommendChartTypes(Map<String, ColumnType> columnTypes) {
        log.debug("Recommending chart types based on: {}", columnTypes);
        // --- Placeholder for LLM Interaction ---
        // if (chatModel != null) { /* Call LLM */ return parseLlmRecommendationResponse(response); }

        // --- Simple Rule-Based Fallback ---
        List<String> recommendations = new ArrayList<>();
        long dCount = columnTypes.values().stream().filter(t -> t == ColumnType.DIMENSION).count();
        long mCount = columnTypes.values().stream().filter(t -> t == ColumnType.MEASURE).count();

        // Determine if there's a time dimension (crude check by name/type)
        boolean hasTimeDimension = columnTypes.entrySet().stream()
                .anyMatch(entry -> entry.getValue() == ColumnType.DIMENSION &&
                        (entry.getKey().toLowerCase().contains("date") ||
                                entry.getKey().toLowerCase().contains("time") ||
                                entry.getKey().toLowerCase().contains("month") ||
                                entry.getKey().toLowerCase().contains("year")));
        // Ideally, check original SQL type (Types.DATE/TIME/TIMESTAMP) if available

        // Order matters: put most likely first
        if (mCount >= 1 && dCount == 1) {
            if (hasTimeDimension) {
                recommendations.add("line"); // Trend over time
                recommendations.add("bar");
            } else {
                recommendations.add("bar");  // Category comparison
                if (dCount == 1 && mCount == 1 ) { // Only recommend pie for 1D/1M
                    recommendations.add("pie"); // Proportions (use with caution for many categories)
                }
                recommendations.add("line"); // Can sometimes work for ordered categories
            }
            recommendations.add("table");
        } else if (mCount >= 1 && dCount >= 2) {
            recommendations.add("bar"); // Grouped/Stacked bar often works
            recommendations.add("heatmap"); // Good for 2D vs 1M
            recommendations.add("table");
        } else if (mCount >= 2 && dCount == 1) {
            if (hasTimeDimension) {
                recommendations.add("line"); // Multiple lines over time
            }
            recommendations.add("bar"); // Grouped/Stacked bar
            recommendations.add("scatter"); // If measures represent X/Y
            recommendations.add("table");
        } else if (mCount == 0 && dCount >= 1) {
            recommendations.add("table"); // Just dimensions, show as table
        }
        else if (mCount >= 2 && dCount == 0) {
            recommendations.add("scatter"); // Correlation between measures
            recommendations.add("table");
        } else {
            // Default or complex cases
            recommendations.add("table");
        }

        // Ensure table is always an option if others fit
        if (!recommendations.contains("table") && !columnTypes.isEmpty()) {
            recommendations.add("table");
        }
        // Remove duplicates just in case
        recommendations = recommendations.stream().distinct().collect(Collectors.toList());

        log.debug("Recommended chart types: {}", recommendations);
        return recommendations;
    }


    @Override
    public String formatForECharts(QueryResult queryResult, Map<String, ColumnType> columnTypes, String selectedChartType) throws Exception {
        log.info("Formatting data for ECharts type: {}", selectedChartType);
        if (queryResult == null || !queryResult.isExecutionSuccessful() || queryResult.getData() == null) {
            throw new IllegalArgumentException("Cannot format data: QueryResult is null, unsuccessful, or has no data.");
        }
        List<Map<String, Object>> data = queryResult.getData();
        List<ColumnInfo> columns = queryResult.getColumns();


        Map<String, Object> eChartsOptions = new LinkedHashMap<>();
        List<String> dimensions = columnTypes.entrySet().stream()
                .filter(e -> e.getValue() == ColumnType.DIMENSION)
                .map(Map.Entry::getKey).collect(Collectors.toList());
        List<String> measures = columnTypes.entrySet().stream()
                .filter(e -> e.getValue() == ColumnType.MEASURE)
                .map(Map.Entry::getKey).collect(Collectors.toList());

        // Handle No Data case explicitly
        if (data.isEmpty()) {
            log.warn("Query returned no data. Generating empty chart options.");
            eChartsOptions.put("title", Map.of("text", "No Data Returned", "subtext", "Query executed successfully but yielded no results."));
            eChartsOptions.put("series", Collections.emptyList()); // Ensure series is empty
            return objectMapper.writerWithDefaultPrettyPrinter().writeValueAsString(eChartsOptions);
        }


        // --- Add formatting logic for different chart types ---
        String chartTitle = "Generated Chart"; // Default title

        switch (selectedChartType.toLowerCase()) {
            case "bar":
            case "line":
                if (dimensions.isEmpty() || measures.isEmpty()) {
                    log.error("Bar/Line chart requires at least one dimension and one measure. D: {}, M: {}", dimensions.size(), measures.size());
                    throw new IllegalArgumentException("Bar/Line chart requires at least one dimension and one measure.");
                }
                String categoryDimension = dimensions.get(0); // Use first dimension for category axis
                chartTitle = String.join(" & ", measures) + " by " + categoryDimension;

                // Prepare Axis Data
                Map<String, Object> xAxis = new HashMap<>();
                xAxis.put("type", "category");
                // Collect unique category values in order
                List<Object> categories = data.stream()
                        .map(row -> row.get(categoryDimension))
                        .distinct() // Keep distinct categories
                        .collect(Collectors.toList());
                xAxis.put("data", categories);
                eChartsOptions.put("xAxis", xAxis);

                Map<String, Object> yAxis = new HashMap<>();
                yAxis.put("type", "value");
                eChartsOptions.put("yAxis", yAxis);

                // Prepare Series Data
                List<Map<String, Object>> seriesList = new ArrayList<>();
                for (String measure : measures) {
                    Map<String, Object> series = new HashMap<>();
                    series.put("name", measure);
                    series.put("type", selectedChartType.toLowerCase()); // 'bar' or 'line'
                    // Ensure data aligns with the potentially filtered categories
                    List<Object> seriesData = categories.stream()
                            .map(category -> data.stream()
                                    .filter(row -> Objects.equals(row.get(categoryDimension), category))
                                    .map(row -> row.get(measure))
                                    .findFirst() // Assumes one value per category per measure (adjust if grouped differently)
                                    .orElse(null)) // Handle missing data points if any
                            .collect(Collectors.toList());
                    series.put("data", seriesData);
                    // Example: Add smooth=true for line charts
                    if("line".equals(selectedChartType.toLowerCase())) series.put("smooth", true);
                    seriesList.add(series);
                }
                eChartsOptions.put("series", seriesList);

                // Add Tooltip, Legend, Title, Grid (for better layout)
                eChartsOptions.put("tooltip", Map.of("trigger", "axis", "axisPointer", Map.of("type", "shadow"))); // Enhanced tooltip
                if (measures.size() > 1) {
                    eChartsOptions.put("legend", Map.of("data", measures));
                } else {
                    eChartsOptions.put("legend", Map.of("show", false)); // Hide legend for single measure
                }
                eChartsOptions.put("grid", Map.of("left", "3%", "right", "4%", "bottom", "3%", "containLabel", true)); // Better padding

                break;

            case "pie":
                if (dimensions.size() != 1 || measures.size() != 1) {
                    log.error("Pie chart requires exactly one dimension and one measure. D: {}, M: {}", dimensions.size(), measures.size());
                    throw new IllegalArgumentException("Pie chart requires exactly one dimension and one measure.");
                }
                String pieDimension = dimensions.get(0);
                String pieMeasure = measures.get(0);
                chartTitle = pieMeasure + " Distribution by " + pieDimension;


                Map<String, Object> pieSeries = new HashMap<>();
                pieSeries.put("name", pieMeasure);
                pieSeries.put("type", "pie");
                pieSeries.put("radius", "70%"); // Example size
                pieSeries.put("center", List.of("50%", "60%")); // Centering
                pieSeries.put("data", data.stream()
                        .map(row -> {
                            Map<String, Object> dataPoint = new HashMap<>();
                            // Echarts pie needs 'value' and 'name' keys
                            dataPoint.put("value", row.get(pieMeasure));
                            dataPoint.put("name", row.get(pieDimension));
                            return dataPoint;
                        })
                        .collect(Collectors.toList()));
                // Add emphasis styling
                pieSeries.put("emphasis", Map.of("itemStyle", Map.of("shadowBlur", 10, "shadowOffsetX", 0, "shadowColor", "rgba(0, 0, 0, 0.5)")));

                eChartsOptions.put("series", List.of(pieSeries));
                eChartsOptions.put("tooltip", Map.of("trigger", "item", "formatter", "{a} <br/>{b} : {c} ({d}%)")); // Show percentage
                eChartsOptions.put("legend", Map.of("orient", "vertical", "left", "left", "type", "scroll")); // Scrollable legend if many items

                break;

            case "table":
                // Formatting for a frontend table library (like AG-Grid, TanStack Table, or even a simple HTML table)
                // is usually just returning the raw data and columns. ECharts itself doesn't have a great general-purpose table component.
                // We'll structure it for ECharts' 'dataset' source, which might be usable by some components or manually processed.
                log.warn("Returning data structured for ECharts dataset source for table type.");
                chartTitle = "Data Table: " + String.join(", ", columns.stream().map(ColumnInfo::getName).collect(Collectors.toList()));
                eChartsOptions.put("dataset", List.of(
                        Map.of(
                                "dimensions", columns.stream().map(ColumnInfo::getName).collect(Collectors.toList()), // Header row
                                "source", data // Data rows
                        )
                ));
                // You might add a simple visual element, but usually the frontend handles the table rendering.
                eChartsOptions.put("grid", Map.of("containLabel", true)); // Needed for dataset
                // Add a placeholder series if needed for ECharts to render anything
                // eChartsOptions.put("series", List.of(Map.of("type", "table"))); // Fictional type

                break;


            default:
                log.error("Unsupported chart type for formatting: {}", selectedChartType);
                throw new IllegalArgumentException("Unsupported chart type for formatting: " + selectedChartType);
        }

        // Add common options like Title
        eChartsOptions.putIfAbsent("title", Map.of("text", chartTitle));
        // Add Toolbox for user interactions (optional)
        eChartsOptions.put("toolbox", Map.of("feature", Map.of(
                "saveAsImage", Map.of(), // Allow saving as image
                "dataView", Map.of("readOnly", false), // Allow viewing data table
                "restore", Map.of() // Allow restoring state
        )));


        // Convert the Java Map structure to a JSON string
        String jsonResult = objectMapper.writerWithDefaultPrettyPrinter().writeValueAsString(eChartsOptions);
        log.debug("Generated ECharts options JSON: {}", jsonResult);
        return jsonResult;
    }

    // --- Placeholder LLM Helper Methods (Implement if using LLM) ---
    /*
    private Map<String, ColumnType> parseLlmClassificationResponse(String llmResponse) { ... }
    private List<String> parseLlmRecommendationResponse(String llmResponse) { ... }
    */
}
