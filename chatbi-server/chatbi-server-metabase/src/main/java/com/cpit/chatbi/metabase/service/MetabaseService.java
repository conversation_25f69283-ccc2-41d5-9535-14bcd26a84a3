package com.cpit.chatbi.metabase.service;

import com.cpit.chatbi.metabase.domain.er.ErDiagram;
import com.cpit.chatbi.metabase.meta.schema.Schema;

public interface MetabaseService {
    /**
     * 获取数据库Schema信息, 包含表、视图、触发器、函数、存储过程等信息
     */
    ErDiagram getSchema();

    /**
     * 获取数据库Schema信息, 包括数据库信息
     */
    Schema getSchemaWithDatabase();


    /**
     * 获取当前用户ID
     */
    String getCurrentUserId();
    
    /**
     * 执行SQL并返回通知
     */
    String executeDdlSql(String sql);


}