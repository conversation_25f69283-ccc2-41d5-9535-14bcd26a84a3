package com.cpit.chatbi.metabase.service.stage;

import com.cpit.chatbi.metabase.domain.dto.stage.StageContext;
import com.cpit.chatbi.metabase.domain.dto.stage.StageResult;
import com.cpit.chatbi.metabase.meta.schema.Schema;
import com.cpit.chatbi.metabase.prompt.SqlAssistantPrompt;
import com.cpit.chatbi.metabase.service.MetabaseService;
import com.cpit.chatbi.metabase.util.SqlExtractorUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.ai.chat.client.ChatClient;
import reactor.core.publisher.Mono;

import java.io.IOException;

@Slf4j
public class OptimizationStage extends AbstractDdlStage {
    private final SqlAssistantPrompt sqlAssistantPrompt;
    private final MetabaseService metabaseService;

    public OptimizationStage(ChatClient chatClient, SqlAssistantPrompt sqlAssistantPrompt, MetabaseService metabaseService) {
        super(chatClient, "validation", "验证 SQL");
        this.sqlAssistantPrompt = sqlAssistantPrompt;
        this.metabaseService = metabaseService;
    }
    @Override protected String buildUserPrompt(StageContext context) {
        try {
            // 从上一阶段提取SQL
            String sqlToValidate = SqlExtractorUtils.extractSql(context.getPreviousStageOutput());
            // If extraction failed, use the raw previous output, but log a warning.
            if (!SqlExtractorUtils.validSql(sqlToValidate)) {
                log.warn("[Stage: {}] 未能从上一阶段提取有效 SQL，将尝试验证原始文本。", getStageName());
                sqlToValidate = context.getPreviousStageOutput();
            }

            // 获取数据库模式信息
            Schema schema = metabaseService.getSchemaWithDatabase();

            // 使用专门的DDL验证提示词
            String systemPrompt = sqlAssistantPrompt.getDdlValidationPrompt(schema, sqlToValidate);

            // 设置到上下文中，覆盖原来的系统提示词
            context.setSystemPrompt(systemPrompt);

            // 验证提示词已经包含了SQL，这里只需要一个简单的提示
            return "请验证上述SQL是否符合要求，并给出详细分析。";
        } catch (IOException e) {
            log.error("获取DDL验证提示词失败", e);
            // 如果出错，使用默认提示词
            String sqlToValidate = SqlExtractorUtils.extractSql(context.getPreviousStageOutput());
            return "请验证以下 SQL 语句是否符合要求，并给出详细分析：\n" + sqlToValidate;
        }
    }

    // getStageChatIdSuffix() 方法已经不再需要，因为我们使用统一的聊天ID

    // 验证阶段是最后一个阶段，完成后正常结束流程
    @Override
    protected Mono<StageResult> decideContinuation(String finalOutput) {
        log.info("========== [Stage: {}] 验证阶段完成，正常结束流程 ==========", getStageName());
        // 返回停止信号，表示流程完成
        return Mono.just(StageResult.stopWith("验证结果:\n" + finalOutput));
    }
}