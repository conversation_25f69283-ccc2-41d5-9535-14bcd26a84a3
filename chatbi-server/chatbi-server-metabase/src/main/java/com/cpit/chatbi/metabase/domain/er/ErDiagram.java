package com.cpit.chatbi.metabase.domain.er;

import com.alibaba.fastjson.annotation.JSONField;
import java.util.*;

public class ErDiagram {
    @JSONField(ordinal = 1)
    private List<TableInfo> tables;

    @JSONField(ordinal = 2)
    private List<Edge> edges;

    @JSONField(ordinal = 3)
    private String databaseDialect; // 数据库方言

    @JSONField(ordinal = 4)
    private String schemaName; // 模式名称


    public List<TableInfo> getTables() {
        return tables;
    }

    public void setTables(List<TableInfo> tables) {
        this.tables = tables;
    }

    public List<Edge> getEdges() {
        return edges;
    }

    public void setEdges(List<Edge> edges) {
        this.edges = edges;
    }

    public String getDatabaseDialect() {
        return databaseDialect;
    }

    public void setDatabaseDialect(String databaseDialect) {
        this.databaseDialect = databaseDialect;
    }

    public String getSchemaName() {
        return schemaName;
    }

    public void setSchemaName(String schemaName) {
        this.schemaName = schemaName;
    }

    /**
     * 根据edges中的关系重新排序tables列表
     * 让有关联的表按照edges的顺序排列
     */
    public void sortTablesByEdges() {
        if (tables == null || edges == null || tables.isEmpty() || edges.isEmpty()) {
            return;
        }

        // 创建表名到TableInfo的映射
        Map<String, TableInfo> tableMap = new HashMap<>();
        for (TableInfo table : tables) {
            tableMap.put(table.getData().getTableName(), table);
        }

        // 创建已排序的表集合
        Set<String> sortedTables = new HashSet<>();
        List<TableInfo> newTables = new ArrayList<>();

        // 按照edges的顺序添加表
        for (Edge edge : edges) {
            String source = edge.getSource();
            String target = edge.getTarget();

            // 添加目标表（被引用的表）
            if (!sortedTables.contains(target)) {
                newTables.add(tableMap.get(target));
                sortedTables.add(target);
            }

            // 添加源表（引用的表）
            if (!sortedTables.contains(source)) {
                newTables.add(tableMap.get(source));
                sortedTables.add(source);
            }
        }

        // 添加没有关联的表
        for (TableInfo table : tables) {
            if (!sortedTables.contains(table.getData().getTableName())) {
                newTables.add(table);
            }
        }

        this.tables = newTables;
    }
}