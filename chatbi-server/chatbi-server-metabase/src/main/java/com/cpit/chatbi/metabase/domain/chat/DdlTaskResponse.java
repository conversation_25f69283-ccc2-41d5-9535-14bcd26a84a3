package com.cpit.chatbi.metabase.domain.chat;

import java.util.UUID;

/**
 * DDL任务响应类，用于流式返回多轮调用的结果
 */
public class DdlTaskResponse {
    private final String id = UUID.randomUUID().toString();
    private final String stage;  // 阶段：analysis, generation, optimization, final
    private final String content; // 内容
    private final long timestamp = System.currentTimeMillis();
    private final String status;  // 状态：thinking, complete, error
    private final Object data;    // 附加数据

    public DdlTaskResponse(String stage, String content, String status) {
        this.stage = stage;
        this.content = content;
        this.status = status;
        this.data = null;
    }

    public DdlTaskResponse(String stage, String content, String status, Object data) {
        this.stage = stage;
        this.content = content;
        this.status = status;
        this.data = data;
    }

    public String getId() {
        return id;
    }

    public String getStage() {
        return stage;
    }

    public String getContent() {
        return content;
    }

    public long getTimestamp() {
        return timestamp;
    }

    public String getStatus() {
        return status;
    }

    public Object getData() {
        return data;
    }
}
