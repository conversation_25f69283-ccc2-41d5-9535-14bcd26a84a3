package com.cpit.chatbi.metabase.domain.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class ChartDataResponse {
    private String eChartsOptionsJson;
    private String recommendedChartType;
    private List<String> otherRecommendedTypes;
    private String generatedSql;
    private String message; // For status or error messages
    private boolean success = true;

    // Constructor for success
    public ChartDataResponse(String eChartsOptionsJson, String recommendedChartType, List<String> otherRecommendedTypes, String generatedSql) {
        this.eChartsOptionsJson = eChartsOptionsJson;
        this.recommendedChartType = recommendedChartType;
        this.otherRecommendedTypes = otherRecommendedTypes;
        this.generatedSql = generatedSql;
        this.message = "Chart data generated successfully.";
    }

    // Constructor for failure
    public ChartDataResponse(String message, String generatedSql) {
        this.success = false;
        this.message = message;
        this.generatedSql = generatedSql; // Include SQL even on failure if available
    }
}