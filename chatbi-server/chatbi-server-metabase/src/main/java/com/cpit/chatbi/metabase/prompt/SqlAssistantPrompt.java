package com.cpit.chatbi.metabase.prompt;

import com.cpit.chatbi.metabase.meta.schema.Column;
import com.cpit.chatbi.metabase.meta.schema.DatabaseInfo;
import com.cpit.chatbi.metabase.meta.schema.Schema;
import com.cpit.chatbi.metabase.meta.schema.Table;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.Resource;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.List;
import java.util.Map;


/**
 * 数据库信息渲染工具类，用于将数据库结构信息格式化为模板字符串
 */
@Slf4j
@Component
public class SqlAssistantPrompt {

    @Value("classpath:/prompts/system-prompt-template.st")
    private Resource systemPromptResource;

    @Value("classpath:/prompts/system-prompt-ddl-relevance.st")
    private Resource ddlRelevancePromptResource;

    @Value("classpath:/prompts/system-prompt-ddl-generation.st")
    private Resource ddlGenerationPromptResource;

    @Value("classpath:/prompts/system-prompt-ddl-validation.st")
    private Resource ddlValidationPromptResource;

    /**
     * 获取判断问题是否与DDL相关的系统提示词
     *
     * @param schema 数据库模式信息
     * @return 格式化后的系统提示词
     * @throws IOException 如果读取模板文件失败
     */
    public String getDdlRelevancePrompt(Schema schema) throws IOException {
        Map<String, Table> tables = schema.getTables();
/*        if (tables == null || tables.isEmpty()) {
            // 空库，返回特别提示
            return "当前数据库为空，未包含任何表结构。请判断用户问题是否与数据库结构设计相关。";
        }*/

        String template = new String(ddlRelevancePromptResource.getInputStream().readAllBytes(), StandardCharsets.UTF_8);

        // 只生成表名和表注释的简化信息，不包含字段详情
        String tableInfo = generateSimpleTableInfo(schema);

        return template
                .replace("${tableInfo}", tableInfo);
    }

    /**
     * 渲染数据库信息为模板字符串
     */
    public String getSystemInstructionPrompt(Schema schema) throws IOException {
        Map<String, Table> tables = schema.getTables();
/*        if (tables == null || tables.isEmpty()) {
            // 空库，返回特别提示
            return "当前数据库为空，未包含任何表结构。请根据用户需求设计新的数据库结构。";
        }*/

        String template = new String(systemPromptResource.getInputStream().readAllBytes(), StandardCharsets.UTF_8);

        String tableInfo = generateTableInfo(schema);

        DatabaseInfo dbInfo = schema.getDatabaseInfo();
        String schemaName = schema.getSchemaInfo().getSchemaName();
        return template
                .replace("${productName}", dbInfo.getProductName())
                .replace("${productVersion}", dbInfo.getProductVersion())
                .replace("${schemaName}", schemaName)
                .replace("${tableInfo}", tableInfo);
    }


    /**
     * 渲染数据库信息为模板字符串
     */
    public String getSystemInstructionPromptOnlyTable(Schema schema) throws IOException {
        Map<String, Table> tables = schema.getTables();
/*        if (tables == null || tables.isEmpty()) {
            // 空库，返回特别提示
            return "当前数据库为空，未包含任何表结构。请根据用户需求设计新的数据库结构。";
        }*/

        String template = new String(systemPromptResource.getInputStream().readAllBytes(), StandardCharsets.UTF_8);

        String tableInfo = generateSimpleTableInfo(schema);

        DatabaseInfo dbInfo = schema.getDatabaseInfo();
        String schemaName = schema.getSchemaInfo().getSchemaName();
        return template
                .replace("${productName}", dbInfo.getProductName())
                .replace("${productVersion}", dbInfo.getProductVersion())
                .replace("${schemaName}", schemaName)
                .replace("${tableInfo}", tableInfo);
    }

    /**
     * 生成表结构信息字符串
     */
    private String generateTableInfo(Schema schema) {
        StringBuilder sb = new StringBuilder();
        Map<String, Table> tables = schema.getTables();

        if (tables != null && !tables.isEmpty()) {
            for (Table table : tables.values()) {
                sb.append("### 表名：").append(table.getName()).append("\n");
                if (table.getComment() != null) {
                    sb.append("- 注释：").append(table.getComment()).append("\n");
                }

                // 字段信息
                sb.append("- 字段列表：\n");
                for (Column col : table.getColumns().values()) {
                    sb.append("  * `").append(col.getName()).append("` ")
                            .append("(").append(col.getTypeName()).append(")");

                    if (!col.isNullable()) {
                        sb.append(" NOT NULL");
                    }

                    if (col.getDefaultValue() != null) {
                        sb.append(" DEFAULT ").append(col.getDefaultValue());
                    }

                    if (col.getComment() != null) {
                        sb.append(" - ").append(col.getComment());
                    }
                    sb.append("\n");
                }

                // 主键信息
                if (table.getPrimaryKey() != null) {
                    sb.append("- 主键：").append(String.join(", ", table.getPrimaryKey().getColumns())).append("\n");
                }

                // 索引信息
                if (!table.getIndexs().isEmpty()) {
                    sb.append("- 索引：\n");
                    table.getIndexs().forEach((name, index) -> {
                        sb.append("  * ").append(name).append(": ")
                                .append(String.join(", ", index.getColumnNames())).append("\n");
                    });
                }
                sb.append("\n");
            }
        }
        return sb.toString();
    }


    /**
     * 生成简化的表结构信息字符串（只包含表名和表注释）
     */
    private String generateSimpleTableInfo(Schema schema) {
        StringBuilder sb = new StringBuilder();
        Map<String, Table> tables = schema.getTables();

        if (tables != null && !tables.isEmpty()) {
            for (Table table : tables.values()) {
                sb.append("- 表名：").append(table.getName());
                if (table.getComment() != null && !table.getComment().isEmpty()) {
                    sb.append("（").append(table.getComment()).append("）");
                }
                sb.append("\n");
            }
        }
        return sb.toString();
    }

    /**
     * 根据表名列表生成表结构信息
     *
     * @param schema 数据库模式信息
     * @param tableNames 表名列表
     * @return 指定表的结构信息字符串
     */
    public String getTableInfoByNames(Schema schema, List<String> tableNames) throws IOException {
        if (tableNames == null || tableNames.isEmpty()) {
            // 如果没有指定表名，返回所有表的简化信息
            return "null";
        }

        StringBuilder sb = new StringBuilder();
        Map<String, Table> tables = schema.getTables();

        if (tables != null && !tables.isEmpty()) {
            // 过滤出用户指定的表
            for (String tableName : tableNames) {
                Table table = tables.get(tableName);
                if (table != null) {
                    sb.append("### 表名：").append(table.getName()).append("\n");
                    if (table.getComment() != null) {
                        sb.append("- 注释：").append(table.getComment()).append("\n");
                    }

                    // 字段信息
                    sb.append("- 字段列表：\n");
                    for (Column col : table.getColumns().values()) {
                        sb.append("  * `").append(col.getName()).append("` ")
                                .append("(").append(col.getTypeName()).append(")");

                        if (!col.isNullable()) {
                            sb.append(" NOT NULL");
                        }

                        if (col.getDefaultValue() != null) {
                            sb.append(" DEFAULT ").append(col.getDefaultValue());
                        }

                        if (col.getComment() != null) {
                            sb.append(" - ").append(col.getComment());
                        }
                        sb.append("\n");
                    }

                    // 主键信息
                    if (table.getPrimaryKey() != null) {
                        sb.append("- 主键：").append(String.join(", ", table.getPrimaryKey().getColumns())).append("\n");
                    }

                    // 索引信息
                    if (!table.getIndexs().isEmpty()) {
                        sb.append("- 索引：\n");
                        table.getIndexs().forEach((name, index) -> {
                            sb.append("  * ").append(name).append(": ")
                                    .append(String.join(", ", index.getColumnNames())).append("\n");
                        });
                    }
                    sb.append("\n");
                } else {
                    sb.append("### 表名：").append(tableName).append(" (表不存在)\n\n");
                }
            }
        }

        return sb.toString();
    }

    /**
     * 获取DDL生成的提示词
     *
     * @param schema 数据库模式信息
     * @param tableNames 表名列表
     * @return DDL生成提示词
     * @throws IOException 如果读取模板文件失败
     */
    public String getDdlGenerationPrompt(Schema schema, List<String> tableNames) throws IOException {
        String template = new String(ddlGenerationPromptResource.getInputStream().readAllBytes(), StandardCharsets.UTF_8);

        // 根据表名获取表结构信息
        String tableInfo = getTableInfoByNames(schema, tableNames);

        DatabaseInfo dbInfo = schema.getDatabaseInfo();
        String schemaName = schema.getSchemaInfo().getSchemaName();
        return template
                .replace("${productName}", dbInfo.getProductName())
                .replace("${productVersion}", dbInfo.getProductVersion())
                .replace("${schemaName}", schemaName)
                .replace("${tableInfo}", tableInfo);
    }

    /**
     * 获取用于验证DDL的提示词
     *
     * @param schema 数据库模式信息
     * @param ddlSql 要验证的DDL SQL
     * @return 验证提示词
     * @throws IOException 如果读取模板文件失败
     */
    public String getDdlValidationPrompt(Schema schema, String ddlSql) throws IOException {
        String template = new String(ddlValidationPromptResource.getInputStream().readAllBytes(), StandardCharsets.UTF_8);

        DatabaseInfo dbInfo = schema.getDatabaseInfo();
        String schemaName = schema.getSchemaInfo().getSchemaName();
        return template
                .replace("${productName}", dbInfo.getProductName())
                .replace("${productVersion}", dbInfo.getProductVersion())
                .replace("${schemaName}", schemaName)
                .replace("${ddlSql}", ddlSql);
    }


}

