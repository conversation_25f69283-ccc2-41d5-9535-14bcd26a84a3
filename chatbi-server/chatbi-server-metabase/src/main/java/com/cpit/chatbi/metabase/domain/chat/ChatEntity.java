package com.cpit.chatbi.metabase.domain.chat;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ChatEntity {

    private String question;

    /** 会话id */
    private String chatId;

    /** 会话内容 */
    private String content;

    /** AI、人 */
    private String type;

    /** 创建时间 */
    private Date time;

    /** AI会话时，获取人对话ID */
    private String userChatId;
}
