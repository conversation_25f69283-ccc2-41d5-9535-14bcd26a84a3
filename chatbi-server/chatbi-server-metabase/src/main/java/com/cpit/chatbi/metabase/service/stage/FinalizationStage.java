package com.cpit.chatbi.metabase.service.stage;

import com.cpit.chatbi.metabase.domain.dto.stage.StageContext;
import lombok.extern.slf4j.Slf4j;
import org.springframework.ai.chat.client.ChatClient;

@Slf4j
class FinalizationStage extends AbstractDdlStage {
    public FinalizationStage(ChatClient chatClient) {
        super(chatClient, "final", "生成最终结果");
    }
    @Override protected String buildUserPrompt(StageContext context) {
        return "请对以下优化后的 SQL 进行评估，并提供最终的完整结果，包括 SQL 语句和执行说明：\n" + context.getPreviousStageOutput();
    }

}