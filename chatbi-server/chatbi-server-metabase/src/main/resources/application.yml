server:
  port: 8080

# Swagger 配置
springdoc:
  swagger-ui:
    path: /swagger-ui.html
    tags-sorter: alpha
    operations-sorter: alpha
  api-docs:
    path: /v3/api-docs
  group-configs:
    - group: 'default'
      paths-to-match: '/**'
      packages-to-scan: com.cpit.chatbi.metabase.controller

spring:
  application:
    name: metabase-erd
  datasource:
    driver-class-name: org.postgresql.Driver
    url: ******************************************************************** # 数据库URL
    username: postgres
    password: 123456

#  ai:
#    ollama:
#      base-url: http://localhost:11434
#      chat:
#        options:
#          model: gemma3:12b-it-qat
##          model: qwen2.5-coder:latest
##          model: qwen2.5:3b


#  ai:
#    ollama:
#      base-url: http://************:11434
#      chat:
#        options:
##          model: codeqwen:7b
#          model: deepseek-r1:70b


  ai:

#    openai:
#      base-url: https://api.deepseek.com
#      api-key: ***********************************
#      chat:
#        options:
#          model: deepseek-chat
#      embedding:
#        enabled: false


    # OpenAI配置 - 用于聊天模型
    openai:
      base-url: https://dashscope.aliyuncs.com/compatible-mode/
      api-key: sk-b09c403026064cd59b39b4908809af30
      chat:
        options:
          model: deepseek-v3
      # 禁用自动配置的嵌入模型
      embedding:
        enabled: false
        options:
          model: text-embedding-v1

    # Ollama配置 - 用于嵌入模型
    ollama:
      base-url: http://localhost:11434
      # 禁用自动配置的聊天模型
      chat:
        enabled: false
      # 禁用自动配置的嵌入模型（我们将使用手动配置）
      embedding:
        enabled: false
        options:
          model: mxbai-embed-large

    vectorstore:
      pgvector:
        index-type: HNSW
        distance-type: COSINE_DISTANCE
        dimensions: 1536
        table-name: vector_store
        initialize-schema: false
        batching-strategy: TOKEN_COUNT
        max-document-batch-size: 10000

  data:
    redis:
      host: ************
      port: 16378
      database: 4
      password: 16378 # 如果有密码，替换为实际密码
      timeout: 5000 # 连接超时时间，单位毫秒

#  data:
#    redis:
#      host: 127.0.0.1
#      port: 6379
#      database: 0
#      password:  # 如果有密码，替换为实际密码
#      timeout: 5000 # 连接超时时间，单位毫秒

# 向量库专用数据源配置
vector-datasource:
  url: ******************************************************************
  username: postgres
  password: 123456
  driver-class-name: org.postgresql.Driver
  # 向量库连接池配置
  hikari:
    maximum-pool-size: 10
    minimum-idle: 2
    connection-timeout: 30000
    idle-timeout: 600000
    max-lifetime: 1800000
