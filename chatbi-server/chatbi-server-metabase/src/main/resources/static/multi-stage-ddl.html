<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>多轮 AI 调用 DDL 生成示例</title>
    <!-- 添加 Markdown 解析库 -->
    <script src="https://cdn.jsdelivr.net/npm/marked/marked.min.js"></script>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        body {
            font-family: 'Microsoft YaHei', sans-serif;
            background-color: #f8f9fa;
            padding: 20px;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        .card {
            margin-bottom: 20px;
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        .card-header {
            font-weight: bold;
            background-color: #f1f8ff;
        }
        .stage-container {
            margin-bottom: 30px;
            border-left: 3px solid #ddd;
            padding-left: 20px;
        }
        .stage-header {
            font-weight: bold;
            margin-bottom: 10px;
            display: flex;
            align-items: center;
        }
        .stage-content {
            background-color: #fff;
            padding: 15px;
            border-radius: 5px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
            white-space: pre-wrap;
            font-family: 'Courier New', monospace;
        }

        /* Markdown 表格样式 */
        .stage-content table {
            border-collapse: collapse;
            width: 100%;
            margin-bottom: 1rem;
            font-family: 'Microsoft YaHei', sans-serif;
        }

        .stage-content th,
        .stage-content td {
            border: 1px solid #dee2e6;
            padding: 8px 12px;
            text-align: left;
            white-space: normal;
        }

        .stage-content th {
            background-color: #f8f9fa;
            font-weight: bold;
        }

        .stage-content tr:nth-child(even) {
            background-color: #f2f2f2;
        }
        .stage-analysis { border-left-color: #007bff; }
        .stage-analysis .stage-header { color: #007bff; }

        .stage-generation { border-left-color: #28a745; }
        .stage-generation .stage-header { color: #28a745; }

        .stage-validation { border-left-color: #fd7e14; }
        .stage-validation .stage-header { color: #fd7e14; }

        /* 兼容旧版本的样式名称 */
        .stage-optimization { border-left-color: #fd7e14; }
        .stage-optimization .stage-header { color: #fd7e14; }

        .stage-summary { border-left-color: #20c997; }
        .stage-summary .stage-header { color: #20c997; }

        .stage-sql { border-left-color: #dc3545; }
        .stage-sql .stage-header { color: #dc3545; }

        .stage-error { border-left-color: #dc3545; }
        .stage-error .stage-header { color: #dc3545; }

        .thinking-indicator {
            display: inline-block;
            margin-left: 10px;
        }
        .thinking-indicator span {
            display: inline-block;
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background-color: currentColor;
            margin-right: 3px;
            opacity: 0.6;
            animation: thinking 1.4s infinite ease-in-out both;
        }
        .thinking-indicator span:nth-child(1) { animation-delay: 0s; }
        .thinking-indicator span:nth-child(2) { animation-delay: 0.2s; }
        .thinking-indicator span:nth-child(3) { animation-delay: 0.4s; }

        @keyframes thinking {
            0%, 80%, 100% { transform: scale(0); }
            40% { transform: scale(1); }
        }

        .sql-block {
            background-color: #282c34;
            color: #abb2bf;
            padding: 15px;
            border-radius: 5px;
            overflow-x: auto;
        }

        .copy-btn {
            float: right;
            font-size: 0.8rem;
            padding: 2px 8px;
        }

        .execute-btn {
            margin-top: 10px;
        }

        .cursor {
            display: inline-block;
            width: 2px;
            height: 1em;
            background-color: currentColor;
            margin-left: 1px;
            animation: blink 1s infinite;
        }

        @keyframes blink {
            0%, 100% { opacity: 1; }
            50% { opacity: 0; }
        }

        .progress-container {
            margin: 20px 0;
        }

        .progress-step {
            display: flex;
            align-items: center;
            margin-bottom: 10px;
        }

        .progress-marker {
            width: 30px;
            height: 30px;
            border-radius: 50%;
            background-color: #e9ecef;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 10px;
            font-weight: bold;
            color: #495057;
        }

        .progress-marker.active {
            background-color: #007bff;
            color: white;
        }

        .progress-marker.completed {
            background-color: #28a745;
            color: white;
        }

        .progress-text {
            flex-grow: 1;
        }

        .progress-line {
            height: 20px;
            border-left: 2px dashed #dee2e6;
            margin-left: 15px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1 class="mb-4">多轮 AI 调用 DDL 生成示例</h1>

        <div class="card mb-4">
            <div class="card-header">输入需求</div>
            <div class="card-body">
                <div class="mb-3">
                    <label for="message" class="form-label">请输入您的 DDL 需求描述：</label>
                    <textarea class="form-control" id="message" rows="4" placeholder="例如：创建一个订单表，包含订单ID、用户ID、订单时间、订单金额和订单状态字段"></textarea>
                </div>
                <div class="row">
                    <div class="col-md-6 mb-3">
                        <label for="chatId" class="form-label">会话ID：</label>
                        <input type="text" class="form-control" id="chatId" value="">
                    </div>
                    <div class="col-md-6 mb-3">
                        <div class="form-check mt-4">
                            <input class="form-check-input" type="checkbox" id="isNewChat" checked>
                            <label class="form-check-label" for="isNewChat">
                                新会话
                            </label>
                        </div>
                    </div>
                </div>
                <button id="generateBtn" class="btn btn-primary">生成 DDL</button>
                <button id="testBtn" class="btn btn-outline-secondary ms-2">测试连接</button>
            </div>
        </div>

        <div class="progress-container d-none" id="progressContainer">
            <h4>生成进度</h4>
            <div class="progress-step">
                <div class="progress-marker" id="step1">1</div>
                <div class="progress-text">需求分析</div>
            </div>
            <div class="progress-line"></div>
            <div class="progress-step">
                <div class="progress-marker" id="step2">2</div>
                <div class="progress-text">SQL 生成</div>
            </div>
            <div class="progress-line"></div>
            <div class="progress-step">
                <div class="progress-marker" id="step3">3</div>
                <div class="progress-text">SQL 验证</div>
            </div>
        </div>

        <div id="resultContainer"></div>

        <div class="card d-none" id="sqlResultCard">
            <div class="card-header">最终 SQL</div>
            <div class="card-body">
                <button class="btn btn-sm btn-outline-secondary copy-btn" id="copyBtn">复制</button>
                <pre class="sql-block" id="sqlResult"></pre>
                <button class="btn btn-success execute-btn" id="executeBtn">执行 SQL</button>
                <div class="mt-3 d-none" id="executeResult"></div>
            </div>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // 生成随机会话ID
            document.getElementById('chatId').value = 'session_' + Math.random().toString(36).substring(2, 10);

            // 绑定生成按钮事件
            document.getElementById('generateBtn').addEventListener('click', generateDDL);

            // 绑定复制按钮事件
            document.getElementById('copyBtn').addEventListener('click', copySqlToClipboard);

            // 绑定执行按钮事件
            document.getElementById('executeBtn').addEventListener('click', executeSql);

            // 绑定测试按钮事件
            document.getElementById('testBtn').addEventListener('click', testConnection);
        });

        // 存储当前阶段的内容
        const stageContents = {
            analysis: '',
            generation: '',
            validation: '',
            summary: '',
            sql: ''
        };

        // 存储当前阶段的状态
        const stageStatus = {
            analysis: 'thinking',
            generation: 'thinking',
            validation: 'thinking',
            summary: 'thinking',
            sql: 'thinking'
        };

        // 生成DDL
        function generateDDL() {
            const message = document.getElementById('message').value.trim();
            const chatId = document.getElementById('chatId').value.trim();
            const isNewChat = document.getElementById('isNewChat').checked;

            if (!message) {
                alert('请输入需求描述');
                return;
            }

            if (!chatId) {
                alert('请输入会话ID');
                return;
            }

            // 清空结果容器
            document.getElementById('resultContainer').innerHTML = '';
            document.getElementById('sqlResult').textContent = '';
            document.getElementById('sqlResultCard').classList.add('d-none');
            document.getElementById('executeResult').classList.add('d-none');
            document.getElementById('executeResult').innerHTML = '';

            // 显示进度条
            document.getElementById('progressContainer').classList.remove('d-none');
            resetProgressSteps();

            // 禁用生成按钮
            const generateBtn = document.getElementById('generateBtn');
            generateBtn.disabled = true;
            generateBtn.textContent = '生成中...';

            // 重置阶段内容
            Object.keys(stageContents).forEach(key => stageContents[key] = '');

            // 创建EventSource
            // 使用与ChatResponse兼容的流式输出端点
            const url = `/api/chat/multi-stage/ddl-stream-chat?message=${encodeURIComponent(message)}&chatId=${encodeURIComponent(chatId)}&isNewChat=${isNewChat}`;
            console.log('Connecting to URL:', url);
            const eventSource = new EventSource(url);

            // 用于跟踪每个阶段的当前内容
            const stageContentsMap = {};
            // 记录当前活动阶段
            let currentStage = 'init';
            // 记录最后一次收到的SQL
            let lastSql = null;

            eventSource.onmessage = function(event) {
                try {
                    // 解析数据
                    const wrapper = JSON.parse(event.data);

                    // 处理ChatResponseWrapper格式的数据
                    if (wrapper.result && wrapper.result.output) {
                        // 从元数据中提取信息
                        const stage = wrapper.metadata.stage || 'init';
                        const incrementalContent = wrapper.result.output.text || '';
                        const status = wrapper.metadata.status || (wrapper.result.metadata.empty ? "thinking" : "complete");
                        const sql = wrapper.metadata.sql;

                        // 如果阶段发生变化，更新UI
                        if (stage !== currentStage) {
                            // 将上一个阶段标记为完成
                            if (currentStage !== 'init') {
                                updateStageStatus(currentStage, 'complete');
                            }
                            // 更新当前阶段
                            currentStage = stage;
                            // 更新进度指示器
                            updateProgressStep(stage);
                        }

                        // 如果是新阶段，初始化内容
                        if (!stageContentsMap[stage]) {
                            stageContentsMap[stage] = '';
                        }

                        // 如果有增量内容，累加到当前阶段
                        if (incrementalContent) {
                            stageContentsMap[stage] += incrementalContent;

                            // 构造兼容的数据格式
                            const compatData = {
                                stage: stage,
                                content: stageContentsMap[stage],
                                status: status,
                                data: sql
                            };

                            // 处理响应
                            processResponse(compatData);
                        }

                        // 如果有SQL数据并且与上次不同，更新SQL显示
                        if (sql && sql !== lastSql) {
                            lastSql = sql;
                            // 更新SQL显示
                            updateStageContainer('sql', {
                                stage: 'sql',
                                content: sql,
                                status: 'complete',
                                data: sql
                            });
                        }

                        // 处理完成状态
                        if (status === 'complete') {
                            updateStageStatus(stage, 'complete');
                        }

                        // 如果有finishReason并且是'stop'，表示整个流程结束
                        if (wrapper.result?.metadata?.finishReason === 'stop') {
                            // 在最后一个阶段完成时启用生成按钮
                            document.getElementById('generateBtn').disabled = false;
                            document.getElementById('generateBtn').textContent = '生成 DDL';
                        }
                    } else {
                        console.warn('Received unexpected data format:', wrapper);
                    }
                } catch (error) {
                    console.error('Error processing message:', error, event.data);
                }
            };

            eventSource.onerror = function(error) {
                console.error('EventSource error:', error);
                eventSource.close();

                // 启用生成按钮
                generateBtn.disabled = false;
                generateBtn.textContent = '生成 DDL';

                // 如果没有收到任何响应，显示错误信息
                if (document.getElementById('resultContainer').innerHTML === '') {
                    const errorDiv = document.createElement('div');
                    errorDiv.className = 'stage-container stage-error';
                    errorDiv.innerHTML = `
                        <div class="stage-header">错误</div>
                        <div class="stage-content">
                            连接服务器时发生错误，请稍后再试。<br>
                            请检查控制台日志以获取更多信息。<br>
                            如果您使用的是直接访问 HTML 文件，请确保通过服务器访问：<br>
                            <code>http://localhost:8080/ddl-generator</code>
                        </div>
                    `;
                    document.getElementById('resultContainer').appendChild(errorDiv);
                }
            };

            eventSource.onopen = function() {
                console.log('EventSource connection opened');
            };
        }

        // 从文本中提取阶段信息
        function extractStageFromText(text) {
            if (!text) return 'init';

            // 根据文本内容判断阶段
            if (text.includes('分析需求') || text.includes('新建表') || text.includes('匹配到表')) {
                return 'analysis';
            } else if (text.includes('生成 SQL') || text.includes('CREATE TABLE') || text.includes('ALTER TABLE')) {
                return 'generation';
            } else if (text.includes('验证 SQL') || text.includes('验证结果')) {
                return 'validation';
            } else if (text.match(/^(CREATE|ALTER|DROP)\s+TABLE/i)) {
                return 'sql';
            } else {
                return 'init';
            }
        }

        // 处理响应
        function processResponse(data) {
            const stage = data.stage;
            const content = data.content;
            const status = data.status;
            const sqlData = data.data;

            // 更新进度步骤
            updateProgressStep(stage);

            // 更新或创建阶段容器
            updateStageContainer(stage, content, status);

            // 更新阶段状态
            updateStageStatus(stage, status);

            // 如果是SQL阶段或有SQL数据，显示SQL结果
            if ((stage === 'sql' && status === 'complete') || sqlData) {
                const sqlContent = sqlData || content;
                document.getElementById('sqlResult').textContent = sqlContent;
                document.getElementById('sqlResultCard').classList.remove('d-none');
            }

            // 注意：生成按钮的启用现在由finishReason='stop'控制
            // 这里不再基于阶段来启用按钮
        }

        // 更新阶段容器
        function updateStageContainer(stage, content, status) {
            // 更新阶段状态
            stageStatus[stage] = status;

            // 查找或创建阶段容器
            let stageContainer = document.getElementById(`stage-${stage}`);
            if (!stageContainer) {
                stageContainer = document.createElement('div');
                stageContainer.id = `stage-${stage}`;
                stageContainer.className = `stage-container stage-${stage}`;

                let stageTitle = '';
                switch(stage) {
                    case 'init': stageTitle = '初始化'; break;
                    case 'analysis': stageTitle = '需求分析'; break;
                    case 'generation': stageTitle = 'SQL 生成'; break;
                    case 'validation': stageTitle = 'SQL 验证'; break;
                    case 'summary': stageTitle = '总结'; break;
                    case 'sql': stageTitle = '提取的 SQL'; break;
                    case 'error': stageTitle = '错误'; break;
                    default: stageTitle = stage;
                }

                stageContainer.innerHTML = `
                    <div class="stage-header">
                        ${stageTitle}
                        <div class="thinking-indicator" id="thinking-${stage}">
                            <span></span><span></span><span></span>
                        </div>
                    </div>
                    <div class="stage-content" id="content-${stage}"></div>
                `;

                document.getElementById('resultContainer').appendChild(stageContainer);
                stageContents[stage] = '';
            }

            // 更新内容 - 对于完整的响应，直接设置内容
            if (status === 'complete' || stage === 'sql' || stage === 'error') {
                stageContents[stage] = content;
            }
            // 对于流式输出，逐步添加内容
            else if (status === 'thinking' && content.length > stageContents[stage].length) {
                stageContents[stage] = content;
            }

            // 更新显示的内容
            const contentElement = document.getElementById(`content-${stage}`);
            // 使用 marked.js 解析 Markdown 内容
            if (stage === 'analysis' || stage === 'generation' || stage === 'validation' || stage === 'sql') {
                // 分析阶段、生成阶段和验证阶段使用 Markdown 渲染
                contentElement.innerHTML = marked.parse(stageContents[stage]);

                // 找到所有代码块并高亮显示
                const codeBlocks = contentElement.querySelectorAll('pre code');
                codeBlocks.forEach(block => {
                    // 添加类名以便于样式化
                    block.parentNode.classList.add('sql-block');
                });
            } else {
                // 其他阶段保持原样
                contentElement.textContent = stageContents[stage];
            }

            // 更新思考指示器
            const thinkingIndicator = document.getElementById(`thinking-${stage}`);
            if (status === 'thinking') {
                thinkingIndicator.style.display = 'inline-block';
            } else {
                thinkingIndicator.style.display = 'none';
            }

            // 如果是错误状态，添加错误样式
            if (status === 'error') {
                stageContainer.classList.add('stage-error');
            }
        }

        // 更新进度步骤
        function updateProgressStep(stage) {
            const step1 = document.getElementById('step1');
            const step2 = document.getElementById('step2');
            const step3 = document.getElementById('step3');

            switch(stage) {
                case 'init':
                    step1.className = 'progress-marker';
                    step2.className = 'progress-marker';
                    step3.className = 'progress-marker';
                    break;
                case 'analysis':
                    step1.className = 'progress-marker active';
                    break;
                case 'generation':
                    step1.className = 'progress-marker completed';
                    step2.className = 'progress-marker active';
                    break;
                case 'validation':
                case 'summary':
                case 'sql':
                    step1.className = 'progress-marker completed';
                    step2.className = 'progress-marker completed';
                    step3.className = 'progress-marker active';
                    break;
            }
        }

        // 重置进度步骤
        function resetProgressSteps() {
            document.getElementById('step1').className = 'progress-marker';
            document.getElementById('step2').className = 'progress-marker';
            document.getElementById('step3').className = 'progress-marker';
        }

        // 更新阶段状态
        function updateStageStatus(stage, status) {
            // 更新阶段状态
            stageStatus[stage] = status;

            const stageContainer = document.getElementById(`stage-${stage}`);
            if (!stageContainer) return;

            // 更新思考指示器
            const thinkingIndicator = document.getElementById(`thinking-${stage}`);
            if (thinkingIndicator) {
                if (status === 'thinking') {
                    thinkingIndicator.style.display = 'inline-block';
                } else {
                    thinkingIndicator.style.display = 'none';
                }
            }

            // 如果是错误状态，添加错误样式
            if (status === 'error') {
                stageContainer.classList.add('stage-error');
            } else {
                stageContainer.classList.remove('stage-error');
            }

            // 如果是完成状态，添加完成样式
            if (status === 'complete') {
                stageContainer.classList.add('stage-complete');
            } else {
                stageContainer.classList.remove('stage-complete');
            }
        }

        // 复制SQL到剪贴板
        function copySqlToClipboard() {
            const sqlText = document.getElementById('sqlResult').textContent;
            navigator.clipboard.writeText(sqlText).then(function() {
                const copyBtn = document.getElementById('copyBtn');
                copyBtn.textContent = '已复制';
                setTimeout(() => {
                    copyBtn.textContent = '复制';
                }, 2000);
            }, function(err) {
                console.error('无法复制文本: ', err);
            });
        }

        // 执行SQL
        function executeSql() {
            const sql = document.getElementById('sqlResult').textContent;
            const executeBtn = document.getElementById('executeBtn');
            const executeResult = document.getElementById('executeResult');

            executeBtn.disabled = true;
            executeBtn.textContent = '执行中...';
            executeResult.innerHTML = '<div class="alert alert-info">正在执行SQL...</div>';
            executeResult.classList.remove('d-none');

            fetch('/api/meta/execute-sql', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ sql: sql })
            })
            .then(response => response.json())
            .then(data => {
                executeBtn.disabled = false;
                executeBtn.textContent = '执行 SQL';

                if (data.code === 0) {
                    executeResult.innerHTML = `<div class="alert alert-success">SQL执行成功: ${data.data || data.message}</div>`;
                } else {
                    executeResult.innerHTML = `<div class="alert alert-danger">SQL执行失败: ${data.message}</div>`;
                }
            })
            .catch(error => {
                executeBtn.disabled = false;
                executeBtn.textContent = '执行 SQL';
                executeResult.innerHTML = `<div class="alert alert-danger">请求失败: ${error.message}</div>`;
                console.error('Error:', error);
            });
        }

        // 测试连接
        function testConnection() {
            const testBtn = document.getElementById('testBtn');
            testBtn.disabled = true;
            testBtn.textContent = '测试中...';

            fetch('/api/chat/test')
                .then(response => response.text())
                .then(data => {
                    alert('连接成功\n\n响应: ' + data);
                    console.log('Test response:', data);
                })
                .catch(error => {
                    alert('连接失败\n\n错误: ' + error);
                    console.error('Test error:', error);
                })
                .finally(() => {
                    testBtn.disabled = false;
                    testBtn.textContent = '测试连接';
                });
        }
    </script>
</body>
</html>
