FROM eclipse-temurin:21-jdk-alpine
LABEL maintainer="chatbi"

RUN ln -sf /usr/share/zoneinfo/Asia/Shanghai /etc/localtime
RUN mkdir -p /chatbi/drivers /chatbi/logs
WORKDIR /chatbi

COPY --chmod=755 drivers/ ./drivers/
COPY --chmod=755 chatbi-server-start/target/chatbi-server-start.jar ./chatbi.jar

ENV CHATBI_DRIVER_PATH=/chatbi/drivers/
EXPOSE 18080

CMD ["java", "-Dfile.encoding=utf-8", "-Duser.timezone=GMT+08", "-Dserver.port=18080", "-jar", "chatbi.jar"]
