# Configuration generated by easy-yapi plug-in
# Convert date to long
json.rule.convert[java.util.Date]=java.lang.Long
json.rule.convert[java.sql.Timestamp]=java.lang.Long
json.rule.convert[java.time.LocalDateTime]=java.lang.Long
json.rule.convert[java.time.LocalDate]=java.lang.Long

# Use version to modify tags
api.tag=#version

# ignore serialVersionUID
constant.field.ignore=groovy:it.name()=="serialVersionUID"

# sprnSupport for Jackson annotations
json.rule.field.ignore=@com.fasterxml.jackson.annotation.JsonIgnore#value
